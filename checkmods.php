<?php
/**
 * Module Checker Script
 * 
 * Usage: 
 *   php checkmods.php <module_name>     - Check if specific module is available
 *   php checkmods.php -a                - List all available modules
 * 
 * Examples: 
 *   php checkmods.php spm
 *   php checkmods.php -a
 * 
 * Returns: 
 *   - true/false for specific module check
 *   - List of all modules when using -a flag
 */

// Check if any parameter is provided
if ($argc < 2) {
    echo "Usage:\n";
    echo "  php checkmods.php <module_name>     - Check if specific module is available\n";
    echo "  php checkmods.php -a                - List all available modules\n";
    echo "\nExamples:\n";
    echo "  php checkmods.php spm\n";
    echo "  php checkmods.php -a\n";
    exit(1);
}

$first_arg = trim($argv[1]);

// Check if user wants to list all modules
if ($first_arg === '-a') {
    $available_modules = get_available_modules();
    
    if (empty($available_modules)) {
        echo "No modules available or unable to retrieve module list.\n";
        exit(1);
    }
    
    // Output all available modules
    echo implode("\n", $available_modules);
    echo "\n";
    exit(0);
}

// Original functionality for checking specific module
$requested_module = strtoupper(trim($argv[1]));

/**
 * Executes a shell command and returns the trimmed output.
 * Returns null if the command fails or returns no output.
 */
function run_command($command) {
    // Basic security check
    if (strpbrk($command, ';|&`') !== false && php_sapi_name() != 'cli') {
        return null;
    }
    $output = shell_exec($command . ' 2>&1');
    return $output ? trim($output) : null;
}

/**
 * Gets all available modules from binit
 */
function get_available_modules() {
    $modules_raw = run_command('binit -modules');
    if (!$modules_raw) {
        return [];
    }
    
    // Parse modules from format like [SPM,APM,NET] or similar
    if (preg_match('/^\[(.*)\]$/', $modules_raw, $matches)) {
        $modules_str = $matches[1];
        // Split by comma and clean up each module name
        $modules = array_map(function($module) {
            return strtoupper(trim($module));
        }, explode(',', $modules_str));
        
        return array_filter($modules); // Remove empty entries
    }
    
    return [];
}

// Get available modules
$available_modules = get_available_modules();

// Check if requested module is available
$is_available = in_array($requested_module, $available_modules);

// Output result
echo $is_available ? "true" : "false";
echo "\n";

// Exit with appropriate code
exit($is_available ? 0 : 1);
?>
