<?php
include "loadenv.php";
$servername = $_ENV["DB_SERVER"];
$username = $_ENV["DB_USER"];
$password = $_ENV["DB_PASSWORD"]; 
$dbname = "bubblemaps";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$id = $_POST['id'];
$newName = $_POST['newName'];

// Get the old name
$sql = "SELECT hostname FROM hosts WHERE id = ?";
$stmt = $conn->prepare($sql);
if ($stmt === false) {
    die("Prepare failed: " . $conn->error);
}

$stmt->bind_param('i', $id);
if (!$stmt->execute()) {
    die("Execute failed: " . $stmt->error);
}

$stmt->bind_result($oldName);
if (!$stmt->fetch()) {
    die("No record found with the given ID.");
}

$stmt->close();

// Update table
$sql = "UPDATE hosts SET hostname = ? WHERE id = ?";
$stmt = $conn->prepare($sql);
if ($stmt === false) {
    die("Prepare failed: " . $conn->error);
}

$stmt->bind_param('si', $newName, $id);
if (!$stmt->execute()) {
    die("Execute failed: " . $stmt->error);
}

echo "Success";

$stmt->close();
$conn->close();
?>
