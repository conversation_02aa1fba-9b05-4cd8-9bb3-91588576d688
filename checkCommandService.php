<?php
include "loadenv.php";
include 'theme_loader.php'; // Include the theme loader
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection
$db_host = $_ENV["DB_SERVER"];
$db_user = $_ENV["DB_USER"];
$db_pass = $_ENV["DB_PASSWORD"];
$db_name = 'db_nagiosql_v3';
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Prevent caching
header("Cache-Control: no-cache, must-revalidate");

// Fetch parameters
$servicename = $_GET['servicename'] ?? '';
$hostname = $_GET['hostname'] ?? '';
$service_id = null;
$check_command = '';
$command_id = null;
$command_args = array_fill(0, 8, ''); // Array for up to 8 arguments
$command_line = '';
$commands = []; // Array to store all commands from tbl_command

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function simulateVerifyActions($ip) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
        ]);

        // Login to Nagios
        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception('Login failed: ' . curl_error($ch));
        }
        error_log("Login response code: " . curl_getinfo($ch, CURLINFO_HTTP_CODE));

        // Handle redirect
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            error_log("Redirecting to: $redirect");
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        // Perform verification actions
        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                throw new Exception("Action $action failed: " . curl_error($ch));
            }
            error_log("Action $action completed with response code: " . curl_getinfo($ch, CURLINFO_HTTP_CODE));
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log("Error in simulateVerifyActions: " . $e->getMessage());
    }
}

// Fetch all commands from tbl_command
$sql = "SELECT id, command_name, command_line FROM tbl_command ORDER BY command_name ASC";
$result = $conn->query($sql);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $commands[$row['id']] = [
            'command_name' => $row['command_name'],
            'command_line' => $row['command_line']
        ];
    }
} else {
    die("Failed to fetch commands: " . $conn->error);
}

if (!empty($servicename) && !empty($hostname)) {
    // Step 1: Get host ID
    $sql = "SELECT id FROM tbl_host WHERE host_name = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        die("Prepare failed: " . $conn->error);
    }
    $stmt->bind_param("s", $hostname);
    $stmt->execute();
    $result = $stmt->get_result();
    $host = $result->fetch_assoc();
    $stmt->close();

    if ($host) {
        $host_id = $host['id'];

        // Step 2: Get service ID and check_command
        $sql = "SELECT id, check_command FROM tbl_service WHERE service_description = ? AND id IN (SELECT idMaster FROM tbl_lnkServiceToHost WHERE idSlave = ?)";
        $stmt = $conn->prepare($sql);
        if ($stmt === false) {
            die("Prepare failed: " . $conn->error);
        }
        $stmt->bind_param("si", $servicename, $host_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $service_data = $result->fetch_assoc();
        $stmt->close();

        if ($service_data) {
            $service_id = $service_data['id'];
            $check_command = $service_data['check_command'] ?? '';

            // Parse check_command
            if (!empty($check_command)) {
                $parts = explode('!', $check_command);
                $command_id = (int)($parts[0] ?? 0); // First part is the command ID
                for ($i = 1; $i <= 8 && $i < count($parts); $i++) {
                    $command_args[$i - 1] = $parts[$i]; // Populate args (0-based index)
                }
            }

            // Set command_line based on command_id
            if ($command_id && isset($commands[$command_id])) {
                $command_line = $commands[$command_id]['command_line'];
            }
        }
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $service_id) {
    $new_command_id = (int)($_POST['command_id'] ?? $command_id); // Use selected command ID
    $new_args = [];
    for ($i = 0; $i < 8; $i++) {
        $arg = $_POST["arg$i"] ?? '';
        if ($arg !== '') {
            $new_args[] = $arg;
        }
    }
    $new_check_command = $new_command_id . (!empty($new_args) ? '!' . implode('!', $new_args) : '');

    // Update the check_command in tbl_service
    $sql = "UPDATE tbl_service SET check_command = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        die("Prepare failed: " . $conn->error);
    }
    $stmt->bind_param("si", $new_check_command, $service_id);
    if (!$stmt->execute()) {
        die("Execute failed: " . $stmt->error);
    }
    $stmt->close();
    simulateVerifyActions(getSelfIp());
    // Refresh data after update
    $sql = "SELECT check_command FROM tbl_service WHERE id = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        die("Prepare failed: " . $conn->error);
    }
    $stmt->bind_param("i", $service_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $service_data = $result->fetch_assoc();
    $stmt->close();

    if ($service_data) {
        $check_command = $service_data['check_command'] ?? '';
        $parts = explode('!', $check_command);
        $command_id = (int)($parts[0] ?? 0);
        $command_args = array_fill(0, 8, '');
        for ($i = 1; $i <= 8 && $i < count($parts); $i++) {
            $command_args[$i - 1] = $parts[$i];
        }
        if ($command_id && isset($commands[$command_id])) {
            $command_line = $commands[$command_id]['command_line'];
        }
    }

    echo '<script>';
    echo 'if (window.self !== window.top) {';
    echo '  window.parent.location.reload();';
    echo '} else {';
    echo '  window.location.reload();';
    echo '}';
    echo '</script>';
    exit;
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($servicename ?: 'No Service'); ?> on <?php echo htmlspecialchars($hostname ?: 'No Host'); ?></title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/advancedCommands.css">
</head>
<body>
    <div class="container">
        <h1><?php echo htmlspecialchars($servicename ?: 'No Service'); ?> on <?php echo htmlspecialchars($hostname ?: 'No Host'); ?></h1>

        <?php if (empty($service_id) && (!empty($servicename) || !empty($hostname))): ?>
            <p class="no-data">No data found for service: <?php echo htmlspecialchars($servicename); ?> on host: <?php echo htmlspecialchars($hostname); ?></p>
        <?php endif; ?>

        <form method="POST">
            <div class="form-section">
                <h2>Command Settings</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Command</label>
                        <select name="command_id" id="command-select">
                            <option value="">-- Select a Command --</option>
                            <?php foreach ($commands as $id => $command): ?>
                                <option value="<?php echo $id; ?>" <?php echo $id === $command_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($command['command_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Command Line</label>
                        <input type="text" id="command-line" value="<?php echo htmlspecialchars($command_line); ?>" disabled>
                    </div>
                </div>
                <div class="form-grid">
                    <?php for ($i = 0; $i < 8; $i++): ?>
                        <div class="form-group">
                            <label>$ARG<?php echo $i + 1; ?>$</label>
                            <input type="text" name="arg<?php echo $i; ?>" class="arg-input" value="<?php echo htmlspecialchars($command_args[$i]); ?>">
                        </div>
                    <?php endfor; ?>
                </div>
            </div>

            <button type="submit" id="save-button" class="save-button">Save Changes</button>
        </form>
    </div>

    <script>
        const commands = <?php echo json_encode($commands); ?>;
        const commandSelect = document.getElementById('command-select');
        const commandLineInput = document.getElementById('command-line');
        const argInputs = document.querySelectorAll('.arg-input');

        commandSelect.addEventListener('change', function() {
            const selectedId = this.value;
            if (selectedId && commands[selectedId]) {
                commandLineInput.value = commands[selectedId].command_line;
            } else {
                commandLineInput.value = '';
            }
            // Clear all argument fields
            argInputs.forEach(input => {
                input.value = '';
            });
        });

        document.getElementById('save-button').addEventListener('click', function() {
            this.textContent = 'Saving...';
        });
    </script>
</body>
</html>