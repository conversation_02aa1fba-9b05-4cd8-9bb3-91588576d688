<?php
include "loadenv.php";
$host = $_ENV["DB_SERVER"];
$dbname = 'bubblemaps';
$username = $_ENV["DB_USER"];
$password = $_ENV["DB_PASSWORD"];

try {
    // Create database connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get the infra value from GET parameter
    $get_infra = $_GET['infra'] ?? ''; // Using null coalescing operator for safety
    
    // Prepare and execute the query
    $stmt = $pdo->prepare("SELECT ip FROM hosts WHERE infra = :infra");
    $stmt->bindParam(':infra', $get_infra);
    $stmt->execute();
    
    // Fetch results
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Output results
    if ($results) {
        foreach ($results as $row) {
            echo $row['ip'] . "\n";
        }
    } else {
        echo "No IP addresses found for infrastructure: " . htmlspecialchars($get_infra);
    }

} catch (PDOException $e) {
    // Handle database errors
    echo "Error: " . $e->getMessage();
}

// Close connection
$pdo = null;
?>