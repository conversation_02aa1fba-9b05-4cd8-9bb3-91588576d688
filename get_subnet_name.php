<?php
include "loadenv.php";

class SubnetFetcher {
    private $conn;
    private $errors = [];
    private const DB_NAME = "bubblemaps";
    private const SCAN_TIMEOUT = 30;

    public function __construct() {
        $this->initDatabaseConnection();
    }

    private function initDatabaseConnection(): void {
        $this->conn = new mysqli($_ENV["DB_SERVER"], $_ENV["DB_USER"], $_ENV["DB_PASSWORD"], self::DB_NAME);
        if ($this->conn->connect_error) {
            $this->handleFatalError("Connection failed: " . $this->conn->connect_error);
        }
    }

    public function fetchSubnets(): void {
        $infra = $this->getInfraParameter();
        $infraData = $this->querySubnets($infra);

        if (empty($infraData)) {
            $infraData = $this->scanAndRetry($infra);
        }

        $this->sendResponse($infraData);
    }

    private function getInfraParameter(): string {
        if (!isset($_GET['infra'])) {
            $this->handleFatalError("Error: 'infra' parameter is required.");
        }
        return $this->conn->real_escape_string($_GET['infra']);
    }

    private function querySubnets(string $infra): array {
        // Select subnets from the specified infra that have at least one host not blacklisted and not in "ask" status
        $sql = "SELECT s.*
                FROM subnets s
                WHERE s.infra = '$infra'
                  AND EXISTS (
                    SELECT 1
                    FROM hosts h
                    WHERE h.subnet = s.subnet
                      AND h.infra = s.infra
                      AND h.blacklist = 0
                      AND h.apmStatus != 'ask'
                  )";
        $result = $this->conn->query($sql);

        if (!$result) {
            $this->errors[] = "Query failed: " . $this->conn->error;
            return [];
        }

        return $this->fetchResults($result);
    }

    private function fetchResults(?mysqli_result $result): array {
        $data = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
        }
        return $data;
    }

    private function scanAndRetry(string $infra): array {
        $selfIP = $this->getSelfIp();
        $credentials = $this->getAdminCredentials();
        
        $this->executeScan($selfIP, $infra, $credentials);
        return $this->querySubnets($infra);
    }

    private function getSelfIp(): string {
        $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
        if ($ip === null) {
            $this->handleFatalError("Error: Unable to retrieve IP address.");
        }
        return trim($ip);
    }

    private function getAdminCredentials(): array {
        $conn = $this->getAdminDatabaseConnection();
        
        $sql = "SELECT username, password FROM users WHERE user_id = 1";
        $result = $conn->query($sql);
        
        $credentials = $this->fetchCredentials($result);
        $conn->close();
        
        return $credentials ?? [];
    }

    private function getAdminDatabaseConnection(): mysqli {
        $conn = new mysqli($_ENV["DB_SERVER"], $_ENV["DB_USER"], $_ENV["DB_PASSWORD"], "blesk");
        if ($conn->connect_error) {
            $this->handleFatalError("Connection failed: " . $conn->connect_error);
        }
        return $conn;
    }

    private function fetchCredentials(?mysqli_result $result): ?array {
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return [
                "username" => $row["username"],
                "password" => $row["password"]
            ];
        }
        return null;
    }

    private function executeScan(string $selfIP, string $infra, array $credentials): void {
        $scanUrl = "https://$selfIP/bubblemaps/scan.php?infra=" . urlencode($infra);
        $ch = $this->initCurl($scanUrl, $credentials);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        $this->handleCurlResponse($ch, $response, $httpCode);
        curl_close($ch);
    }

    private function initCurl(string $url, array $credentials): CurlHandle {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => self::SCAN_TIMEOUT,
            CURLOPT_USERPWD => "{$credentials['username']}:{$credentials['password']}",
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTPHEADER => ['Content-Type: application/json']
        ]);
        return $ch;
    }

    private function handleCurlResponse(CurlHandle $ch, $response, int $httpCode): void {
        if (curl_errno($ch)) {
            $this->errors[] = "cURL error: " . curl_error($ch);
        } elseif ($httpCode !== 200) {
            $this->errors[] = "scan.php returned HTTP status code: $httpCode";
        } else {
            error_log("scan.php response: " . $response);
        }
        
        if ($this->errors && empty($this->fetchResults($this->conn->query("SELECT * FROM subnets WHERE infra = '{$_GET['infra']}'")))) {
            $this->errors[] = "No data found even after calling scan.php.";
        }
    }

    private function sendResponse(array $infraData): void {
        header('Content-Type: application/json');
        echo json_encode([
            "data" => $infraData,
            "errors" => $this->errors
        ]);
        $this->conn->close();
    }

    private function handleFatalError(string $message): void {
        error_log($message);
        header('Content-Type: application/json');
        echo json_encode(["errors" => [$message]]);
        exit;
    }
}

$fetcher = new SubnetFetcher();
$fetcher->fetchSubnets();