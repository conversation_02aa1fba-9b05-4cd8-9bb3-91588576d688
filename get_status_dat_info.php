<?php
/**
 * <PERSON><PERSON>t to read plugin_output and long_plugin_output from Nagios status.dat file
 * for a specific host and service when this information isn't available via the API
 */

// Ensure no output buffering to prevent corruption
@ob_end_clean();
if (ob_get_level()) @ob_end_clean();

// Set content type to JSON
header('Content-Type: application/json');

// Function to safely output JSON and exit
function outputJson($data) {
    // Ensure all strings are valid UTF-8 to prevent JSON encoding issues
    array_walk_recursive($data, function(&$item) {
        if (is_string($item)) {
            // Replace invalid UTF-8 characters
            $item = mb_convert_encoding($item, 'UTF-8', 'UTF-8');
            // Remove null bytes which can break JSON
            $item = str_replace("\0", "", $item);
        }
    });
    
    // Encode with options to handle potential issues
    $json = json_encode($data, JSON_PARTIAL_OUTPUT_ON_ERROR | JSON_UNESCAPED_SLASHES);
    
    // Check if encoding failed
    if ($json === false) {
        // Fallback with just the error message
        echo '{"error":"JSON encoding failed: ' . json_last_error_msg() . '"}';
    } else {
        echo $json;
    }
    exit;
}

try {
    // Get parameters from request
    $hostname = isset($_GET['hostname']) ? $_GET['hostname'] : '';
    $type = isset($_GET['type']) ? $_GET['type'] : 'service';
    $serviceDescription = isset($_GET['serviceDescription']) ? $_GET['serviceDescription'] : '';

    // Validate parameters
    if (empty($hostname)) {
        outputJson(['error' => 'Missing required parameter: hostname']);
    }
    
    if ($type === 'service' && empty($serviceDescription)) {
        outputJson(['error' => 'Missing required parameter: serviceDescription for service type requests']);
    }

    // Debug information
    $debug = [];
    $debug['type'] = $type;
    $debug['hostname'] = $hostname;
    if ($type === 'service') {
        $debug['serviceDescription'] = $serviceDescription;
    }

    // Path to Nagios status.dat file
    $statusDatPath = '/var/spool/nagios/status.dat';
    $debug['status_file_path'] = $statusDatPath;
    $debug['file_exists'] = file_exists($statusDatPath) ? 'yes' : 'no';
    $debug['file_readable'] = is_readable($statusDatPath) ? 'yes' : 'no';
    
    // Check if file exists and is readable
    if (!file_exists($statusDatPath)) {
        outputJson([
            'error' => 'Status.dat file does not exist',
            'debug' => $debug
        ]);
    }
    
    if (!is_readable($statusDatPath)) {
        outputJson([
            'error' => 'Status.dat file is not readable',
            'debug' => $debug
        ]);
    }

    // Basic system information
    $debug['php_version'] = phpversion();
    $debug['memory_limit'] = ini_get('memory_limit');
    $debug['max_execution_time'] = ini_get('max_execution_time');
    
    // Use SplFileObject for memory-efficient file reading
    $file = new SplFileObject($statusDatPath);
    $file->setFlags(SplFileObject::DROP_NEW_LINE | SplFileObject::READ_AHEAD | SplFileObject::SKIP_EMPTY);
    
    // New 'fullstatus' type to get all data for a host in one go
    if ($type === 'fullstatus') {
        $hostStatus = null;
        $serviceStatus = [];

        $inHostBlock = false;
        $currentHostData = [];
        $inServiceBlock = false;
        $currentServiceData = [];

        foreach ($file as $line) {
            $line = trim($line);

            // Host block processing
            if ($line === 'hoststatus {') {
                $inHostBlock = true;
                $currentHostData = [];
                continue;
            }
            if ($inHostBlock && $line === '}') {
                $inHostBlock = false;
                if (isset($currentHostData['host_name']) && $currentHostData['host_name'] === $hostname) {
                    $hostStatus = $currentHostData;
                }
                continue;
            }
            if ($inHostBlock) {
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $currentHostData[trim($key)] = $value;
                }
                continue;
            }

            // Service block processing
            if ($line === 'servicestatus {') {
                $inServiceBlock = true;
                $currentServiceData = [];
                continue;
            }
            if ($inServiceBlock && $line === '}') {
                $inServiceBlock = false;
                if (isset($currentServiceData['host_name']) && $currentServiceData['host_name'] === $hostname) {
                    $serviceStatus[] = $currentServiceData;
                }
                continue;
            }
            if ($inServiceBlock) {
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $currentServiceData[trim($key)] = $value;
                }
            }
        }

        if ($hostStatus !== null) {
            outputJson([
                'hoststatus' => $hostStatus,
                'servicestatus' => $serviceStatus,
                'debug' => $debug
            ]);
        } else {
            outputJson([
                'error' => 'Host not found in status.dat',
                'debug' => $debug
            ]);
        }
    }
    
    // Initialize result
    $result = [
        'debug' => $debug
    ];
    
    $found = false;
    
    if ($type === 'service') {
        // PROCESS FOR SERVICE PLUGIN OUTPUT
        $inServiceBlock = false;
        $correctHost = false;
        $correctService = false;
        
        foreach ($file as $line) {
            $line = trim($line);
            
            // Start of a service block
            if ($line === 'servicestatus {') {
                $inServiceBlock = true;
                $correctHost = false;
                $correctService = false;
                continue;
            }
            
            // End of a service block
            if ($inServiceBlock && $line === '}') {
                if ($correctHost && $correctService) {
                    $found = true;
                    break; // We found what we need, stop processing
                }
                $inServiceBlock = false;
                continue;
            }
            
            // Inside a service block, check for host and service matches
            if ($inServiceBlock) {
                // Check host name
                if (strpos($line, 'host_name=') === 0) {
                    $thisHost = substr($line, strlen('host_name='));
                    $correctHost = ($thisHost === $hostname);
                }
                
                // Check service description
                if (strpos($line, 'service_description=') === 0) {
                    $thisService = substr($line, strlen('service_description='));
                    $correctService = ($thisService === $serviceDescription);
                }
                
                // If we have the right host and service, capture the outputs
                if ($correctHost && $correctService) {
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $result[trim($key)] = $value;
                    }
                }
            }
        }
    } else {
        // PROCESS FOR HOST PLUGIN OUTPUT
        $inHostBlock = false;
        $correctHost = false;
        
        foreach ($file as $line) {
            $line = trim($line);
            
            // Start of a host block
            if ($line === 'hoststatus {') {
                $inHostBlock = true;
                $correctHost = false;
                continue;
            }
            
            // End of a host block
            if ($inHostBlock && $line === '}') {
                if ($correctHost) {
                    $found = true;
                    break; // We found what we need, stop processing
                }
                $inHostBlock = false;
                continue;
            }
            
            // Inside a host block, check for host match
            if ($inHostBlock) {
                // Check host name
                if (strpos($line, 'host_name=') === 0) {
                    $thisHost = substr($line, strlen('host_name='));
                    $correctHost = ($thisHost === $hostname);
                }
                
                // If we have the right host, capture the outputs
                if ($correctHost) {
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $result[trim($key)] = $value;
                    }
                }
            }
        }
    }
    
    // Return result
    if ($found) {
        outputJson($result);
    } else {
        $result['error'] = ($type === 'service') ? 
            'Service not found in status.dat' : 
            'Host not found in status.dat';
        outputJson($result);
    }
    
} catch (Exception $e) {
    outputJson([
        'error' => 'Exception: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
} catch (Error $e) {
    outputJson([
        'error' => 'PHP Error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}

// This line should never be reached, but just in case
echo '{"error":"Script reached end without returning valid data"}';
exit; 