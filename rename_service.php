<?php
include_once "loadenv.php";

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function getDatabaseConnectionNagiosql() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "db_nagiosql_v3";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function simulateVerifyActions($hostname) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        // Initialize cURL session with cookies
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        // Login to Nagios
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => $_ENV['NAGIOS_USER'],
            'tfPassword' => $_ENV['NAGIOS_PASS'],
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Login failed: ' . curl_error($ch));

        // Handle redirect
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        // Perform verification actions
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if(curl_errno($ch)) throw new Exception("Action $action failed");
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log($e->getMessage());
    }
}

function renameService($ip, $currentServiceName, $newServiceName) {
    try {
        $nagiosConn = getDatabaseConnectionNagiosql();
        
        // 1. Get host ID from tbl_host where address matches the IP
        $hostQuery = "SELECT id FROM tbl_host WHERE address = ?";
        $hostStmt = $nagiosConn->prepare($hostQuery);
        $hostStmt->bind_param("s", $ip);
        $hostStmt->execute();
        $hostResult = $hostStmt->get_result();
        
        if ($hostResult->num_rows === 0) {
            $hostStmt->close();
            $nagiosConn->close();
            throw new Exception("Host with IP $ip not found in Nagios database");
        }
        
        $hostRow = $hostResult->fetch_assoc();
        $hostId = $hostRow['id'];
        $hostStmt->close();
        
        // 2. Get service information where service_description matches and config_name starts with 'imp_'
        $serviceQuery = "
            SELECT s.id, s.config_name, s.service_description 
            FROM tbl_service s 
            INNER JOIN tbl_lnkServiceToHost lsh ON s.id = lsh.idMaster 
            WHERE lsh.idSlave = ? 
            AND s.service_description = ? 
            AND s.register = '1' 
            AND s.active = '1'
            AND s.config_name LIKE 'imp_%'
        ";
        $serviceStmt = $nagiosConn->prepare($serviceQuery);
        $serviceStmt->bind_param("is", $hostId, $currentServiceName);
        $serviceStmt->execute();
        $serviceResult = $serviceStmt->get_result();
        
        if ($serviceResult->num_rows === 0) {
            $serviceStmt->close();
            $nagiosConn->close();
            throw new Exception("Service '$currentServiceName' not found for host $ip or config_name does not start with 'imp_'");
        }
        
        $serviceRow = $serviceResult->fetch_assoc();
        $serviceId = $serviceRow['id'];
        $configName = $serviceRow['config_name'];
        $serviceStmt->close();
        
        // 3. Check if new service name already exists for this host
        $checkQuery = "
            SELECT COUNT(*) as count 
            FROM tbl_service s 
            INNER JOIN tbl_lnkServiceToHost lsh ON s.id = lsh.idMaster 
            WHERE lsh.idSlave = ? 
            AND s.service_description = ? 
            AND s.register = '1' 
            AND s.active = '1'
        ";
        $checkStmt = $nagiosConn->prepare($checkQuery);
        $checkStmt->bind_param("is", $hostId, $newServiceName);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $checkRow = $checkResult->fetch_assoc();
        $checkStmt->close();
        
        if ($checkRow['count'] > 0) {
            $nagiosConn->close();
            throw new Exception("Service with name '$newServiceName' already exists for this host");
        }
        
        // 4. Update the service_description (display_name)
        $updateQuery = "UPDATE tbl_service SET service_description = ? WHERE id = ?";
        $updateStmt = $nagiosConn->prepare($updateQuery);
        $updateStmt->bind_param("si", $newServiceName, $serviceId);
        $updateResult = $updateStmt->execute();
        $updateStmt->close();
        
        $nagiosConn->close();
        
        if ($updateResult) {
            // 5. Run doit.php directly after successful rename
            $doitOutput = shell_exec('php doit.php 2>&1');
            error_log("doit.php output after service rename: " . $doitOutput);

            return array(
                'success' => true,
                'message' => "Service renamed from '$currentServiceName' to '$newServiceName' successfully"
            );
        } else {
            throw new Exception("Failed to update service name in database");
        }
        
    } catch (Exception $e) {
        error_log("Error renaming service: " . $e->getMessage());
        return array(
            'success' => false,
            'error' => $e->getMessage()
        );
    }
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $ip = isset($_POST['ip']) ? $_POST['ip'] : '';
    $currentServiceName = isset($_POST['current_service_name']) ? $_POST['current_service_name'] : '';
    $newServiceName = isset($_POST['new_service_name']) ? trim($_POST['new_service_name']) : '';

    // Basic validation
    if (empty($ip) || empty($currentServiceName) || empty($newServiceName)) {
        echo json_encode(array(
            'success' => false,
            'error' => 'All parameters (ip, current_service_name, new_service_name) are required'
        ));
        exit;
    }

    // Validate new service name (basic validation)
    if (strlen($newServiceName) > 255) {
        echo json_encode(array(
            'success' => false,
            'error' => 'New service name is too long (maximum 255 characters)'
        ));
        exit;
    }

    if (preg_match('/[<>"\']/', $newServiceName)) {
        echo json_encode(array(
            'success' => false,
            'error' => 'New service name contains invalid characters'
        ));
        exit;
    }

    try {
        $result = renameService($ip, $currentServiceName, $newServiceName);
        echo json_encode($result);
    } catch (Exception $e) {
        echo json_encode(array(
            'success' => false,
            'error' => $e->getMessage()
        ));
    }
} else {
    echo json_encode(array(
        'success' => false,
        'error' => 'This script expects a POST request'
    ));
}
?>
