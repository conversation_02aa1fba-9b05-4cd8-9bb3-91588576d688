* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: '<PERSON><PERSON>ri', sans-serif;
    color: #333;
    background-color: #fff;
    line-height: 1.4;
    font-size: 14px;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 15px 10px;
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
    font-size: 20px;
    position: relative;
    padding-bottom: 8px;
}

h1:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: #888;
    border-radius: 2px;
}

.form-section {
    margin-bottom: 15px;
    padding: 15px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #B0B0B0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.form-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.form-section h2 {
    color: #555;
    font-size: 16px;
    margin-bottom: 10px;
    border-bottom: 1px solid #B0B0B0;
    padding-bottom: 6px;
    display: flex;
    align-items: center;
}

.form-section h2 i {
    margin-right: 8px;
    color: #777;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.form-group {
    margin-bottom: 10px;
}

label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #555;
    font-size: 13px;
}

input[type="number"],
input[type="text"],
input[type="password"],
select {
    width: 100%;
    padding: 8px;
    border: 1px solid #B0B0B0;
    border-radius: 4px;
    font-size: 13px;
    background: #ffffff;
    color: #333;
    transition: all 0.3s;
    outline: none;
}

input[type="number"]:focus,
input[type="text"]:focus,
input[type="password"],
select:focus {
    border-color: #777;
    box-shadow: 0 0 0 2px rgba(119, 119, 119, 0.15);
}

input[type="text"][disabled] {
    background: #f0f0f0;
    cursor: not-allowed;
}

.radio-group,
.checkbox-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.radio-group label,
.checkbox-group label {
    display: inline-flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
    color: #555;
    font-size: 13px;
}

input[type="radio"],
input[type="checkbox"] {
    margin-right: 6px;
    accent-color: #777;
}

.no-data {
    text-align: center;
    color: #c41a23;
    font-style: italic;
    margin: 15px 0;
    padding: 8px;
    background: rgba(196, 26, 35, 0.1);
    border-radius: 4px;
}

.select2-container {
    width: 100% !important;
}

.select2-selection--multiple {
    min-height: 34px;
    border: 1px solid #B0B0B0;
    border-radius: 4px;
    background: #ffffff !important;
}

.select2-container--default .select2-selection--multiple {
    padding: 2px 5px;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    color: #333;
    background: #ffffff !important;
    padding: 2px;
    line-height: 24px;
    text-align: left;
}

.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    color: #777 !important;
    background: #ffffff !important;
    float: left;
    margin-left: 5px;
}

.select2-container .select2-search--inline {
    float: left;
}

.select2-selection__choice {
    background-color: #f0f0f0 !important;
    border: none !important;
    color: #333 !important;
    padding: 2px 6px;
    margin: 3px 5px 3px 0 !important;
    border-radius: 3px;
}

.select2-selection__choice__remove {
    color: #333 !important;
    margin-right: 4px;
}

.select2-search__field {
    width: 100% !important;
    color: #333;
    background: #ffffff !important;
    border: none !important;
    padding: 4px;
    margin-top: 0 !important;
}

.select2-dropdown {
    background-color: #ffffff !important;
    border: 1px solid #B0B0B0 !important;
    color: #333 !important;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.select2-results__option {
    color: #333 !important;
    background-color: #ffffff !important;
}

.select2-results__option--highlighted {
    background-color: #f0f0f0 !important;
    color: #333 !important;
}

.select2-container--default .select2-results__option[aria-selected="true"] {
    background-color: #e0e0e0 !important;
    color: #333 !important;
}

.select2-container--default .select2-search--inline .select2-search__field {
    background: #ffffff !important;
    color: #333 !important;
    border: none !important;
}

.excluded .select2-selection__choice {
    background-color: #fde8ea !important;
}

.reset-btn {
    background-color: #f0f0f0;
    color: #333;
    margin: 0 4px;
}

.reset-btn:hover {
    background-color: #e0e0e0;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .container {
        margin: 10px;
        padding: 10px;
    }

    h1 {
        font-size: 18px;
    }

    .form-section {
        padding: 8px;
    }

    .form-section h2 {
        font-size: 14px;
    }

    .save-button {
        width: 100%;
    }
}

table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    margin-top: 12px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

th,
td {
    border: none;
    border-bottom: 1px solid #B0B0B0;
    padding: 8px 10px;
    text-align: left;
    color: #333;
    font-size: 13px;
}

th {
    background-color: #f4f4f4;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

tr:last-child td {
    border-bottom: none;
}

tr.even-row {
    background-color: #ffffff;
}

tr.odd-row {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #f0f0f0;
}

.action-btn {
    padding: 6px;
    margin: 0 2px;
    min-width: 32px;
    height: 32px;
    border: 1px solid #B0B0B0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s;
    background-color: #f0f0f0;
    color: #333;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background-color: #e0e0e0;
    transform: translateY(-1px);
    border-color: #777;
}

.add-btn {
    background-color: #f0f0f0;
    color: #333;
    margin-bottom: 10px;
    padding: 6px 12px;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #B0B0B0;
}

.add-btn i {
    margin-right: 5px;
}

.add-btn:before {
    content: '';
    margin-right: 0;
    font-weight: normal;
}

.add-btn:hover {
    background-color: #e0e0e0;
    border-color: #777;
}

.edit-btn {
    background-color: #f0f0f0;
    color: #333;
}

.edit-btn:hover {
    background-color: #e0e0e0;
}

.delete-btn {
    background-color: #f0f0f0;
    color: #333;
}

.delete-btn:hover {
    background-color: #e0e0e0;
}

.move-btn {
    background-color: #f0f0f0;
    color: #333;
}

.move-btn:hover {
    background-color: #e0e0e0;
}

.password-hidden {
    display: inline;
}

.password-text {
    display: none;
}

.password-text.hidden {
    display: none;
}

.password-text.visible {
    display: inline;
}

.toggle-password {
    cursor: pointer;
    margin-left: 6px;
    font-size: 13px;
    color: #777;
    transition: color 0.2s;
}

.toggle-password:hover {
    color: #fff;
}

.form-section.editing {
    border: 2px solid #777;
    box-shadow: 0 0 0 4px rgba(119, 119, 119, 0.1);
}

.save-button, .reset-btn {
    background-color: #f0f0f0;
    color: #333;
    padding: 8px;
    min-width: 32px;
    height: 32px;
    border: 1px solid #B0B0B0;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 0;
    margin-right: 4px;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.save-button:hover, .reset-btn:hover {
    background-color: #e0e0e0;
    transform: translateY(-1px);
    border-color: #777;
}

tr.editing-row {
    background-color: rgba(119, 119, 119, 0.05) !important;
    border-left: 3px solid #777;
}

.form-container {
    display: none;
    margin-top: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 4px;
    animation: fadeIn 0.3s ease;
}

.form-container.visible {
    display: block;
}

.form-container.exiting {
    animation: fadeOut 0.3s ease;
    opacity: 0;
    transform: translateY(-10px);
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.action-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    align-items: center;
}

/* Button group styling */
.button-group {
    display: flex;
    gap: 6px;
    margin-bottom: 10px;
}

.button-group .action-btn {
    margin: 0;
}

/* Add status message styling */
.status-message {
    padding: 8px 12px;
    margin: 12px 0;
    border-radius: 4px;
    font-weight: 600;
    display: none;
    font-size: 13px;
}

.status-message.success {
    background-color: rgba(139, 172, 15, 0.1);
    color: #8bac0f;
    border-left: 4px solid #8bac0f;
}

.status-message.error {
    background-color: rgba(196, 26, 35, 0.1);
    color: #c41a23;
    border-left: 4px solid #c41a23;
}

/* Tooltip for icon-only buttons */
[title] {
    position: relative;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    margin-bottom: 5px;
}

@media screen and (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header {
        padding: 8px 0;
    }

    h1 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .form-section {
        padding: 12px;
        margin-bottom: 15px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .save-button {
        width: 100%;
        padding: 8px;
    }

    table {
        border: 0;
        box-shadow: none;
    }

    th {
        display: none;
    }

    tr {
        display: block;
        margin-bottom: 12px;
        border: 1px solid #444;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    td {
        display: block;
        text-align: right;
        border: none;
        border-bottom: 1px solid #333;
        position: relative;
        padding: 8px 12px 8px 45%;
    }

    td:last-child {
        border-bottom: 0;
    }

    td:before {
        content: attr(data-label);
        position: absolute;
        left: 12px;
        width: 40%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: 600;
        color: #777;
    }

    .action-btn {
        display: inline-flex;
        margin: 2px;
    }

    .toggle-password {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
    }
}

.select2-container--open .select2-dropdown {
    border-color: #777;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.select2-container--default .select2-selection--multiple .select2-selection__clear {
    margin-right: 10px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    float: left;
    margin-right: 5px;
    background-color: #f0f0f0;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    list-style: none;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #333;
    cursor: pointer;
    display: inline-block;
    font-weight: bold;
    margin-right: 5px;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-container input[type="text"],
.password-input-container input[type="password"] {
    padding-right: 30px; /* Make space for the icon */
}

.toggle-password-form {
    position: absolute;
    right: 10px;
    cursor: pointer;
    color: #777;
    transition: color 0.2s;
    font-size: 13px;
}

.toggle-password-form:hover {
    color: #333; /* Adjust hover color for light theme */
}

/* Hide default password reveal icon */
input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear {
    display: none;
}

input[type="password"]::-webkit-reveal-password,
input[type="password"]::-webkit-show-password {
    display: none;
}

.select2-container .select2-selection--multiple {
    min-height: 36px;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: 1px solid #777;
    outline: 0;
}