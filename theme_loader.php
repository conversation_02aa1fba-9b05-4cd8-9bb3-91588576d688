<?php
// Read the chosen theme from the JSON file
$themeJsonPath = 'styles/chosen-theme.json';
$chosenTheme = 'dark-theme'; // Default fallback theme

if (file_exists($themeJsonPath)) {
    $themeJsonContent = @file_get_contents($themeJsonPath); // Use @ to suppress errors if file is unreadable
    if ($themeJsonContent !== false) {
        $themeData = json_decode($themeJsonContent, true);
        if (json_last_error() === JSON_ERROR_NONE && isset($themeData['chosen_theme'])) {
            // Basic validation: ensure it's a simple string (alphanumeric, hyphen, underscore)
            // to prevent directory traversal or other issues.
            if (preg_match('/^[a-zA-Z0-9_-]+$/', $themeData['chosen_theme'])) {
                 $chosenTheme = $themeData['chosen_theme'];
            } else {
                // Log error or handle invalid theme name if needed
                error_log("Invalid theme name found in $themeJsonPath: " . $themeData['chosen_theme']);
            }
        } else {
             error_log("Error decoding JSON or 'chosen_theme' key missing in $themeJsonPath. JSON error: " . json_last_error_msg());
        }
    } else {
        error_log("Could not read theme file: $themeJsonPath");
    }
} else {
     error_log("Theme configuration file not found: $themeJsonPath");
}
?>