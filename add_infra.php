<?php
include "loadenv.php";
// Database connection parameters
$servername = $_ENV["DB_SERVER"];
$username = $_ENV["DB_USER"];
$password = $_ENV["DB_PASSWORD"];
$dbname = "bubblemaps";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Function to check if name exists in the 'infra' table
function nameExists($name) {
    global $conn;

    // Prepare the SQL statement to check if the name exists
    $stmt = $conn->prepare("SELECT id FROM infra WHERE name = ?");
    if ($stmt === false) {
        die("Prepare failed: " . $conn->error);
    }

    // Bind the parameter
    $stmt->bind_param("s", $name);

    // Execute the statement
    $stmt->execute();

    // Store the result
    $stmt->store_result();

    // Check if any rows were returned
    if ($stmt->num_rows > 0) {
        return true; // Name exists
    } else {
        return false; // Name does not exist
    }

    // Close the statement
    $stmt->close();
}

// Function to insert data into the 'infra' table
function insertIntoInfra($name) {
    global $conn;

    // Check if the name already exists
    if (nameExists($name)) {
        return '<div class="alert alert-danger">Error: Name already exists.</div>'; // Return error message
    }

    // Prepare the SQL statement
    $stmt = $conn->prepare("INSERT INTO infra (name) VALUES (?)");
    if ($stmt === false) {
        die("Prepare failed: " . $conn->error);
    }

    // Bind the parameter
    $stmt->bind_param("s", $name);

    // Execute the statement
    if ($stmt->execute()) {
        return '<div class="alert alert-success">New record inserted successfully. Refreshing...</div>'; // Return success message
    } else {
        return '<div class="alert alert-danger">Error: ' . $stmt->error . '</div>'; // Return error message
    }

    // Close the statement
    $stmt->close();
}

// Check if the name is received via POST
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['name'])) {
    $name = $_POST['name'];
    $response = insertIntoInfra($name); // Get the response from the function
    echo $response; // Output the response
} else {
    echo '<div class="alert alert-danger">Invalid request.</div>'; // Handle invalid requests
}

// Close the connection
$conn->close();
?>