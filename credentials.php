<?php
include "src/credentialsHelper.php";
include 'theme_loader.php'; // Include the theme loader
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credentials Management</title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/advancedCommands.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <script src="functions/credentialsFunctions/credentials.js"></script>
</head>

<body>
    
    <div class="container">
        <h1>Credentials Management</h1>
        <div id="global-status" class="status-message"></div>

        <!-- WMI Credentials -->
        <div class="form-section">
            <h2><i class="fa fa-windows"></i> WMI Credentials</h2>
            <div class="button-group">
                <button class="action-btn add-btn" onclick="showAddForm('wmi')" title="Add WMI"><i class="fa fa-plus"></i> Add</button>
            </div>
            <div id="wmiFormContainer" class="form-container">
                <form method="POST" action="?type=wmi" id="wmiForm">
                    <input type="hidden" name="id" id="wmi_id">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="wmi_domain">Domain</label>
                            <input type="text" id="wmi_domain" name="domain">
                        </div>
                        <div class="form-group">
                            <label for="wmi_username">Username</label>
                            <input type="text" id="wmi_username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="wmi_password">Password</label>
                            <div class="password-input-container">
                                <input type="password" id="wmi_password" name="password" required>
                                <i class="fa fa-eye toggle-password-form"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="wmi_description">Description</label>
                            <input type="text" id="wmi_description" name="description">
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="save-button" type="submit" title="Save"><i class="fa fa-floppy-o"></i></button>
                    </div>
                </form>
            </div>
            <?php 
            // Custom displayTable function to use icons-only buttons
            function displayCustomTable($table, $fields, $type) {
                $conn = getDBConnection();
                if ($conn) {
                    try {
                        $positionField = ($type === 'snmpv3') ? 'sequence' : 'position';
                        $stmt = $conn->query("SELECT * FROM $table ORDER BY $positionField");
                        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        if ($results) {
                            echo "<table><tr>";
                            $displayFields = ($type === 'snmpv3')
                                ? ['id', 'snmp_auth_protocol', 'snmp_username', 'snmp_password', 'snmp_priv_protocol', 'snmp_priv_passphrase']
                                : array_filter($fields, function ($field) {
                                    return $field !== 'position' && $field !== 'sequence';
                                });
                            foreach ($displayFields as $field) {
                                $displayName = ($field === 'snmp_password') ? 'auth password' : (($field === 'snmp_priv_passphrase') ? 'priv passphrase' : (($field === 'snmp_auth_protocol') ? 'auth protocol' : (($field === 'snmp_priv_protocol') ? 'priv protocol' : $field)));
                                echo "<th data-label='$displayName'>$displayName</th>";
                            }
                            echo "<th data-label='Actions'>Actions</th></tr>";
                            $rowCount = 0;
                            foreach ($results as $row) {
                                $rowClass = ($rowCount++ % 2 == 0) ? 'even-row' : 'odd-row';
                                echo "<tr class='$rowClass' data-row-id='{$row['id']}'>";
                                foreach ($displayFields as $field) {
                                    if (strpos($field, 'password') !== false || $field === 'snmp_priv_passphrase') {
                                        echo "<td data-label='$field'>";
                                        echo "<span class='password-hidden'>••••••••</span>";
                                        echo "<span class='password-text hidden' data-field='$field'>" . htmlspecialchars($row[$field]) . "</span>";
                                        echo "<span class='toggle-password fa fa-eye' data-target='$field'></span>";
                                        echo "</td>";
                                    } else {
                                        echo "<td data-label='$field'>" . htmlspecialchars($row[$field]) . "</td>";
                                    }
                                }
                                echo "<td data-label='Actions'>";
                                echo "<div class='action-buttons'>";
                                echo "<button class='action-btn edit-btn' onclick='editEntry(\"$type\", " . json_encode($row) . ")' title='Edit'><i class='fa fa-edit'></i></button>";
                                if ($row[$positionField] != 1) {
                                    echo "<button class='action-btn delete-btn' onclick='deleteEntry(\"$table\", {$row['id']})' title='Delete'><i class='fa fa-trash'></i></button>";
                                }
                                echo "<button class='action-btn move-btn' onclick='moveUp(\"$table\", {$row['id']})' title='Move Up'><i class='fa fa-arrow-up'></i></button>";
                                echo "<button class='action-btn move-btn' onclick='moveDown(\"$table\", {$row['id']})' title='Move Down'><i class='fa fa-arrow-down'></i></button>";
                                echo "</div>";
                                echo "</td></tr>";
                            }
                            echo "</table>";
                        } else {
                            echo "<p class='no-data'>No credentials available</p>";
                        }
                    } catch (PDOException $e) {
                        echo "Error querying $table: " . $e->getMessage();
                    }
                } else {
                    echo "Database connection failed for $table";
                }
            }
            
            displayCustomTable('cwmi', ['id', 'domain', 'username', 'password', 'description', 'position'], 'wmi');
            ?>
        </div>

        <!-- VMware Credentials -->
        <div class="form-section">
            <h2><i class="fa fa-cloud"></i> VMware Credentials</h2>
            <div class="button-group">
                <button class="action-btn add-btn" onclick="showAddForm('vmware')" title="Add VMware"><i class="fa fa-plus"></i> Add</button>
            </div>
            <div id="vmwareFormContainer" class="form-container">
                <form method="POST" action="?type=vmware" id="vmwareForm">
                    <input type="hidden" name="id" id="vmware_id">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="vm_username">Username</label>
                            <input type="text" id="vm_username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="vm_password">Password</label>
                            <div class="password-input-container">
                                <input type="password" id="vm_password" name="password" required>
                                <i class="fa fa-eye toggle-password-form"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="vm_description">Description</label>
                            <input type="text" id="vm_description" name="description">
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="save-button" type="submit" title="Save"><i class="fa fa-floppy-o"></i></button>
                    </div>
                </form>
            </div>
            <?php displayCustomTable('cvmware', ['id', 'username', 'password', 'description', 'position'], 'vmware'); ?>
        </div>

        <!-- SSH Credentials -->
        <div class="form-section">
            <h2><i class="fa fa-terminal"></i> SSH Credentials</h2>
            <div class="button-group">
                <button class="action-btn add-btn" onclick="showAddForm('ssh')" title="Add SSH"><i class="fa fa-plus"></i> Add</button>
            </div>
            <div id="sshFormContainer" class="form-container">
                <form method="POST" action="?type=ssh" id="sshForm">
                    <input type="hidden" name="id" id="ssh_id">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="ssh_username">Username</label>
                            <input type="text" id="ssh_username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="ssh_password">Password</label>
                            <div class="password-input-container">
                                <input type="password" id="ssh_password" name="password">
                                <i class="fa fa-eye toggle-password-form"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="ssh_enable">Enable</label>
                            <input type="text" id="ssh_enable" name="enable">
                        </div>
                        <div class="form-group">
                            <label for="ssh_port">Port</label>
                            <input type="number" id="ssh_port" name="port" value="22" required>
                        </div>
                        <div class="form-group">
                            <label for="ssh_description">Description</label>
                            <input type="text" id="ssh_description" name="description">
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="save-button" type="submit" title="Save"><i class="fa fa-floppy-o"></i></button>
                    </div>
                </form>
            </div>
            <?php displayCustomTable('cssh', ['id', 'username', 'password', 'enable', 'port', 'description', 'position'], 'ssh'); ?>
        </div>

        <!-- Telnet Credentials -->
        <div class="form-section">
            <h2><i class="fa fa-plug"></i> Telnet Credentials</h2>
            <div class="button-group">
                <button class="action-btn add-btn" onclick="showAddForm('telnet')" title="Add Telnet"><i class="fa fa-plus"></i> Add</button>
            </div>
            <div id="telnetFormContainer" class="form-container">
                <form method="POST" action="?type=telnet" id="telnetForm">
                    <input type="hidden" name="id" id="telnet_id">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="tel_username">Username</label>
                            <input type="text" id="tel_username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="tel_password">Password</label>
                            <div class="password-input-container">
                                <input type="password" id="tel_password" name="password">
                                <i class="fa fa-eye toggle-password-form"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="tel_enable">Enable</label>
                            <input type="text" id="tel_enable" name="enable">
                        </div>
                        <div class="form-group">
                            <label for="tel_description">Description</label>
                            <input type="text" id="tel_description" name="description">
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="save-button" type="submit" title="Save"><i class="fa fa-floppy-o"></i></button>
                    </div>
                </form>
            </div>
            <?php displayCustomTable('ctel', ['id', 'username', 'password', 'enable', 'description', 'position'], 'telnet'); ?>
        </div>

        <!-- SNMP v1/v2 -->
        <div class="form-section">
            <h2><i class="fa fa-network-wired"></i> SNMP v1/v2</h2>
            <div class="button-group">
                <button class="action-btn add-btn" onclick="showAddForm('snmp')" title="Add SNMP"><i class="fa fa-plus"></i> Add</button>
            </div>
            <div id="snmpFormContainer" class="form-container">
                <form method="POST" action="?type=snmp" id="snmpForm">
                    <input type="hidden" name="id" id="snmp_id">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="snmp_description">Description</label>
                            <input type="text" id="snmp_description" name="description" required>
                        </div>
                        <div class="form-group">
                            <label for="snmp_communityro">Community RO</label>
                            <div class="password-input-container">
                                <input type="password" id="snmp_communityro" name="communityro">
                                <i class="fa fa-eye toggle-password-form"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="snmp_communityrw">Community RW</label>
                            <div class="password-input-container">
                                <input type="password" id="snmp_communityrw" name="communityrw">
                                <i class="fa fa-eye toggle-password-form"></i>
                            </div>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="save-button" type="submit" title="Save"><i class="fa fa-floppy-o"></i></button>
                    </div>
                </form>
            </div>
            <?php
            // Custom rendering for SNMP community fields
            $conn = getDBConnection();
            if ($conn) {
                try {
                    $stmt = $conn->query("SELECT * FROM csnmp ORDER BY position");
                    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    if ($results) {
                        echo "<table><tr>";
                        $displayFields = ['id', 'description', 'communityro', 'communityrw'];
                        foreach ($displayFields as $field) {
                            $displayName = ($field === 'communityro') ? 'Community RO' : (($field === 'communityrw') ? 'Community RW' : $field);
                            echo "<th data-label='$displayName'>$displayName</th>";
                        }
                        echo "<th data-label='Actions'>Actions</th></tr>";
                        $rowCount = 0;
                        foreach ($results as $row) {
                            $rowClass = ($rowCount++ % 2 == 0) ? 'even-row' : 'odd-row';
                            echo "<tr class='$rowClass' data-row-id='{$row['id']}'>";
                            foreach ($displayFields as $field) {
                                echo "<td data-label='$field'>";
                                if ($field === 'communityro' || $field === 'communityrw') {
                                    echo "<span class='password-hidden'>••••••••</span>";
                                    echo "<span class='password-text hidden' data-field='$field'>" . htmlspecialchars($row[$field]) . "</span>";
                                    echo "<span class='toggle-password fa fa-eye' data-target='$field'></span>";
                                } else {
                                    echo htmlspecialchars($row[$field]);
                                }
                                echo "</td>";
                            }
                            echo "<td data-label='Actions'>";
                            echo "<div class='action-buttons'>";
                            echo "<button class='action-btn edit-btn' onclick='editEntry(\"snmp\", " . json_encode($row) . ")' title='Edit'><i class='fa fa-edit'></i></button>";
                            if ($row['position'] != 1) {
                                echo "<button class='action-btn delete-btn' onclick='deleteEntry(\"csnmp\", {$row['id']})' title='Delete'><i class='fa fa-trash'></i></button>";
                            }
                            echo "<button class='action-btn move-btn' onclick='moveUp(\"csnmp\", {$row['id']})' title='Move Up'><i class='fa fa-arrow-up'></i></button>";
                            echo "<button class='action-btn move-btn' onclick='moveDown(\"csnmp\", {$row['id']})' title='Move Down'><i class='fa fa-arrow-down'></i></button>";
                            echo "</div>";
                            echo "</td></tr>";
                        }
                        echo "</table>";
                    } else {
                        echo "<p class='no-data'>No credentials available</p>";
                    }
                } catch (PDOException $e) {
                    echo "Error querying csnmp: " . $e->getMessage();
                }
            }
            ?>
        </div>

        <!-- SNMP v3 -->
        <div class="form-section">
            <h2><i class="fa fa-network-wired"></i> SNMP v3</h2>
            <div class="button-group">
                <button class="action-btn add-btn" onclick="showAddForm('snmpv3')" title="Add SNMP v3"><i class="fa fa-plus"></i> Add</button>
            </div>
            <div id="snmpv3FormContainer" class="form-container">
                <form method="POST" action="?type=snmpv3" id="snmpv3Form">
                    <input type="hidden" name="id" id="snmpv3_id">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="snmp_username">Username</label>
                            <input type="text" id="snmp_username" name="snmp_username">
                        </div>
                        <div class="form-group">
                            <label for="snmp_auth_protocol">Auth Protocol</label>
                            <select id="snmp_auth_protocol" name="snmp_auth_protocol" class="form-control">
                                <option value="SHA1">SHA1</option>
                                <option value="SHA224">SHA224</option>
                                <option value="SHA256">SHA256</option>
                                <option value="SHA384">SHA384</option>
                                <option value="SHA512">SHA512</option>
                                <option value="MD5">MD5</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="snmp_password">Auth Password</label>
                            <div class="password-input-container">
                                <input type="password" id="snmp_password" name="snmp_password">
                                <i class="fa fa-eye toggle-password-form"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="snmp_priv_protocol">Priv Protocol</label>
                            <select id="snmp_priv_protocol" name="snmp_priv_protocol" class="form-control">
                                <option value="AES128" selected>AES128</option>
                                <option value="AES192">AES192</option>
                                <option value="AES192C">AES192C</option>
                                <option value="AES256">AES256</option>
                                <option value="AES256C">AES256C</option>
                                <option value="DES">DES</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="snmp_priv_passphrase">Priv Passphrase</label>
                            <div class="password-input-container">
                                <input type="password" id="snmp_priv_passphrase" name="snmp_priv_passphrase">
                                <i class="fa fa-eye toggle-password-form"></i>
                            </div>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="save-button" type="submit" title="Save"><i class="fa fa-floppy-o"></i></button>
                    </div>
                </form>
            </div>
            <?php displayCustomTable('csnmpv3', ['id', 'snmp_auth_protocol', 'snmp_username', 'snmp_password', 'snmp_priv_protocol', 'snmp_priv_passphrase', 'sequence'], 'snmpv3'); ?>
        </div>

        <!-- Other Credentials -->
        <div class="form-section">
            <h2><i class="fa fa-key"></i> Other Credentials</h2>
            <div class="button-group">
                <button id="other-creds-btn" class="action-btn add-btn" onclick="toggleOtherCredsIframe()" title="Add Other"><i class="fa fa-plus"></i> Add</button>
            </div>
            <div id="otherIframeContainer" class="form-container">
                <iframe id="otherCredsIframe" src="" frameborder="0" style="width: 100%; height: 400px; display: none;"></iframe>
            </div>
        </div>
    </div>

    <script>
        // Display status messages for form submissions
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const status = urlParams.get('status');
            
            if (status === 'success') {
                showStatusMessage('Success!', 'success');
            } else if (status === 'error') {
                showStatusMessage('Error', 'error');
            }
        });
        
        function showStatusMessage(message, type) {
            const statusElement = document.getElementById('global-status');
            statusElement.textContent = message;
            statusElement.className = 'status-message ' + type;
            statusElement.style.display = 'block';
            
            setTimeout(() => {
                statusElement.style.opacity = '0';
                setTimeout(() => {
                    statusElement.style.display = 'none';
                    statusElement.style.opacity = '1';
                }, 300);
            }, 3000);
        }
    </script>
</body>

</html>