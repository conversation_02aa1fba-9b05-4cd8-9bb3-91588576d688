<?php
include "loadenv.php";

// Get the infrastructure parameter from the URL
if (!isset($_GET['infra'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'No infrastructure specified']);
    exit;
}

$infra = $_GET['infra'];

// Connect to the database
$servername = $_ENV["DB_SERVER"];
$username = $_ENV["DB_USER"];
$password = $_ENV["DB_PASSWORD"];
$dbname = "bubblemaps";

$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Connection failed: ' . $conn->connect_error]);
    exit;
}

// Get all subnets for the infra
$sql = "SELECT DISTINCT subnet FROM hosts WHERE infra = ? AND blacklist = 0 AND apmStatus != 'ask'";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $infra);
$stmt->execute();
$result = $stmt->get_result();

$subnets = [];
while ($row = $result->fetch_assoc()) {
    $subnets[] = $row['subnet'];
}

// Result array for each subnet
$subnetData = [];

// Process each subnet
foreach ($subnets as $subnet) {
    // FIRST CHECK - Any down hosts in this subnet?
    $downCheckSql = "SELECT COUNT(*) as down_count FROM hosts 
                    WHERE subnet = ? AND infra = ? AND blacklist = 0 
                    AND apmStatus = 'down'";
    $downCheckStmt = $conn->prepare($downCheckSql);
    $downCheckStmt->bind_param("ss", $subnet, $infra);
    $downCheckStmt->execute();
    $downCheckResult = $downCheckStmt->get_result();
    $downCount = $downCheckResult->fetch_assoc()['down_count'];
    $downCheckStmt->close();
    
    // Get hosts in this subnet including service status counts
    $hostsSql = "SELECT id, hostname, ip, apmStatus, 
                pending_count, ok_count, warning_count, unknown_count, critical_count 
                FROM hosts 
                WHERE subnet = ? AND infra = ? AND blacklist = 0 AND apmStatus != 'ask'";
    $hostsStmt = $conn->prepare($hostsSql);
    $hostsStmt->bind_param("ss", $subnet, $infra);
    $hostsStmt->execute();
    $hostsResult = $hostsStmt->get_result();
    
    // If we have any DOWN hosts, immediately set worst status to DOWN
    if ($downCount > 0) {
        $worstStatus = 'down';
    } else {
        // Otherwise, determine worst status normally
        $worstStatus = 'ok'; // Default status
    }
    
    $problemHosts = 0; // Count of hosts with problems
    
    // Status counts for filtering
    $statusCounts = [
        'host_status' => [
            'up' => 0,
            'down' => 0,
            'unreachable' => 0,
            'pending' => 0
        ],
        'service_status' => [
            'ok' => 0,
            'warning' => 0,
            'critical' => 0, 
            'unknown' => 0,
            'pending' => 0
        ]
    ];
    
    // Array to track which host statuses are present in this subnet
    $hostStatusPresent = [
        'up' => false,
        'down' => false,
        'unreachable' => false,
        'pending' => false
    ];
    
    // Status priority: critical > down > warning > unknown > pending > ok
    $statusPriority = [
        'critical' => 1,
        'down' => 2,
        'warning' => 3,
        'unknown' => 4,
        'pending' => 5,
        'ok' => 6
    ];
    
    $currentPriority = 6; // Start with lowest priority (ok)
    
    // Create serviceStatuses object to store all service statuses for filtering
    $serviceStatuses = [];
    $serviceStatusCount = 0;
    
    // Store raw host statuses for filtering - this preserves actual host statuses
    $hostStatuses = [];
    
    while ($hostRow = $hostsResult->fetch_assoc()) {
        // Check host status first
        $hostStatus = $hostRow['apmStatus'];
        
        // Store original host status for filtering
        $hostStatuses[] = [
            'id' => $hostRow['id'],
            'status' => $hostStatus 
        ];
        
        // Any host not marked as "down", "unreachable", or "pending" is considered "up"
        if (!in_array($hostStatus, ['down', 'unreachable', 'pending'])) {
            $hostStatus = 'ok';
            $statusCounts['host_status']['up']++;
            $hostStatusPresent['up'] = true;
        } else {
            // Increment the appropriate host status counter
            if ($hostStatus === 'down') {
                $statusCounts['host_status']['down']++;
                $hostStatusPresent['down'] = true;
            } else if ($hostStatus === 'unreachable') {
                $statusCounts['host_status']['unreachable']++;
                $hostStatusPresent['unreachable'] = true;
            } else if ($hostStatus === 'pending') {
                $statusCounts['host_status']['pending']++;
                $hostStatusPresent['pending'] = true;
            }
        }
        
        // Determine if host has problems (not ok)
        $hasHostProblems = ($hostStatus !== 'ok');
        
        // Add service statuses to the count
        $statusCounts['service_status']['ok'] += $hostRow['ok_count'];
        $statusCounts['service_status']['warning'] += $hostRow['warning_count'];
        $statusCounts['service_status']['critical'] += $hostRow['critical_count'];
        $statusCounts['service_status']['unknown'] += $hostRow['unknown_count'];
        $statusCounts['service_status']['pending'] += $hostRow['pending_count'];
        
        // Check service statuses
        $hasCritical = $hostRow['critical_count'] > 0;
        $hasWarning = $hostRow['warning_count'] > 0;
        $hasUnknown = $hostRow['unknown_count'] > 0;
        $hasPending = $hostRow['pending_count'] > 0;
        
        // Add service statuses to the serviceStatuses object for filtering
        // We need to create dummy entries that match the structure used in host bubbles
        if ($hasCritical) {
            for ($i = 0; $i < $hostRow['critical_count']; $i++) {
                $serviceStatuses["critical_service_" . $serviceStatusCount++] = 16; // 16 = critical
            }
        }
        
        if ($hasWarning) {
            for ($i = 0; $i < $hostRow['warning_count']; $i++) {
                $serviceStatuses["warning_service_" . $serviceStatusCount++] = 4; // 4 = warning
            }
        }
        
        if ($hasUnknown) {
            for ($i = 0; $i < $hostRow['unknown_count']; $i++) {
                $serviceStatuses["unknown_service_" . $serviceStatusCount++] = 8; // 8 = unknown
            }
        }
        
        if ($hasPending) {
            for ($i = 0; $i < $hostRow['pending_count']; $i++) {
                $serviceStatuses["pending_service_" . $serviceStatusCount++] = 1; // 1 = pending
            }
        }
        
        if ($hostRow['ok_count'] > 0) {
            for ($i = 0; $i < $hostRow['ok_count']; $i++) {
                $serviceStatuses["ok_service_" . $serviceStatusCount++] = 2; // 2 = ok
            }
        }
        
        // Determine if any services have problems
        $hasServiceProblems = ($hasCritical || $hasWarning || $hasUnknown || $hasPending);
        
        // Count host as problem if either host status or any service has issues
        if ($hasHostProblems || $hasServiceProblems) {
            $problemHosts++;
        }
        
        // Skip status priority logic if we already found a down host
        if ($downCount <= 0) {
            // Host status takes precedence over service status
            if ($hostStatus === 'down' && $statusPriority['down'] < $currentPriority) {
                $currentPriority = $statusPriority['down'];
                $worstStatus = 'down';
            } else if ($hostStatus === 'unreachable' && $statusPriority['unknown'] < $currentPriority) {
                $currentPriority = $statusPriority['unknown'];
                $worstStatus = 'unreachable';
            } else if ($hostStatus === 'pending' && $statusPriority['pending'] < $currentPriority) {
                $currentPriority = $statusPriority['pending'];
                $worstStatus = 'pending';
            } else {
                // Only then check service statuses
                // Critical services
                if ($hasCritical && $statusPriority['critical'] < $currentPriority) {
                    $currentPriority = $statusPriority['critical'];
                    $worstStatus = 'critical';
                }
                
                // Warning services
                if ($hasWarning && $statusPriority['warning'] < $currentPriority) {
                    $currentPriority = $statusPriority['warning'];
                    $worstStatus = 'warning';
                }
                
                // Unknown services
                if ($hasUnknown && $statusPriority['unknown'] < $currentPriority) {
                    $currentPriority = $statusPriority['unknown'];
                    $worstStatus = 'unknown';
                }
                
                // Pending services
                if ($hasPending && $statusPriority['pending'] < $currentPriority) {
                    $currentPriority = $statusPriority['pending'];
                    $worstStatus = 'pending';
                }
            }
        }
    }
    
    // Map host status to numeric codes for filtering (only for worst status display)
    $hostStatus = 2; // Default to 2 = ok
    if ($worstStatus === 'down') {
        $hostStatus = 4; // 4 = down
    } else if ($worstStatus === 'pending') {
        $hostStatus = 1; // 1 = pending
    } else if ($worstStatus === 'unknown' || $worstStatus === 'unreachable') {
        $hostStatus = 8; // 8 = unknown/unreachable
    }
    
    // Add subnet data to result
    $subnetData[] = [
        'subnet' => $subnet,
        'worstStatus' => $worstStatus,
        'problemHosts' => $problemHosts,
        'statusCounts' => $statusCounts,
        'serviceStatuses' => $serviceStatuses,
        'hostStatus' => $hostStatus,
        'hostStatusPresent' => $hostStatusPresent,
        'hostStatuses' => $hostStatuses
    ];
    
    $hostsStmt->close();
}

// Return the data as JSON
header('Content-Type: application/json');
echo json_encode($subnetData);

// Close the database connection
$stmt->close();
$conn->close();
?> 