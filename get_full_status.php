<?php
/**
 * <PERSON><PERSON>t to read all status information for a given host from the Nagios status.dat file.
 * This includes the host status and the status of all its services.
 */

// Ensure no output buffering to prevent corruption
@ob_end_clean();
if (ob_get_level()) @ob_end_clean();

// Set content type to JSON
header('Content-Type: application/json');

// Function to safely output JSON and exit
function outputJson($data) {
    // Ensure all strings are valid UTF-8 to prevent JSON encoding issues
    array_walk_recursive($data, function(&$item) {
        if (is_string($item)) {
            // Replace invalid UTF-8 characters
            $item = mb_convert_encoding($item, 'UTF-8', 'UTF-8');
            // Remove null bytes which can break JSON
            $item = str_replace("\0", "", $item);
        }
    });
    
    // Encode with options to handle potential issues
    $json = json_encode($data, JSON_PARTIAL_OUTPUT_ON_ERROR | JSON_UNESCAPED_SLASHES);
    
    // Check if encoding failed
    if ($json === false) {
        // Fallback with just the error message
        echo '{"error":"JSON encoding failed: ' . json_last_error_msg() . '"}';
    } else {
        echo $json;
    }
    exit;
}

try {
    // Get parameters from request
    $hostname = isset($_GET['hostname']) ? $_GET['hostname'] : '';

    // Validate parameters
    if (empty($hostname)) {
        outputJson(['error' => 'Missing required parameter: hostname']);
    }

    // Debug information
    $debug = [];
    $debug['hostname'] = $hostname;

    // Path to Nagios status.dat file
    $statusDatPath = '/var/spool/nagios/status.dat';
    $debug['status_file_path'] = $statusDatPath;
    $debug['file_exists'] = file_exists($statusDatPath) ? 'yes' : 'no';
    $debug['file_readable'] = is_readable($statusDatPath) ? 'yes' : 'no';
    
    // Check if file exists and is readable
    if (!file_exists($statusDatPath)) {
        outputJson([
            'error' => 'Status.dat file does not exist',
            'debug' => $debug
        ]);
    }
    
    if (!is_readable($statusDatPath)) {
        outputJson([
            'error' => 'Status.dat file is not readable',
            'debug' => $debug
        ]);
    }

    // Basic system information
    $debug['php_version'] = phpversion();
    $debug['memory_limit'] = ini_get('memory_limit');
    $debug['max_execution_time'] = ini_get('max_execution_time');
    
    // Use SplFileObject for memory-efficient file reading
    $file = new SplFileObject($statusDatPath);
    $file->setFlags(SplFileObject::DROP_NEW_LINE | SplFileObject::READ_AHEAD | SplFileObject::SKIP_EMPTY);
    
    $hostStatus = null;
    $serviceStatus = [];

    $inHostBlock = false;
    $currentHostData = [];
    $inServiceBlock = false;
    $currentServiceData = [];

    foreach ($file as $line) {
        $line = trim($line);

        // Host block processing
        if ($line === 'hoststatus {') {
            $inHostBlock = true;
            $currentHostData = [];
            continue;
        }
        if ($inHostBlock && $line === '}') {
            $inHostBlock = false;
            if (isset($currentHostData['host_name']) && $currentHostData['host_name'] === $hostname) {
                $hostStatus = $currentHostData;
            }
            continue;
        }
        if ($inHostBlock) {
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $currentHostData[trim($key)] = $value;
            }
            continue;
        }

        // Service block processing
        if ($line === 'servicestatus {') {
            $inServiceBlock = true;
            $currentServiceData = [];
            continue;
        }
        if ($inServiceBlock && $line === '}') {
            $inServiceBlock = false;
            if (isset($currentServiceData['host_name']) && $currentServiceData['host_name'] === $hostname) {
                $serviceStatus[] = $currentServiceData;
            }
            continue;
        }
        if ($inServiceBlock) {
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $currentServiceData[trim($key)] = $value;
            }
        }
    }

    if ($hostStatus !== null) {
        outputJson([
            'hoststatus' => $hostStatus,
            'servicestatus' => $serviceStatus,
            'debug' => $debug
        ]);
    } else {
        outputJson([
            'error' => 'Host not found in status.dat',
            'debug' => $debug
        ]);
    }
    
} catch (Exception $e) {
    outputJson([
        'error' => 'Exception: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
} catch (Error $e) {
    outputJson([
        'error' => 'PHP Error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}

// This line should never be reached, but just in case
echo '{"error":"Script reached end without returning valid data"}';
exit; 