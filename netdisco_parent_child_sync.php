<?php
include "loadenv.php";

// Check if SPM module is available
$spm_check = shell_exec("php " . __DIR__ . "/checkmods.php spm 2>&1");
if (trim($spm_check) !== "true") {
    echo "SPM module is not available. Exiting script.\n";
    exit(1);
}

// Database configuration
define('DB_HOST', $_ENV["DB_SERVER"]);
define('DB_USER', $_ENV["DB_USER"]);
define('DB_PASS', $_ENV["DB_PASSWORD"]);
define('BUBBLEMAPS_DB', 'bubblemaps');
define('NAGIOSQL_DB', 'db_nagiosql_v3');
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);

/**
 * Get self IP address
 */
function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

/**
 * Simulate verify actions in NagiosQL
 */
function simulateVerifyActions($hostname) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        // Initialize cURL session with cookies
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
        ]);

        // Login to Nagios
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Login failed: ' . curl_error($ch));

        // Handle redirect
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        // Perform verification actions
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if(curl_errno($ch)) throw new Exception("Action $action failed");
        }
        
        curl_close($ch);
        echo "NagiosQL verification actions completed successfully.\n";
    } catch (Exception $e) {
        error_log("Error in simulateVerifyActions: " . $e->getMessage());
    }
}

/**
 * Get database connection for bubblemaps
 */
function getBubbleMapsConnection() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, BUBBLEMAPS_DB);
    if ($conn->connect_error) {
        throw new Exception("BubbleMaps DB connection failed: " . $conn->connect_error);
    }
    return $conn;
}

/**
 * Get database connection for nagiosql
 */
function getNagiosQLConnection() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, NAGIOSQL_DB);
    if ($conn->connect_error) {
        throw new Exception("NagiosQL DB connection failed: " . $conn->connect_error);
    }
    return $conn;
}

/**
 * Execute netdisco query for a specific switch IP
 */
function queryNetdiscoForSwitch($switchIp) {
    // Debug: Add option to test query directly
    if (isset($_GET['debug_query']) && $_GET['debug_query'] === '1') {
        echo "=== DEBUG: Testing query for switch $switchIp ===\n";
    }
    
    // Build the simplified Netdisco query for real-time connections
    $sql = <<<SQL
SELECT
    dp.ip AS parent_ip,
    dp.port AS port,
    COALESCE(
        (SELECT string_agg(ip::text, ', ') 
         FROM node_ip 
         WHERE mac = n.mac AND active = true
         GROUP BY mac),
        dp.remote_ip::text
    ) AS child_ip
FROM 
    device_port dp
LEFT JOIN 
    node n ON dp.ip = n.switch AND dp.port = n.port
WHERE
    dp.ip = '$switchIp'
    AND dp.up = 'up'
    AND (
        (n.time_last > NOW() - INTERVAL '1 hour' AND EXISTS (
            SELECT 1 FROM node_ip 
            WHERE mac = n.mac AND active = true
        ))
        OR dp.remote_ip IS NOT NULL
    )
ORDER BY 
    dp.ip, dp.port;
SQL;

    // Execute the query via psql, producing pipe-separated, header-less output for easy parsing
    $command = "sudo -u netdisco psql -d netdisco -t -A -F'|' -c " . escapeshellarg($sql);
    
    // Debug: Show the query if debugging
    if (isset($_GET['debug_query']) && $_GET['debug_query'] === '1') {
        echo "Query: $sql\n";
        echo "Command: $command\n";
    }
    
    $output = shell_exec($command . ' 2>&1');
    
    if ($output === null) {
        throw new Exception("Failed to execute netdisco query for switch: $switchIp");
    }
    
    // Debug: Log the raw output for troubleshooting
    error_log("Netdisco query for switch $switchIp returned: " . substr($output, 0, 1000) . (strlen($output) > 1000 ? "..." : ""));
    
    // Debug: Show output if debugging
    if (isset($_GET['debug_query']) && $_GET['debug_query'] === '1') {
        echo "Raw output:\n$output\n";
    }
    
    // Parse the output into structured data
    $lines = array_filter(explode("\n", trim($output)));
    $results = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '') {
            continue;
        }

        // Expected order: parent_ip|port|child_ip
        $parts = explode('|', $line);

        if (count($parts) >= 3) {
            $switchIp = trim($parts[0]);
            $port = trim($parts[1]);
            $childIpRaw = trim($parts[2]);
            
            // Handle multiple IPs separated by commas (from string_agg)
            $childIps = array_map('trim', explode(',', $childIpRaw));
            
            foreach ($childIps as $childIp) {
                // Remove CIDR notation (e.g., /32, /24) from IP addresses
                $childIp = preg_replace('/\/\d+$/', '', $childIp);
                
                // Validate IP addresses
                if (filter_var($switchIp, FILTER_VALIDATE_IP) && 
                    filter_var($childIp, FILTER_VALIDATE_IP) &&
                    $switchIp !== $childIp) {
                    
                    $results[] = [
                        'switch_ip'             => $switchIp, // parent_ip
                        'connected_device_ip'   => $childIp, // child_ip
                        'connection_type'       => 'Simplified Query',
                    ];
                } else {
                    error_log("Skipping invalid IP pair: switch=$switchIp, device=$childIp");
                }
            }
        }
    }
    
    return $results;
}

/**
 * Check if a device IP exists in bubblemaps and meets criteria
 */
function isValidDevice($deviceIp, $bubbleMapsConn) {
    if (empty($deviceIp)) {
        return false;
    }
    
    $stmt = $bubbleMapsConn->prepare("SELECT id FROM hosts WHERE ip = ? AND blacklist = 0 AND apmStatus != 'not-added'");
    if (!$stmt) {
        error_log("Failed to prepare statement: " . $bubbleMapsConn->error);
        return false;
    }
    
    $stmt->bind_param("s", $deviceIp);
    $stmt->execute();
    $result = $stmt->get_result();
    $exists = $result->num_rows > 0;
    $stmt->close();
    
    return $exists;
}

/**
 * Get host ID from nagiosql database by IP address
 */
function getHostIdByIp($ip, $nagiosqlConn) {
    $stmt = $nagiosqlConn->prepare("SELECT id FROM tbl_host WHERE address = ?");
    if (!$stmt) {
        error_log("Failed to prepare statement: " . $nagiosqlConn->error);
        return null;
    }
    
    $stmt->bind_param("s", $ip);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    return $row ? (int)$row['id'] : null;
}

/**
 * Check if adding a relationship would create a cycle
 * We're adding (childId, parentId) where childId is master and parentId is slave
 * This means "child depends on parent", so we need to check if parent can reach child
 */
function wouldCreateCycle($parentId, $childId, $nagiosqlConn) {
    // Check if parent can reach child (which would create a cycle)
    // Since we're adding child->parent, we need to check if parent->child path exists
    $visited = [];
    
    // Start from parent and see if we can reach child
    return canReach($parentId, $childId, $nagiosqlConn, $visited);
}

/**
 * Helper function to check if node A can reach node B
 */
function canReach($startNode, $targetNode, $nagiosqlConn, &$visited) {
    if ($startNode == $targetNode) {
        return true;
    }
    
    if (isset($visited[$startNode])) {
        return false; // Already visited this path
    }
    
    $visited[$startNode] = true;
    
    // Check all nodes that this node can reach (as master)
    $stmt = $nagiosqlConn->prepare("SELECT idSlave FROM tbl_lnkHostToHost WHERE idMaster = ?");
    if ($stmt) {
        $stmt->bind_param("i", $startNode);
        $stmt->execute();
        $res = $stmt->get_result();
        while ($row = $res->fetch_assoc()) {
            $nextNode = (int)$row['idSlave'];
            if (canReach($nextNode, $targetNode, $nagiosqlConn, $visited)) {
                $stmt->close();
                return true;
            }
        }
        $stmt->close();
    }
    
    return false;
}

/**
 * Create parent-child relationship in nagiosql database
 */
function createParentChildRelationship($parentIp, $childIp, $nagiosqlConn) {
    // Check if parent and child are the same
    if ($parentIp == $childIp) {
        error_log("Parent and child are the same: $parentIp");
        return false;
    }

    $parentId = getHostIdByIp($parentIp, $nagiosqlConn);
    $childId = getHostIdByIp($childIp, $nagiosqlConn);
    
    if (!$parentId || !$childId) {
        error_log("Could not find host IDs - Parent: $parentIp ($parentId), Child: $childIp ($childId)");
        return false;
    }

    // Check if relationship already exists in either orientation
    $stmt = $nagiosqlConn->prepare("SELECT COUNT(*) AS count FROM tbl_lnkHostToHost WHERE (idMaster = ? AND idSlave = ?) OR (idMaster = ? AND idSlave = ?)");
    if (!$stmt) {
        error_log("Failed to prepare statement: " . $nagiosqlConn->error);
        return false;
    }
    
    // NOTE: parentId -> childId is the canonical orientation; we still check the reverse to avoid duplicates
    $stmt->bind_param("iiii", $parentId, $childId, $childId, $parentId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    if ($row['count'] > 0) {
        // Relationship already exists in either direction
        return true;
    }

    // ------------------------------------------------------------------
    // Use robust cycle detection to prevent circular relationships
    // ------------------------------------------------------------------
    
    if (wouldCreateCycle($parentId, $childId, $nagiosqlConn)) {
        error_log("Skipping relationship $parentIp <- loop <- $childIp to avoid circular dependency (parent can reach child)");
        return false;
    }
    
    // Create the relationship (child is master, parent is slave in the relationship table)
    $stmt = $nagiosqlConn->prepare("INSERT INTO tbl_lnkHostToHost (idMaster, idSlave) VALUES (?, ?)");
    if (!$stmt) {
        error_log("Failed to prepare statement: " . $nagiosqlConn->error);
        return false;
    }
    
    // SWAP: childId is master, parentId is slave
    $stmt->bind_param("ii", $childId, $parentId);
    $success = $stmt->execute();
    $stmt->close();
    
    if ($success) {
        // Update the child host to indicate it has parents (since it's now the master)
        $stmt = $nagiosqlConn->prepare("UPDATE tbl_host SET parents = 1 WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("i", $childId);
            $stmt->execute();
            $stmt->close();
        }
        
        error_log("Created parent-child relationship: $parentIp (parent) <- $childIp (child) [NagiosQL: $childIp is master, $parentIp is slave]");
        return true;
    } else {
        error_log("Failed to create parent-child relationship: " . $nagiosqlConn->error);
        return false;
    }
}



/**
 * Main function to sync netdisco relationships
 */
function syncNetdiscoParentChildRelationships() {
    echo "Starting netdisco parent-child relationship sync...\n";
    
    $bubbleMapsConn = getBubbleMapsConnection();
    $nagiosqlConn = getNagiosQLConnection();
    
    // --- NEW: Delete all existing parent-child links before syncing ---
    try {
        $nagiosqlConn->query("DELETE FROM tbl_lnkHostToHost");
        // Also reset the 'parents' flag for all hosts
        $nagiosqlConn->query("UPDATE tbl_host SET parents = 0");
        echo "Cleared all existing parent-child links in NagiosQL.\n";
    } catch (Exception $e) {
        error_log("Failed to clear parent-child links: " . $e->getMessage());
        throw $e;
    }
    // ---------------------------------------------------------------
    
    // Track processed parent/child pairs (ordered, to avoid duplicate links)
    $processedPairs = [];
    
    // Collect all relationships first, then process them in topological order
    $allRelationships = [];
    
    try {
        // Get all host IPs from bubblemaps
        $result = $bubbleMapsConn->query("SELECT DISTINCT ip FROM hosts WHERE blacklist = 0 AND apmStatus != 'not-added'");
        
        if (!$result) {
            throw new Exception("Failed to fetch hosts from bubblemaps: " . $bubbleMapsConn->error);
        }
        
        // First pass: collect all relationships
        while ($row = $result->fetch_assoc()) {
            $hostIp = $row['ip'];
            
            try {
                echo "Querying netdisco for switch: $hostIp\n";
                $netdiscoResults = queryNetdiscoForSwitch($hostIp);
                
                if (empty($netdiscoResults)) {
                    continue; // No results for this switch
                }
                
                foreach ($netdiscoResults as $netdiscoRow) {
                    $switchIp = $netdiscoRow['switch_ip']; // parent_ip from Netdisco
                    $connectedDeviceIp = $netdiscoRow['connected_device_ip']; // child_ip from Netdisco
                    
                    if (empty($connectedDeviceIp)) {
                        continue; // No connected device IP
                    }

                    // Check if connected device exists in bubblemaps and meets criteria
                    if (isValidDevice($connectedDeviceIp, $bubbleMapsConn)) {
                        $pairKey = $switchIp . '|' . $connectedDeviceIp;
                        if (!isset($processedPairs[$pairKey])) {
                            $allRelationships[] = [
                                'parent' => $switchIp,
                                'child' => $connectedDeviceIp,
                                'type' => $netdiscoRow['connection_type']
                            ];
                            $processedPairs[$pairKey] = true;
                        }
                    }
                }
                
            } catch (Exception $e) {
                error_log("Error processing switch $hostIp: " . $e->getMessage());
            }
        }
        
        // Second pass: process relationships in topological order (parents before children)
        $processedRelationships = 0;
        $createdRelationships = 0;
        
        foreach ($allRelationships as $relationship) {
            $switchIp = $relationship['parent'];
            $connectedDeviceIp = $relationship['child'];
            
            echo "Processing relationship: $switchIp -> $connectedDeviceIp ({$relationship['type']})\n";
            
            // Create parent-child relationship (switch is parent, connected device is child)
            if (createParentChildRelationship($switchIp, $connectedDeviceIp, $nagiosqlConn)) {
                $createdRelationships++;
                echo "  ✓ Created relationship\n";
            } else {
                echo "  ✗ Skipped relationship (likely circular)\n";
            }
            $processedRelationships++;
        }
        
        echo "Netdisco sync completed. Processed: $processedRelationships, Created: $createdRelationships relationships\n";
        
        // Run verify actions if any relationships were created
        if ($createdRelationships > 0) {
            echo "Running NagiosQL verification actions...\n";
            simulateVerifyActions(getSelfIp());
            sleep(3); // Give some time for the verification to complete
        }
        
    } finally {
        $bubbleMapsConn->close();
        $nagiosqlConn->close();
    }
}

// Function to read the lock status
function isLocked($lockFile) {
    if (!file_exists($lockFile)) {
        return false;
    }
    
    $fp = @fopen($lockFile, 'r');
    if (!$fp) {
        return false;
    }
    
    // Try to get a shared lock (non-blocking)
    $locked = !flock($fp, LOCK_SH | LOCK_NB);
    fclose($fp);
    
    return $locked;
}

// Function to set the lock status
function setLock($lockFile, $status) {
    global $lockHandle;
    
    if ($status) {
        // Create or open the lock file
        $lockHandle = fopen($lockFile, 'w');
        if (!$lockHandle) {
            throw new Exception("Unable to create lock file: $lockFile");
        }
        
        // Try to acquire an exclusive lock (non-blocking)
        if (!flock($lockHandle, LOCK_EX | LOCK_NB)) {
            fclose($lockHandle);
            throw new Exception("Unable to acquire lock: $lockFile");
        }
        
        // Write PID to the lock file
        fwrite($lockHandle, getmypid());
        fflush($lockHandle);
    } else if (isset($lockHandle) && is_resource($lockHandle)) {
        // Release the lock
        flock($lockHandle, LOCK_UN);
        fclose($lockHandle);
        
        // Remove the lock file
        if (file_exists($lockFile)) {
            @unlink($lockFile);
        }
    }
}

// Script start

// Disallow the script to run multiple instances if already running
$lockFile = dirname(__FILE__) . '/locks/netdisco_sync.lock';
$lockHandle = null;

// Check if the script is already running
if (isLocked($lockFile)) {
    echo "Netdisco sync script is already running. Exiting.\n";
    exit;
}

// Set the lock
try {
    setLock($lockFile, true);
    
    // Start the sync process
    syncNetdiscoParentChildRelationships();
    
    echo "Netdisco sync script completed successfully.\n";
} catch (Exception $e) {
    // Handle any errors that occur during execution
    echo "Error: " . $e->getMessage() . "\n";
    error_log("Netdisco sync error: " . $e->getMessage());
} finally {
    // Always release the lock when the script finishes
    setLock($lockFile, false);
}
?> 