/**
 * Translation Observer - Handles automatic translation of dynamic content
 * including deeply nested iframes and modals.
 */

// The main translation observer function that can be called directly
function initTranslationObserver(translator) {
    // Make the translator globally accessible for later use
    window.translator = translator;
    
    // Function to create a recursive observer on DOM nodes
    const createRecursiveObserver = (targetNode, isIframe = false) => {
        // Skip if we can't access the document (e.g., cross-origin)
        let nodeToObserve;
        
        if (isIframe) {
            try {
                // Check if we can access this iframe
                if (!targetNode.contentDocument || !targetNode.contentDocument.body) {
                    return;
                }
                // First translate the iframe content
                window.translator.translateIframe(targetNode);
                nodeToObserve = targetNode.contentDocument.body;
                
                // Specifically check for nested iframes inside this iframe
                const nestedIframes = targetNode.contentDocument.querySelectorAll('iframe');
                if (nestedIframes.length > 0) {
                    nestedIframes.forEach(nestedIframe => {
                        try {
                            if (nestedIframe.contentDocument && nestedIframe.contentDocument.readyState === 'complete') {
                                createRecursiveObserver(nestedIframe, true);
                            }
                            
                            nestedIframe.addEventListener('load', () => {
                                createRecursiveObserver(nestedIframe, true);
                            });
                        } catch (err) {
                            console.warn('Cannot access nested iframe content due to cross-origin restrictions:', err);
                        }
                    });
                }
            } catch (e) {
                console.warn('Cannot access iframe content due to cross-origin restrictions:', e);
                return;
            }
        } else {
            nodeToObserve = targetNode;
        }
        
        // Create a new observer for this node
        const observer = new MutationObserver((mutations) => {
            // Translate the modified content
            if (isIframe) {
                window.translator.translateIframe(targetNode);
            } else {
                window.translator.translatePage(nodeToObserve);
            }
            
            // Check for any newly added nodes that might need their own observers
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // If it's an iframe, observe its content
                            if (node.tagName === 'IFRAME') {
                                // Handle the initial iframe load
                                if (node.contentDocument && node.contentDocument.readyState === 'complete') {
                                    createRecursiveObserver(node, true);
                                }
                                
                                // And make sure we handle it when it loads
                                node.addEventListener('load', () => {
                                    createRecursiveObserver(node, true);
                                });
                            }
                            
                            // For non-iframes, scan for any iframes inside them
                            else {
                                const nestedIframes = node.querySelectorAll('iframe');
                                if (nestedIframes.length > 0) {
                                    nestedIframes.forEach(iframe => {
                                        if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                                            createRecursiveObserver(iframe, true);
                                        }
                                        
                                        iframe.addEventListener('load', () => {
                                            createRecursiveObserver(iframe, true);
                                        });
                                    });
                                }
                            }
                            
                            // Special handling for elements that might contain dynamically loaded modal content
                            if (node.classList && 
                                (node.classList.contains('modal') || 
                                 node.classList.contains('formModal-content') || 
                                 node.classList.contains('iframeModal-content'))) {
                                createRecursiveObserver(node);
                            }
                        }
                    });
                }
            });
        });
        
        // Configure and start the observer
        observer.observe(nodeToObserve, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class', 'src'], // Watch for modal visibility changes and iframe src changes
            characterData: false
        });
        
        // Return the observer in case we need to stop it later
        return observer;
    };
    
    // Initialize by creating observers for the main document and any existing iframes
    const mainObserver = createRecursiveObserver(document.body);
    
    // Find and observe any existing iframes
    document.querySelectorAll('iframe').forEach(iframe => {
        if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
            createRecursiveObserver(iframe, true);
        }
        
        iframe.addEventListener('load', () => {
            createRecursiveObserver(iframe, true);
        });
    });
    
    // Specifically target the iframes in credentials.php
    const credentialsIframe = document.getElementById('otherCredsIframe');
    if (credentialsIframe) {
        credentialsIframe.addEventListener('load', () => {
            createRecursiveObserver(credentialsIframe, true);
        });
    }
    
    // Also initialize observers for elements that might contain modals
    document.querySelectorAll('.modal, .formModal-content, .iframeModal-content').forEach(modal => {
        createRecursiveObserver(modal);
    });
    
    // Return the main observer in case we need to stop it later
    return mainObserver;
} 

// Handle the specific iframe toggle functionality for credentials.php
function toggleOtherCredsIframe() {
    const iframe = document.getElementById('otherCredsIframe');
    if (iframe) {
        if (iframe.style.display === 'none' || !iframe.src) {
            iframe.src = 'other_credentials.php'; // Set the source if not already set
            iframe.style.display = 'block';
            
            // Ensure translation happens after iframe loads
            iframe.addEventListener('load', function() {
                if (window.translator) {
                    window.translator.translateIframe(iframe);
                    
                    // Check for nested iframes
                    try {
                        const nestedIframes = iframe.contentDocument.querySelectorAll('iframe');
                        if (nestedIframes.length > 0) {
                            nestedIframes.forEach(nestedIframe => {
                                nestedIframe.addEventListener('load', function() {
                                    if (window.translator) {
                                        window.translator.translateIframe(nestedIframe);
                                    }
                                });
                            });
                        }
                    } catch (e) {
                        console.warn('Cannot access iframe content:', e);
                    }
                }
            });
        } else {
            iframe.style.display = 'none';
        }
    }
} 