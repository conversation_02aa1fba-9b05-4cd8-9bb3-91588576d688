/**
 * APM Progress monitoring functionality for subnets.php
 * Handles both desktop and mobile APM progress indicators
 */

/**
 * Check APM progress and update indicators
 */
function checkApmProgress() {
    fetch('src/settingsphp/runUpdateApmStatus.php?action=progress')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                
                // Get both desktop and mobile indicators
                const simpleIndicator = document.getElementById('apm-progress-simple');
                const mobileIndicator = document.getElementById('apm-progress-mobile');
                const countText = document.getElementById('apm-progress-count');
                const countTextMobile = document.getElementById('apm-progress-count-mobile');

                // Show indicator if we have any data
                if (data.total > 0 || data.status === 'running' || data.status === 'completed') {
                    if (simpleIndicator) {
                        simpleIndicator.style.display = 'flex';
                    }
                    if (mobileIndicator) {
                        mobileIndicator.style.display = 'flex';
                    }
                    
                    const displayText = data.status === 'running' ? data.current + '/' + data.total :
                                      data.status === 'completed' ? data.total + '/' + data.total + ' ✓' :
                                      data.current + '/' + data.total;
                    
                    if (countText) {
                        countText.textContent = displayText;
                    }
                    if (countTextMobile) {
                        countTextMobile.textContent = displayText;
                    }
                } else if (data.status && data.status.startsWith('error')) {
                    if (simpleIndicator) {
                        simpleIndicator.style.display = 'flex';
                    }
                    if (mobileIndicator) {
                        mobileIndicator.style.display = 'flex';
                    }
                    if (countText) {
                        countText.textContent = 'Error';
                    }
                    if (countTextMobile) {
                        countTextMobile.textContent = 'Error';
                    }
                } else {
                    // Only hide if truly idle with no data
                    if (data.status === 'idle' && data.total === 0) {
                        if (simpleIndicator) {
                            simpleIndicator.style.display = 'none';
                        }
                        if (mobileIndicator) {
                            mobileIndicator.style.display = 'none';
                        }
                    }
                }
            } catch (parseError) {
                console.error('JSON parse error:', parseError);
            }
        })
        .catch(error => {
            console.error('Error checking APM progress:', error);
        });
}

/**
 * Start monitoring APM progress - CONTINUOUS STREAM
 */
function startApmProgressMonitoring() {
    // Check immediately
    checkApmProgress();
    
    // Then check every 500ms continuously - NEVER STOP
    setInterval(checkApmProgress, 500);
}

/**
 * Helper function to check if we're on mobile
 */
function isMobile() {
    return window.innerWidth <= 768;
} 