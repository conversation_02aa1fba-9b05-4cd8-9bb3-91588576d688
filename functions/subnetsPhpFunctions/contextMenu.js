/**
 * Context menu functionality for subnets.php
 * Handles right-click context menu for subnet bubbles
 */

/**
 * Initialize context menu functionality
 */
function initContextMenu() {
    const contextMenu = document.getElementById('context-menu');
    let currentBubble = null;
    const urlParams = new URLSearchParams(window.location.search);
    const contextMenuTitle = contextMenu.querySelector('.context-menu-title');
    
    document.addEventListener('contextmenu', function(event) {
        if (event.target.classList.contains('host-bubble')) {
            event.preventDefault();
            currentBubble = event.target;
            const bubbleData = d3.select(currentBubble).datum();
            
            if (bubbleData && bubbleData.hostname && contextMenuTitle) {
                contextMenuTitle.textContent = `Subnet: ${bubbleData.hostname}`;
            }
            
            contextMenu.style.top = event.clientY + 'px';
            contextMenu.style.left = event.clientX + 'px';
            contextMenu.style.display = 'block';
        } else {
            contextMenu.style.display = 'none';
            currentBubble = null;
        }
    });

    document.addEventListener('click', function(event) {
        if (!currentBubble) {
            contextMenu.style.display = 'none';
            return;
        }

        const bubbleData = d3.select(currentBubble).datum();

        if (event.target.id === 'rename') {
            const newName = prompt('Enter new name for the bubble:', bubbleData.hostname);
            if (newName) {
                bubbleData.hostname = newName;
                d3.select(currentBubble).datum(bubbleData);
                d3.selectAll(`#map g .bubble-text`)
                    .filter(d => d.id === bubbleData.id)
                    .each(function(d) {
                        d.hostname = newName;
                    })
                    .text(newName);
                updateBubbleName(bubbleData.id, newName);
            }
            contextMenu.style.display = 'none';
        } else if (event.target.id === 'view') {
            event.preventDefault();
            const nickname = bubbleData.hostname;
            const viewParam = urlParams.has('subnet') ? '&subnet=true' : '';
            const infraParam = urlParams.get('infra') || '';
            window.location.href = `hosts.php?subnet=${encodeURIComponent(bubbleData.subnet)}&subnetNickname=${encodeURIComponent(nickname)}&infra=${encodeURIComponent(infraParam)}${viewParam}`;
            contextMenu.style.display = 'none';
        } else if (!contextMenu.contains(event.target)) {
            contextMenu.style.display = 'none';
        }
    });
}

/**
 * Update bubble name on the server
 */
async function updateBubbleName(id, newName) {
    try {
        const response = await fetch('update_subnet_name.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `id=${id}&newName=${newName}`
        });
        const result = await response.text();
        if (result !== 'Success') {
            console.error('Error updating bubble name:', result);
        }
    } catch (error) {
        console.error('Error updating bubble name:', error);
    }
} 