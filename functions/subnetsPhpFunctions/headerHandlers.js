/**
 * Header and navigation functionality for subnets.php
 * Handles hamburger menu, window events, and layout adjustments
 */

/**
 * Initialize header functionality
 */
function initHeader() {
    const hamburger = document.querySelector('.hamburger');
    const headerButtons = document.querySelector('.header-buttons');

    hamburger.addEventListener('click', function(e) {
        e.stopPropagation();
        this.classList.toggle('active');
        headerButtons.classList.toggle('active');
    });

    document.addEventListener('click', function(event) {
        if (!event.target.closest('.header-content') || event.target.id === 'formModal-button') {
            hamburger.classList.remove('active');
            headerButtons.classList.remove('active');
        }
    });
}

/**
 * Initialize window event handlers
 */
function initWindowEvents() {
    window.addEventListener('resize', () => {
        window.location.reload();
    });

    window.addEventListener('pageshow', function(event) {
        if (event.persisted) window.location.reload();
    });

    window.addEventListener('load', function() {
        const headerHeight = document.querySelector('header').offsetHeight;
        const loaderContainer = document.querySelector('.loader-container');
        loaderContainer.style.top = `${headerHeight}px`;
        loaderContainer.style.height = `calc(100% - ${headerHeight}px)`;
    });
}

/**
 * Add mobile status container styles
 */
function addMobileStyles() {
    const mobileStyles = document.createElement('style');
    mobileStyles.innerHTML = `
        /* Mobile Status Container Styles */
        .mobile-status-container {
            display: none;
            flex-direction: column;
            padding: 8px 15px;
            border-bottom: 1px solid #555;
            position: relative;
            z-index: 8;
        }
        
        /* Only show the mobile status container in mobile view */
        @media (max-width: 768px) {
            .mobile-status-container {
                display: flex;
                gap: 10px;
                align-items: center;
                justify-content: center;
                text-align: center;
            }
            
            /* Hide header indicators on mobile - show mobile ones instead */
            header .hostlist-status-indicators {
                display: none !important;
            }
            
            header #apm-progress-simple {
                display: none !important;
            }
            
            /* Hide desktop status count wrapper on mobile */
            .status-count-wrapper {
                display: none !important;
            }
            
            /* APM Progress for mobile */
            .apm-progress-mobile {
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 5px;
            }
            
            .apm-progress-mobile i {
                color: #ccc;
                font-size: 13px;
                margin-right: 2px;
            }
        }
    `;
    document.head.appendChild(mobileStyles);
} 