// Global variable to store the search interval for subnet filtering
let searchIntervalSubnets = null;

/**
 * Entry point called on every input event from the search bar
 */
function filterSubnets() {
    const searchBar = document.getElementById('searchBar');
    if (!searchBar) return;
    const searchValue = searchBar.value.toLowerCase();

    // Clear any existing interval so we do not spawn multiple loops
    if (searchIntervalSubnets) {
        clearInterval(searchIntervalSubnets);
    }

    // Run initial search immediately
    performSubnetSearch(searchValue);

    // Keep applying the same search continuously to catch dynamic updates
    searchIntervalSubnets = setInterval(() => {
        const currentValue = searchBar.value.toLowerCase();
        performSubnetSearch(currentValue);
    }, 1);
}

/**
 * Actual search implementation. Shows / hides subnet bubbles based on match.
 * @param {string} searchTerm Raw, lower-case search term from the input field
 */
function performSubnetSearch(searchTerm) {
    // Support exact match when surrounded by quotes ( "10.0.0.0/24" )
    const isExactMatch = (searchTerm.startsWith('"') && searchTerm.endsWith('"') && searchTerm.length > 2) ||
                         (searchTerm.startsWith("'") && searchTerm.endsWith("'") && searchTerm.length > 2);
    const exactTerm = isExactMatch ? searchTerm.substring(1, searchTerm.length - 1) : '';
    const termToSearch = isExactMatch ? exactTerm : searchTerm;

    // When nothing to search just show everything and quit early
    if (termToSearch === '') {
        resetSubnetVisibility();
        return;
    }

    // Iterate over all subnet bubbles (they use the same CSS classes as host bubbles)
    d3.selectAll('.host-bubble').each(function(d) {
        if (!d) return;
        // d.hostname is the nickname, d.subnet is the subnet string
        const nicknameMatch = isExactMatch ? d.hostname.toLowerCase() === termToSearch : d.hostname.toLowerCase().includes(termToSearch);
        const subnetMatch   = isExactMatch ? (d.subnet && d.subnet.toLowerCase() === termToSearch) : (d.subnet && d.subnet.toLowerCase().includes(termToSearch));

        const visible = nicknameMatch || subnetMatch;

        // Toggle visibility for bubble, text, and badge
        d3.select(this).style('display', visible ? 'block' : 'none');
        d3.select(this.parentNode).select(`.badge-${d.id}`).style('display', visible ? 'block' : 'none');
    });

    d3.selectAll('.bubble-text').each(function(d) {
        if (!d) return;
        const nicknameMatch = isExactMatch ? d.hostname.toLowerCase() === termToSearch : d.hostname.toLowerCase().includes(termToSearch);
        const subnetMatch   = isExactMatch ? (d.subnet && d.subnet.toLowerCase() === termToSearch) : (d.subnet && d.subnet.toLowerCase().includes(termToSearch));
        const visible = nicknameMatch || subnetMatch;
        d3.select(this).style('display', visible ? 'block' : 'none');
    });
}

/**
 * Reset visibility of all subnet bubbles and related labels / badges.
 */
function resetSubnetVisibility() {
    d3.selectAll('.host-bubble').style('display', 'block');
    d3.selectAll('.bubble-text').style('display', 'block');
    d3.selectAll('[class*="badge-"]').style('display', 'block');
}

// Clear filtering interval and reset view when the input field is cleared
document.addEventListener('DOMContentLoaded', () => {
    const searchBar = document.getElementById('searchBar');
    if (!searchBar) return;

    searchBar.addEventListener('input', () => {
        if (searchBar.value === '') {
            if (searchIntervalSubnets) {
                clearInterval(searchIntervalSubnets);
                searchIntervalSubnets = null;
            }
            resetSubnetVisibility();
        }
    });
}); 