async function fetchHostGroupList(host) {
    const url = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostgrouplist&hostgroupmember=${host}`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
        const data = await response.json();
        return data.data?.hostgrouplist || [];
    } catch (error) {
        console.error('Error fetching host group list:', error);
        return [];
    }
}

async function fetchAllHostGroups() {
    const url = 'get_hostgroups_from_db.php';
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'text/plain',
            },
        });
        
        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
        
        const text = await response.text();
        return text.trim().split('\n').filter(line => line.length > 0);
    } catch (error) {
        console.error('Error fetching all host groups:', error);
        return [];
    }
}

async function realTimeHostGroupsStatus() {
    try {
        console.log('Starting realTimeHostGroupsStatus');
        
        const urlParams = new URLSearchParams(window.location.search);
        const infra = urlParams.get('infra');

        const bubbles = document.querySelectorAll('.bubble');

        // Fetch IPs from PHP
        const phpResponse = await fetch(`https://${window.location.hostname}/bubblemaps/get_infras_hosts.php?infra=${encodeURIComponent(infra)}`);
        const ipsText = await phpResponse.text();
        const infraIps = ipsText.trim().split('\n').filter(ip => ip);

        // Resolve hostnames from IPs
        const resolvedHostnames = await Promise.all(
            infraIps.map(async (ip) => {
                try {
                    const hostname = await getHostnameByIP(ip);
                    return hostname || ip; // Return hostname if resolved, otherwise keep IP
                } catch (error) {
                    console.error(`Failed to resolve ${ip}: ${error}`);
                    return ip; // Keep IP if resolution fails
                }
            })
        );

        // Fetch detailed hostgroup data from Nagios API
        const response = await fetch(`https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostgrouplist&details=true`);
        const jsonResponse = await response.json();
        console.log('Nagios API response:', jsonResponse);

        if (!jsonResponse.data || !jsonResponse.data.hostgrouplist) {
            throw new Error("Invalid hostgroups API response");
        }

        // Process each bubble
        for (const bubble of bubbles) {
            const hostgroupName = bubble.textContent.trim();

            // Initially set to pending
            bubble.className = 'bubble pending';

            // Find matching hostgroup in Nagios data
            const hostgroup = Object.values(jsonResponse.data.hostgrouplist)
                .find(group => group.group_name === hostgroupName);

            if (hostgroup) {
                // Filter members to only those that match our resolved hostnames or IPs
                const relevantMembers = hostgroup.members.filter(member => 
                    resolvedHostnames.includes(member) || infraIps.includes(member)
                );

                if (relevantMembers.length > 0) {
                    // Get statuses for relevant members
                    const memberStatuses = await Promise.all(
                        relevantMembers.map(async (member) => {
                            const { bubbleClass, criticalCount } = await getHostStatusClass(member);
                            return { member, statusClass: bubbleClass, criticalCount };
                        })
                    );

                    // Determine the overall status based on bubbleClass
                    const overallStatus = memberStatuses.reduce((worstStatus, { statusClass }) => {
                        const priority = ['critical', 'warning', 'unknown', 'pending', 'ok'].indexOf(statusClass);
                        const currentWorstPriority = ['critical', 'warning', 'unknown', 'pending', 'ok'].indexOf(worstStatus);
                        return priority < currentWorstPriority ? statusClass : worstStatus;
                    }, 'ok');

                    // Calculate total critical count (optional, if you want to display it)
                    const totalCriticalCount = memberStatuses.reduce((sum, { criticalCount }) => sum + criticalCount, 0);

                    // Set bubble properties
                    bubble.className = `bubble ${overallStatus}`;
                    bubble.title = `Members:\n${memberStatuses.map(({ member, statusClass }) =>
                        `${member}: ${statusClass.charAt(0).toUpperCase() + statusClass.slice(1)}`
                    ).join('\n')}`;
                    bubble.dataset.members = JSON.stringify(memberStatuses);
                } else {
                    console.log(`No matching members found for ${hostgroupName}`);
                    bubble.className = 'bubble unknown'; // Or another fallback status
                    bubble.title = 'Members: None matching';
                }
            } else {
                console.log(`"${hostgroupName}" not found in Nagios data`);
                bubble.className = 'bubble unknown'; // Or another fallback status
                bubble.title = 'Members: Not found in Nagios';
            }
        }

    } catch (error) {
        console.error('Error in realTimeHostGroupsStatus:', error);
        const bubbles = document.querySelectorAll('.bubble');
        bubbles.forEach(bubble => {
            bubble.className = 'bubble error';
            bubble.title = 'Error fetching status';
        });
    }
}

// Keep this file as a placeholder for backward compatibility
// but it's not needed for the new All Hosts view
function getHostGroups() {
    console.log("getHostGroups function is deprecated. Using All Hosts view instead.");
    return [];
}