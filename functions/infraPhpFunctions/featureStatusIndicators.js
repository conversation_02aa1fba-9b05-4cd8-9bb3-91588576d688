/**
 * Feature Status Indicators functionality for infra.php
 * Handles flapping, notifications, event handlers, active checks, and passive checks indicators
 */

// Global variable to store combined feature data
window.combinedFeatureData = null;

/**
 * Initialize and populate combined feature status indicators
 */
async function initFeatureStatusIndicators() {
    await updateFeatureStatusIndicators();
    
    // Set up click listeners for indicators
    function setupClickListeners() {
        document.querySelectorAll('.status-indicator[id^="feature-"]').forEach(indicator => {
            // Remove existing listeners to avoid duplicates
            indicator.removeEventListener('click', handleIndicatorClick);
            indicator.addEventListener('click', handleIndicatorClick);
        });
    }
    
    function handleIndicatorClick(e) {
        const statusType = this.id.split('-status')[0].replace('feature-', '').replace('-mobile', ''); 
        showFeatureIssuePopover(statusType, this);
    }
    
    // Initial setup
    setupClickListeners();
    
    // Re-setup click listeners periodically to catch mobile indicators
    setInterval(setupClickListeners, 1000);
    
    // Set up periodic updates for feature status indicators
    setInterval(updateFeatureStatusIndicators, 30000); // Update every 30 seconds
    
    // Set up close popover handler
    document.querySelector('.close-popover').addEventListener('click', function() {
        const popover = document.getElementById('feature-issue-popover');
        popover.style.display = 'none';
    });
    
    // Close popover when clicking outside
    document.addEventListener('click', function(e) {
        const popover = document.getElementById('feature-issue-popover');
        const indicators = document.querySelectorAll('.status-indicator');
        
        let clickedOnIndicator = false;
        indicators.forEach(indicator => {
            if (indicator.contains(e.target)) {
                clickedOnIndicator = true;
            }
        });
        
        if (!popover.contains(e.target) && !clickedOnIndicator) {
            popover.style.display = 'none';
        }
    });
}

/**
 * Update all combined feature status indicators
 */
async function updateFeatureStatusIndicators() {
    try {
        // Fetch all necessary data
        const hostObjectDataPromise = fetchHostObjectData(); // For host IPs, subnets, and details
        const hostStatusUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=hostlist&details=true`;
        const serviceStatusUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=servicelist&details=true`;

        const [hostObjectData, hostStatusResponse, serviceStatusResponse] = await Promise.all([
            hostObjectDataPromise,
            fetch(hostStatusUrl),
            fetch(serviceStatusUrl)
        ]);

        if (!hostStatusResponse.ok) throw new Error(`HTTP error (host status): ${hostStatusResponse.status}`);
        if (!serviceStatusResponse.ok) throw new Error(`HTTP error (service status): ${serviceStatusResponse.status}`);

        const hostStatusDataAll = await hostStatusResponse.json();
        const serviceStatusDataAll = await serviceStatusResponse.json();

        if (hostStatusDataAll.result.type_code !== 0) throw new Error(`API error (host status): ${hostStatusDataAll.result.message}`);
        if (serviceStatusDataAll.result.type_code !== 0) throw new Error(`API error (service status): ${serviceStatusDataAll.result.message}`);
        
        const combinedFeatures = {
            'flap-detection': { enabled: [], disabled: [], flapping: [], total_relevant: 0 },
            'notifications': { enabled: [], disabled: [], total_relevant: 0 },
            'event-handlers': { enabled: [], disabled: [], total_relevant: 0 },
            'active-checks': { enabled: [], disabled: [], total_relevant: 0 },
            'passive-checks': { enabled: [], disabled: [], total_relevant: 0 },
            'acknowledged-problems': { acknowledged: [], total_relevant: 0 }
        };

        // Process host data
        if (hostStatusDataAll.data && hostStatusDataAll.data.hostlist) {
            for (const [hostName, hostStatus] of Object.entries(hostStatusDataAll.data.hostlist)) {
                const hostDetails = hostObjectData.data.hostlist ? hostObjectData.data.hostlist[hostName] : null;
                const hostItem = {
                    type: 'host',
                    name: hostName,
                    display_name: hostName, 
                    address: hostDetails?.address || hostName,
                    subnet: hostDetails?.subnet || 'External',
                    // raw statuses for robust checking
                    is_flapping: Number(hostStatus.is_flapping) === 1,
                    flap_detection_enabled: Number(hostStatus.flap_detection_enabled) === 1,
                    notifications_enabled: Number(hostStatus.notifications_enabled) === 1,
                    event_handler_enabled: Number(hostStatus.event_handler_enabled) === 1,
                    active_checks_enabled: Number(hostStatus.checks_enabled) === 1,
                    passive_checks_enabled: Number(hostStatus.accept_passive_checks) === 1,
                    problem_has_been_acknowledged: Number(hostStatus.problem_has_been_acknowledged) === 1
                };

                combinedFeatures['flap-detection'].total_relevant++;
                if (hostItem.flap_detection_enabled) {
                    combinedFeatures['flap-detection'].enabled.push(hostItem);
                    if (hostItem.is_flapping) combinedFeatures['flap-detection'].flapping.push(hostItem);
                } else combinedFeatures['flap-detection'].disabled.push(hostItem);

                combinedFeatures['notifications'].total_relevant++;
                if (hostItem.notifications_enabled) combinedFeatures['notifications'].enabled.push(hostItem);
                else combinedFeatures['notifications'].disabled.push(hostItem);

                combinedFeatures['event-handlers'].total_relevant++;
                if (hostItem.event_handler_enabled) combinedFeatures['event-handlers'].enabled.push(hostItem);
                else combinedFeatures['event-handlers'].disabled.push(hostItem);

                combinedFeatures['active-checks'].total_relevant++;
                if (hostItem.active_checks_enabled) combinedFeatures['active-checks'].enabled.push(hostItem);
                else combinedFeatures['active-checks'].disabled.push(hostItem);

                combinedFeatures['passive-checks'].total_relevant++;
                if (hostItem.passive_checks_enabled) combinedFeatures['passive-checks'].enabled.push(hostItem);
                else combinedFeatures['passive-checks'].disabled.push(hostItem);

                combinedFeatures['acknowledged-problems'].total_relevant++;
                if (hostItem.problem_has_been_acknowledged) {
                    combinedFeatures['acknowledged-problems'].acknowledged.push(hostItem);
                }
            }
        }

        // Process service data
        if (serviceStatusDataAll.data && serviceStatusDataAll.data.servicelist) {
            for (const [hostName, services] of Object.entries(serviceStatusDataAll.data.servicelist)) {
                const hostDetails = hostObjectData.data.hostlist ? hostObjectData.data.hostlist[hostName] : null;
                for (const [serviceName, serviceStatus] of Object.entries(services)) {
                    const serviceItem = {
                        type: 'service',
                        host_name: hostName,
                        service_name: serviceName,
                        display_name: `${hostName} - ${serviceName}`,
                        host_address: hostDetails?.address || hostName,
                        host_subnet: hostDetails?.subnet || 'External',
                        // raw statuses for robust checking
                        is_flapping: Number(serviceStatus.is_flapping) === 1,
                        flap_detection_enabled: Number(serviceStatus.flap_detection_enabled) === 1,
                        notifications_enabled: Number(serviceStatus.notifications_enabled) === 1,
                        event_handler_enabled: Number(serviceStatus.event_handler_enabled) === 1,
                        active_checks_enabled: Number(serviceStatus.checks_enabled) === 1,
                        passive_checks_enabled: Number(serviceStatus.accept_passive_checks) === 1,
                        problem_has_been_acknowledged: Number(serviceStatus.problem_has_been_acknowledged) === 1
                    };

                    combinedFeatures['flap-detection'].total_relevant++;
                    if (serviceItem.flap_detection_enabled) {
                        combinedFeatures['flap-detection'].enabled.push(serviceItem);
                        if (serviceItem.is_flapping) combinedFeatures['flap-detection'].flapping.push(serviceItem);
                    } else combinedFeatures['flap-detection'].disabled.push(serviceItem);

                    combinedFeatures['notifications'].total_relevant++;
                    if (serviceItem.notifications_enabled) combinedFeatures['notifications'].enabled.push(serviceItem);
                    else combinedFeatures['notifications'].disabled.push(serviceItem);

                    combinedFeatures['event-handlers'].total_relevant++;
                    if (serviceItem.event_handler_enabled) combinedFeatures['event-handlers'].enabled.push(serviceItem);
                    else combinedFeatures['event-handlers'].disabled.push(serviceItem);

                    combinedFeatures['active-checks'].total_relevant++;
                    if (serviceItem.active_checks_enabled) combinedFeatures['active-checks'].enabled.push(serviceItem);
                    else combinedFeatures['active-checks'].disabled.push(serviceItem);

                    combinedFeatures['passive-checks'].total_relevant++;
                    if (serviceItem.passive_checks_enabled) combinedFeatures['passive-checks'].enabled.push(serviceItem);
                    else combinedFeatures['passive-checks'].disabled.push(serviceItem);

                    combinedFeatures['acknowledged-problems'].total_relevant++;
                    if (serviceItem.problem_has_been_acknowledged) {
                        combinedFeatures['acknowledged-problems'].acknowledged.push(serviceItem);
                    }
                }
            }
        }
        
        // Update indicators based on combined data
        updateIndicator('feature-flap-detection', combinedFeatures['flap-detection'], combinedFeatures['flap-detection'].flapping.length > 0);
        updateIndicator('feature-notifications', combinedFeatures['notifications']);
        updateIndicator('feature-event-handlers', combinedFeatures['event-handlers']);
        updateIndicator('feature-active-checks', combinedFeatures['active-checks']);
        updateIndicator('feature-passive-checks', combinedFeatures['passive-checks']);
        updateIndicator('feature-acknowledged-problems', combinedFeatures['acknowledged-problems']);
        
        window.combinedFeatureData = combinedFeatures;
        
    } catch (error) {
        console.error('Error updating feature status indicators:', error);
        document.querySelectorAll('.status-indicator[id^="feature-"]').forEach(indicator => {
            indicator.className = 'status-indicator error';
            const badge = indicator.querySelector('.indicator-badge');
            if (badge) {
                badge.textContent = 'ERR';
                badge.style.display = 'flex';
            }
        });
    }
}

/**
 * Update a specific indicator based on combined host and service data
 */
function updateIndicator(indicatorIdBase, data, isFlappingWarning = false) { 
    const indicators = [
        document.getElementById(`${indicatorIdBase}-status`),
        document.getElementById(`${indicatorIdBase}-status-mobile`)
    ].filter(Boolean); // Remove null elements
    
    if (indicators.length === 0) {
        console.warn(`No indicators found for base ID ${indicatorIdBase}`);
        return;
    }
    
    indicators.forEach(indicator => {
        const badge = indicator.querySelector('.indicator-badge');
    
        indicator.className = 'status-indicator'; 
        badge.style.display = 'none';
        badge.textContent = '';

            if (indicatorIdBase === 'feature-flap-detection') {
            const flappingCount = data.flapping ? data.flapping.length : 0;
            const disabledFlapDetectionCount = data.disabled ? data.disabled.length : 0;
            const totalFlapProblems = flappingCount + disabledFlapDetectionCount;

            if (totalFlapProblems > 0) {
                indicator.classList.add('warning');
                badge.textContent = totalFlapProblems;
                badge.style.display = 'flex';
                indicator.style.display = 'flex'; // Show indicator when there are issues
            } else {
                indicator.style.display = 'none'; // Hide indicator when everything is fine
            }
        } else if (indicatorIdBase === 'feature-acknowledged-problems') {
            const acknowledgedCount = data.acknowledged ? data.acknowledged.length : 0;
            
            if (acknowledgedCount > 0) {
                indicator.classList.add('warning');
                badge.textContent = acknowledgedCount;
                badge.style.display = 'flex';
                indicator.style.display = 'flex'; // Show indicator when there are acknowledged problems
            } else {
                indicator.style.display = 'none'; // Hide indicator when no problems are acknowledged
            }
        } else {
                // Original logic for other indicators (notifications, event handlers, etc.)
                const disabledCount = data.disabled.length;
                // total_relevant: total number of hosts + services considered for this feature.
                // enabled.length: number of items where feature is explicitly enabled and not problematic.

                if (disabledCount === 0) { // No items have this feature disabled
                    indicator.style.display = 'none'; // Hide indicator when everything is fine
                } else if (disabledCount > 0 && data.total_relevant > 0 && disabledCount === data.total_relevant) { // All relevant items have this feature disabled
                    indicator.classList.add('inactive');
                    badge.textContent = disabledCount;
                    badge.style.display = 'flex';
                    indicator.style.display = 'flex'; // Show indicator when there are issues
                } else if (disabledCount > 0) { // Some items have this feature disabled
                    indicator.classList.add('warning');
                    badge.textContent = disabledCount;
                    badge.style.display = 'flex';
                    indicator.style.display = 'flex'; // Show indicator when there are issues
                } else if (data.total_relevant === 0) { // No relevant items found for this feature
                    indicator.style.display = 'none'; // Hide indicator when no relevant data
                } else {
                    // Default to hidden if no other condition met
                    indicator.style.display = 'none';
                }
            }
        });
    }

/**
 * Generalized function to show popover for feature issues (combined hosts and services)
 */
function showFeatureIssuePopover(type, indicatorElement) { 
    const popover = document.getElementById('feature-issue-popover');
    const popoverTitleEl = document.getElementById('popover-title');
    const popoverListEl = document.getElementById('popover-list');
    const badge = indicatorElement.querySelector('.indicator-badge');

    if (badge.style.display === 'none' || !badge.textContent) {
        popover.style.display = 'none';
        return;
    }
    
    popoverListEl.innerHTML = ''; 
    
    let featureName = '';
    switch(type) {
        case 'flap-detection': featureName = 'Flap Detection'; break;
        case 'notifications': featureName = 'Notifications'; break;
        case 'event-handlers': featureName = 'Event Handlers'; break;
        case 'active-checks': featureName = 'Active Checks'; break;
        case 'passive-checks': featureName = 'Passive Checks'; break;
        case 'acknowledged-problems': featureName = 'Acknowledged Problems'; break;
        default: featureName = type; 
    }

    const featureData = window.combinedFeatureData;
    if (!featureData || !featureData[type]) {
        popoverListEl.innerHTML = '<li>Data not available.</li>';
        popoverTitleEl.textContent = `${featureName} - Error`;
        positionPopover();
        return;
    }
    
    const currentTypeData = featureData[type];
    const itemsToDisplay = [];
    let listTitle = '';

    if (type === 'flap-detection') {
        const flappingItems = currentTypeData.flapping || [];
        const disabledDetectionItems = currentTypeData.disabled || [];
        const totalProblems = flappingItems.length + disabledDetectionItems.length;
        listTitle = `Flap Detection Issues (${totalProblems})`;

        flappingItems.forEach(item => {
            itemsToDisplay.push({ ...item, issue_type: 'Currently Flapping' });
        });
        disabledDetectionItems.forEach(item => {
            itemsToDisplay.push({ ...item, issue_type: 'Flap Detection Disabled' });
        });

    } else if (type === 'acknowledged-problems') {
        const acknowledgedItems = currentTypeData.acknowledged || [];
        const count = acknowledgedItems.length;
        listTitle = `Acknowledged Problems (${count})`;
        itemsToDisplay.push(...acknowledgedItems.map(item => ({ ...item, issue_type: 'Problem Acknowledged' })));

    } else if (indicatorElement.classList.contains('inactive') || indicatorElement.classList.contains('warning')) {
        const count = parseInt(badge.textContent) || 0;
        listTitle = `Items with ${featureName} Disabled (${count})`;
        itemsToDisplay.push(...(currentTypeData.disabled || []).map(item => ({ ...item, issue_type: `${featureName} Disabled` })));
    } else {
        popover.style.display = 'none';
        return;
    }

    popoverTitleEl.textContent = listTitle;
    if (itemsToDisplay.length === 0) {
        popoverListEl.innerHTML = `<li>No relevant items found.</li>`;
    } else {
        itemsToDisplay.forEach(item => {
            const li = document.createElement('li');
            let displayText = '';
            if (item.type === 'host') {
                displayText = `[Host] ${item.display_name} (${item.address})`;
            } else { // service
                displayText = `[Service] ${item.display_name}`;
            }
            // Add issue type for flap detection or generic disabled message
            if (item.issue_type) {
                displayText += ` - ${item.issue_type}`;
            }

            li.textContent = displayText;
            li.onclick = function() {
                if (item.type === 'host') {
                    openHostInModal(item.name, item.address, item.subnet);
                } else { // service
                    openServiceInModal(item.host_name, item.host_address, item.host_subnet, item.service_name);
                }
                popover.style.display = 'none';
            };
            popoverListEl.appendChild(li);
        });
    }
    
    // Position the popover directly below the clicked indicator
    positionPopover();
    
    function positionPopover() {
        // Check if we're on mobile
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // For mobile: position below the status indicators container
            const statusIndicatorsContainer = document.querySelector('.mobile-status-container .hostlist-status-indicators') || 
                                            document.querySelector('.hostlist-status-indicators');
            
            if (statusIndicatorsContainer) {
                const containerRect = statusIndicatorsContainer.getBoundingClientRect();
                popover.style.position = 'fixed';
                popover.style.top = (containerRect.bottom + 10) + 'px'; // 10px gap below indicators
                popover.style.left = '50%';
                popover.style.transform = 'translateX(-50%)'; // Center horizontally only
                popover.style.maxHeight = '60vh'; // Reduced to leave more space
                popover.style.zIndex = '10000';
                popover.style.display = 'block';
            } else {
                // Fallback to center if indicators not found
                popover.style.position = 'fixed';
                popover.style.top = '50%';
                popover.style.left = '50%';
                popover.style.transform = 'translate(-50%, -50%)';
                popover.style.maxHeight = '80vh';
                popover.style.zIndex = '10000';
                popover.style.display = 'block';
            }
        } else {
            // For desktop: position directly below the indicator
            const rect = indicatorElement.getBoundingClientRect();
            
            // Position directly below indicator with gap
            popover.style.position = 'fixed'; // Use fixed instead of absolute
            popover.style.top = (rect.bottom + 10) + 'px'; // 10px gap below indicator
            popover.style.left = rect.left + 'px';
            popover.style.transform = 'none';
            popover.style.maxHeight = '80vh';
            popover.style.zIndex = '10000';
            popover.style.display = 'block';
            
            // Make sure popover isn't cut off at screen edges
            setTimeout(() => {
                const popoverRect = popover.getBoundingClientRect();
                
                // Check right edge
                if (popoverRect.right > window.innerWidth) {
                    popover.style.left = (window.innerWidth - popoverRect.width - 10) + 'px';
                }
                
                // Check bottom edge - if it goes below viewport, position above indicator
                if (popoverRect.bottom > window.innerHeight) {
                    popover.style.top = (rect.top - popoverRect.height - 10) + 'px';
                }
                
                // Check left edge
                if (popoverRect.left < 0) {
                    popover.style.left = '10px';
                }
            }, 10);
        }
    }
}

/**
 * Fetch host object data from Nagios API
 */
async function fetchHostObjectData() {
    try {
        const apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true`;
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.result.type_code !== 0) {
            throw new Error(`API error: ${data.result.message}`);
        }
        
        return data;
    } catch (error) {
        console.error('Error fetching host object data:', error);
        throw error;
    }
}

/**
 * Open a host in the modal iframe and trigger the host modal to open
 */
function openHostInModal(hostname, ip, subnet) {
    // Use the same modal opening approach as in fetchHostsBubbles.js
    // Since we're in infra.php (main page), use 'All' as dummy infra value
    const url = `host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(hostname)}&infra=All&hostip=${encodeURIComponent(ip)}&subnet=${encodeURIComponent(subnet || 'External')}`;
    
    showModal(url, 'host');
}

/**
 * Open a service in the modal iframe and trigger the service modal to open
 */
function openServiceInModal(hostname, hostAddress, hostSubnet, serviceName) {
    // For services, we need to open the host.php page and then trigger the service modal
    // Since we're in infra.php (main page), use 'All' as dummy infra value
    const url = `host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(hostname)}&infra=All&hostip=${encodeURIComponent(hostAddress)}&subnet=${encodeURIComponent(hostSubnet || 'External')}`;
    
    showModal(url, 'service', serviceName);
} 