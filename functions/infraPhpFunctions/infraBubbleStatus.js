/**
 * Infrastructure Bubble Status functionality for infra.php
 * Handles bubble colors and badges based on database queries for each infrastructure
 */

// Global variable to track which bubble currently has a detail card open
let currentOpenInfraCardBubbleId = null;

/**
 * Fetch status data for infra bubbles from the database
 * @param {Array} bubbles - Array of bubble data objects
 * @returns {Promise<Array>} - Updated bubble data with status information
 */
async function fetchInfraStatus(bubbles) {
    try {
        // Get status for all infrastructures
        const response = await fetch('get_infra_status.php');
        const data = await response.json();
        
        // Update each bubble with its status and problem host count
        return bubbles.map(bubble => {
            const infraName = bubble.hostname;
            const infraData = data.find(i => i.infra === infraName);
            
            if (infraData) {
                // Update bubble with status information
                bubble.status = infraData.worstStatus || 'ok';
                bubble.problemHosts = infraData.problemHosts || 0;
                
                // Store service statuses and host status for filtering
                bubble.serviceStatuses = infraData.serviceStatuses || {};
                bubble.hostStatus = infraData.hostStatus || 2; // Default to OK (2)
                
                // Store which host statuses are present in this infra
                bubble.hostStatusPresent = infraData.hostStatusPresent || {};
                bubble.hostStatuses = infraData.hostStatuses || [];
                
                // Store status counts from the response
                bubble.statusCounts = infraData.statusCounts || {
                    host_status: { up: 0, down: 0, unreachable: 0, pending: 0 },
                    service_status: { ok: 0, warning: 0, critical: 0, unknown: 0, pending: 0 }
                };
                
                // Make sure statusCounts has all required properties with default values
                if (!bubble.statusCounts.host_status) {
                    bubble.statusCounts.host_status = { up: 0, down: 0, unreachable: 0, pending: 0 };
                }
                if (!bubble.statusCounts.service_status) {
                    bubble.statusCounts.service_status = { ok: 0, warning: 0, critical: 0, unknown: 0, pending: 0 };
                }
            }
            
            return bubble;
        });
    } catch (error) {
        console.error("Error fetching infra status:", error);
        return bubbles; // Return original data if fetch fails
    }
}

/**
 * Update bubble visual appearance based on status
 * @param {Object} hostBubbles - D3 selection of host bubbles
 * @param {Array} bubbleData - Array of bubble data
 */
function updateBubbleVisuals(hostBubbles, bubbleData) {
    bubbleData.forEach(bubble => {
        // Update bubble class to reflect status
        const hostBubble = hostBubbles.filter(d => d.id === bubble.id);
        
        hostBubble
            .attr("class", `host-bubble ${bubble.status}`)
            .attr("data-host-status", bubble.hostStatus)
            .attr("data-service-statuses", JSON.stringify(bubble.serviceStatuses))
            .attr("data-host-statuses-present", JSON.stringify(bubble.hostStatusPresent));
    });
}

/**
 * Create or update badge for a bubble
 * @param {Object} container - D3 container element
 * @param {Object} bubble - Bubble data object
 */
function createOrUpdateBadge(container, bubble) {
    if (bubble.problemHosts <= 0) return;
    
    let badge = container.select(`.badge-${bubble.id}`);
    
    if (badge.empty()) {
        badge = container
            .append("g")
            .attr("class", `badge badge-${bubble.id}`)
            .attr("transform", `translate(${bubble.x + bubble.size * 0.7}, ${bubble.y - bubble.size * 0.7})`);
        
        // Add the badge circle
        badge.append("circle")
            .attr("r", 10)
            .style("fill", "#ff4444")
            .style("stroke", "#fff")
            .style("stroke-width", "1px");
        
        // Add the badge text
        badge.append("text")
            .attr("text-anchor", "middle")
            .attr("dy", ".35em")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("fill", "#fff");
    }
    
    // Update badge text to show problem host count
    badge.select("text").text(bubble.problemHosts);
    
    // Position badge
    badge.attr("transform", `translate(${bubble.x + bubble.size * 0.7}, ${bubble.y - bubble.size * 0.7})`);
}

/**
 * Update badge positions during bubble animation
 * @param {Object} g - D3 container element
 * @param {Array} bubbleData - Array of bubble data
 */
function updateBadgePositions(g, bubbleData) {
    bubbleData.forEach(d => {
        const badge = g.select(`.badge-${d.id}`);
        if (!badge.empty()) {
            badge.attr("transform", `translate(${d.x + d.size * 0.7}, ${d.y - d.size * 0.7})`);
        }
    });
}

/**
 * Show infrastructure detail card
 * @param {Event} event - Mouse event
 * @param {Object} d - Bubble data object
 */
function showInfraDetailCard(event, d) {
    // Prevent event from bubbling up to canvas
    event.stopPropagation();
    
    // Check if a card already exists and remove it
    const existingCard = document.getElementById('subnet-detail-card');
    if (existingCard) {
        existingCard.remove();
    }
    
    // Update the global tracking variable
    currentOpenInfraCardBubbleId = d.id;
    
    // Create the detail card element using the same approach as subnets
    const detailCard = document.createElement('div');
    detailCard.id = 'subnet-detail-card';
    detailCard.className = 'subnet-detail-card';
    
    // Get the bubble's position relative to the viewport
    const bubbleBoundingRect = event.target.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // Define card dimensions with some margin for safety
    const cardWidth = 350;
    const cardHeight = 450;
    const margin = 10;
    
    // Calculate optimal position
    // Start by trying to position to the right of the bubble
    let cardLeft = bubbleBoundingRect.right + 20;
    
    // If too close to right edge, try left side
    if (cardLeft + cardWidth > viewportWidth - margin) {
        cardLeft = bubbleBoundingRect.left - cardWidth - 20;
    }
    
    // If still doesn't fit (bubble is too wide or viewport too narrow), center horizontally
    if (cardLeft < margin || cardLeft + cardWidth > viewportWidth - margin) {
        cardLeft = Math.max(margin, Math.min(viewportWidth - cardWidth - margin, 
                           (viewportWidth - cardWidth) / 2));
    }
    
    // For vertical positioning, try to align with bubble center
    let cardTop = bubbleBoundingRect.top + (bubbleBoundingRect.height / 2) - (cardHeight / 2);
    
    // Ensure it doesn't go above or below viewport
    if (cardTop < margin) {
        cardTop = margin;
    } else if (cardTop + cardHeight > viewportHeight - margin) {
        cardTop = viewportHeight - cardHeight - margin;
    }
    
    // Set the card position
    detailCard.style.left = `${cardLeft}px`;
    detailCard.style.top = `${cardTop}px`;
    
    // Format the status counts for display
    const hostCounts = d.hostStatusPresent ? 
        `<div class="status-count-section">
            <div class="status-count-title">Host Status</div>
            <div class="status-count-row">
                ${d.hostStatusPresent.up ? 
                    `<span class="host-count ok" title="Up">Up: ${d.statusCounts && d.statusCounts.host_status ? d.statusCounts.host_status.up : 0}</span>` 
                    : ''}
                ${d.hostStatusPresent.down ? 
                    `<span class="host-count down" title="Down">Down: ${d.statusCounts && d.statusCounts.host_status ? d.statusCounts.host_status.down : 0}</span>` 
                    : ''}
                ${d.hostStatusPresent.unreachable ? 
                    `<span class="host-count unreachable" title="Unreachable">Unreachable: ${d.statusCounts && d.statusCounts.host_status ? d.statusCounts.host_status.unreachable : 0}</span>` 
                    : ''}
                ${d.hostStatusPresent.pending ? 
                    `<span class="host-count pending" title="Pending">Pending: ${d.statusCounts && d.statusCounts.host_status ? d.statusCounts.host_status.pending : 0}</span>` 
                    : ''}
            </div>
        </div>` 
        : '';
    
    // Service count section
    const hasNonZeroServiceCount = d.statusCounts && d.statusCounts.service_status && 
        (d.statusCounts.service_status.ok > 0 || 
        d.statusCounts.service_status.warning > 0 || 
        d.statusCounts.service_status.critical > 0 || 
        d.statusCounts.service_status.unknown > 0 || 
        d.statusCounts.service_status.pending > 0);

    const serviceCounts = hasNonZeroServiceCount ? 
        `<div class="status-count-section">
            <div class="status-count-title">Service Status</div>
            <div class="status-count-row">
                ${d.statusCounts.service_status.ok > 0 ? 
                    `<span class="service-count ok" title="OK">OK: ${d.statusCounts.service_status.ok}</span>` : ''}
                ${d.statusCounts.service_status.warning > 0 ? 
                    `<span class="service-count warning" title="Warning">Warning: ${d.statusCounts.service_status.warning}</span>` : ''}
                ${d.statusCounts.service_status.critical > 0 ? 
                    `<span class="service-count critical" title="Critical">Critical: ${d.statusCounts.service_status.critical}</span>` : ''}
                ${d.statusCounts.service_status.unknown > 0 ? 
                    `<span class="service-count unknown" title="Unknown">Unknown: ${d.statusCounts.service_status.unknown}</span>` : ''}
                ${d.statusCounts.service_status.pending > 0 ? 
                    `<span class="service-count pending" title="Pending">Pending: ${d.statusCounts.service_status.pending}</span>` : ''}
            </div>
        </div>`
        : '';
    
    // Calculate total hosts and services directly from status counts
    const totalHosts = 
        (d.statusCounts && d.statusCounts.host_status) ? 
        (d.statusCounts.host_status.up || 0) + 
        (d.statusCounts.host_status.down || 0) + 
        (d.statusCounts.host_status.unreachable || 0) + 
        (d.statusCounts.host_status.pending || 0) : 0;
    
    const totalServices = 
        (d.statusCounts && d.statusCounts.service_status) ? 
        (d.statusCounts.service_status.ok || 0) + 
        (d.statusCounts.service_status.warning || 0) + 
        (d.statusCounts.service_status.critical || 0) + 
        (d.statusCounts.service_status.unknown || 0) + 
        (d.statusCounts.service_status.pending || 0) : 0;
    
    // Prepare status display text - add "Worst:" prefix for non-OK statuses
    const statusDisplayText = d.status === 'ok' ? 'OK' : `WORST: ${d.status.toUpperCase()}`;
    
    // Set the card content using the same structure as subnets
    detailCard.innerHTML = `
        <div class="subnet-card-header ${d.status}">
            <span class="subnet-card-close">&times;</span>
            <h3 class="subnet-card-title">${d.hostname}</h3>
            <div class="subnet-card-subtitle">Infrastructure</div>
        </div>
        <div class="subnet-card-body">
            <div class="subnet-card-info">
                <div class="subnet-status-badge ${d.status}">TOTAL</div>
                <div class="subnet-counts">
                    <div class="count-item">
                        <span class="count-value">${totalHosts}</span>
                        <span class="count-label">Hosts</span>
                    </div>
                    <div class="count-item">
                        <span class="count-value">${totalServices}</span>
                        <span class="count-label">Services</span>
                    </div>
                </div>
            </div>
            
            ${hostCounts}
            ${serviceCounts}
            
            <div class="subnet-card-actions">
                <button class="subnet-card-btn primary" onclick="viewInfra('${d.hostname}')">
                    View Infrastructure
                </button>
            </div>
        </div>
    `;
    
    // Add the card to the document
    document.body.appendChild(detailCard);
    
    // Add a resize observer to adjust the card position if its size changes
    const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
            const card = entry.target;
            const rect = card.getBoundingClientRect();
            const margin = 10;
            
            // If card extends beyond viewport edges, reposition it
            if (rect.right > window.innerWidth - margin) {
                card.style.left = `${Math.max(margin, window.innerWidth - rect.width - margin)}px`;
            }
            
            if (rect.bottom > window.innerHeight - margin) {
                card.style.top = `${Math.max(margin, window.innerHeight - rect.height - margin)}px`;
            }
            
            if (rect.left < margin) {
                card.style.left = `${margin}px`;
            }
            
            if (rect.top < margin) {
                card.style.top = `${margin}px`;
            }
        }
    });
    
    // Start observing the detail card
    resizeObserver.observe(detailCard);
    
    // Add card close button event handler
    const closeButton = detailCard.querySelector('.subnet-card-close');
    closeButton.addEventListener('click', function() {
        resizeObserver.disconnect();
        detailCard.remove();
        // Clear the tracking variable when card is closed
        currentOpenInfraCardBubbleId = null;
    });
    
    // Close the card when clicking outside
    document.addEventListener('click', function closeCardOnOutsideClick(e) {
        // If the click is outside the card and not on a bubble, close the card
        if (!detailCard.contains(e.target) && !e.target.classList.contains('host-bubble')) {
            resizeObserver.disconnect();
            detailCard.remove();
            document.removeEventListener('click', closeCardOnOutsideClick);
            // Clear the tracking variable when card is closed
            currentOpenInfraCardBubbleId = null;
        }
    });
}

/**
 * Navigate to infrastructure view (called from the detail card)
 * @param {string} infraName - Name of the infrastructure
 */
function viewInfra(infraName) {
    const urlParams = new URLSearchParams(window.location.search);
    const targetUrl = urlParams.has('subnet')
        ? `subnets.php?infra=${encodeURIComponent(infraName)}&subnet=true`
        : `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${encodeURIComponent(infraName)}`;
    window.location.href = targetUrl;
} 