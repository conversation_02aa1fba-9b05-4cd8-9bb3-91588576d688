// Context Menu and Submenu
const setupContextMenu = () => {
    const contextMenu = document.getElementById('context-menu');
    const moveTo = document.getElementById('move-to');
    const submenu = moveTo.querySelector('.submenu');
    const contextMenuTitle = contextMenu.querySelector('.context-menu-title');

    // Function to generate hostgroup context menu items
    const generateHostgroupMenuItems = (hostgroup) => {
        const baseUrl = `https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi`;
        return `
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=84&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-moon-o fa-lg"></i> Schedule downtime for all hosts</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=85&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-moon-o fa-lg"></i> Schedule downtime for all services</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=65&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-microphone-slash fa-lg"></i> Enable notifications for all hosts</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=66&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-microphone fa-lg"></i> Disable notifications for all hosts</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=63&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-microphone-slash fa-lg"></i> Enable notifications for all services</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=64&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-microphone fa-lg"></i> Disable notifications for all services</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=67&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-toggle-off fa-lg"></i> Enable active checks of all services</a></li>
            <li><a href="#" onclick="showModal('${baseUrl}?cmd_typ=68&hostgroup=${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-toggle-on fa-lg"></i> Disable active checks of all services</a></li>
            <li><a href="#" onclick="renameHostgroup('${encodeURIComponent(hostgroup)}'); return false;"><i class="fa fa-pencil fa-lg"></i> Rename hostgroup</a></li>
        `;
    };

    document.addEventListener('contextmenu', (event) => {
        contextMenu.style.display = 'none'; // Reset display

        const ul = contextMenu.querySelector('ul');
        if (event.target.classList.contains('host-bubble')) {
            event.preventDefault();
            currentBubble = event.target;
            const bubbleData = d3.select(currentBubble).datum();
            
            // Check if we're in multi-select mode and this host is selected
            const selectedHosts = window.selectedHosts || new Set();
            const isSelected = selectedHosts.has(bubbleData.id);
            const hasMultipleSelected = selectedHosts.size > 1;
            
            if (isSelected && hasMultipleSelected) {
                // Multi-select context menu
                contextMenuTitle.textContent = `${selectedHosts.size} hosts selected`;
                ul.innerHTML = `
                    <li id="move-to">Move all selected hosts to
                        <ul class="submenu"></ul>
                    </li>
                    <li id="clear-selection">Clear selection</li>
                `;
            } else {
                // Regular single host context menu
                contextMenuTitle.textContent = `Host: ${bubbleData.hostname}`;
                ul.innerHTML = `
                    <li id="rename">Edit Name</li>
                    <li id="view">Open Details</li>
                    <li id="parent-options">Parent Options</li>
                    <li id="show-links">Filter by Connections</li>
                    <li id="move-to">Relocate to
                        <ul class="submenu"></ul>
                    </li>
                    <li id="delete">Remove Host</li>
                `;
            }
            
            // Repopulate submenu after resetting content
            populateHostGroupSubmenu();
            positionContextMenu(event, contextMenu);
        } else if (event.target.classList.contains('group-bubble')) {
            event.preventDefault();
            const groupData = d3.select(event.target).datum();
            const hostgroup = groupData.hostgroup;
            contextMenuTitle.textContent = `Hostgroup: ${hostgroup}`;
            ul.innerHTML = generateHostgroupMenuItems(hostgroup);
            positionContextMenu(event, contextMenu);
        }
    });

    document.addEventListener('click', (event) => {
        if (!currentBubble && !event.target.closest('.group-bubble')) {
            contextMenu.style.display = 'none';
            return;
        }

        if (event.target.classList.contains('host-bubble') || contextMenu.contains(event.target)) {
            const bubbleData = currentBubble ? d3.select(currentBubble).datum() : null;
            const isClickInsideMenu = contextMenu.contains(event.target);

            if (event.target.id === 'rename' && bubbleData) {
                const newName = prompt('Enter new name:', bubbleData.hostname);
                if (newName) {
                    bubbleData.hostname = newName;
                    d3.select(currentBubble).datum(bubbleData);
                    d3.selectAll('#map g .bubble-text')
                        .filter(d => d.id === bubbleData.id)
                        .each(d => d.hostname = newName)
                        .text(newName);
                    updateBubbleName(bubbleData.id, newName);
                }
                contextMenu.style.display = 'none';
            } else if (event.target.id === 'view' && bubbleData) {
                event.preventDefault();
                (async () => {
                    const {
                        ip,
                        subnet,
                        hostname
                    } = bubbleData;
                    const realHostName = (await getHostnameByIP(ip)) ?? hostname;
                    const isAllHostsView = urlParams.get('subnet') === 'all' && urlParams.get('subnetNickname') === 'All Hosts';
                    const viewParam = isAllHostsView ? '' : '&subnet=true';
                    showModal(`host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(realHostName)}&infra=${encodeURIComponent(urlParams.get('infra'))}&hostip=${encodeURIComponent(ip)}&subnet=${subnet}${viewParam}`);
                    contextMenu.style.display = 'none';
                })();
            } else if (event.target.id === 'delete' && bubbleData) {
                blacklistHost(bubbleData.ip, currentBubble, bubbleData);
                contextMenu.style.display = 'none';
            } else if (event.target.id === 'show-links' && bubbleData) {
                const linkedIds = new Set([bubbleData.id]);
                const linkedIps = new Set([bubbleData.ip]);

                d3.selectAll('.parent-child-arrow').each(function () {
                    const arrowData = d3.select(this).datum();
                    if (arrowData.parentId === bubbleData.id) {
                        linkedIps.add(arrowData.childIp);
                    } else if (arrowData.childIp === bubbleData.ip) {
                        linkedIds.add(arrowData.parentId);
                    }
                });

                d3.selectAll('.host-bubble').each(function (d) {
                    const shouldDisplay = linkedIds.has(d.id) || linkedIps.has(d.ip);
                    d3.select(this).style('display', shouldDisplay ? 'block' : 'none');
                    d3.selectAll('.bubble-text')
                        .filter(text => text.id === d.id)
                        .style('display', shouldDisplay ? 'block' : 'none');
                    d3.select(`.badge-${d.id}`).style('display', shouldDisplay ? 'block' : 'none');
                });

                d3.selectAll('.parent-child-arrow').each(function () {
                    const arrowData = d3.select(this).datum();
                    const shouldDisplay = linkedIds.has(arrowData.parentId) && linkedIps.has(arrowData.childIp);
                    d3.select(this).style('display', shouldDisplay ? 'block' : 'none');
                });

                d3.selectAll('.group-bubble').each(function (groupData) {
                    const groupBubble = d3.select(this);
                    const hostgroup = groupData.hostgroup;
                    
                    // Always hide group bubble for "No Hostgroup" or "unknown"
                    if (hostgroup === "No Hostgroup" || hostgroup.toLowerCase() === "unknown") {
                        groupBubble.style('display', 'none');
                        d3.selectAll('.group-text')
                            .filter(d => d.hostgroup === hostgroup)
                            .style('display', 'none');
                        return;
                    }

                    const groupClassName = hostgroup.toLowerCase().replace(/\s+/g, '-');
                    const allHostBubbles = d3.selectAll('.host-bubble');
                    let totalHostBubbles = 0;
                    let visibleHostBubbles = 0;

                    allHostBubbles.each(function (hostData) {
                        if (hostData.hostgroups && hostData.hostgroups[0].toLowerCase().replace(/\s+/g, '-') === groupClassName) {
                            totalHostBubbles++;
                            if (d3.select(this).style('display') !== 'none') {
                                visibleHostBubbles++;
                            }
                        }
                    });

                    const shouldHideGroup = totalHostBubbles > 0 && visibleHostBubbles === 0;
                    groupBubble.style('display', shouldHideGroup ? 'none' : 'block');
                    d3.selectAll('.group-text')
                        .filter(d => d.hostgroup === hostgroup)
                        .style('display', shouldHideGroup ? 'none' : 'block');
                });

                contextMenu.style.display = 'none';
            } else if (event.target.id === 'parent-options' && bubbleData) {
                event.preventDefault();
                (async () => {
                    const { ip, hostname } = bubbleData;
                    const realHostName = (await getHostnameByIP(ip)) ?? hostname;
                    const infra = encodeURIComponent(urlParams.get('infra') || '');
                    showModal(`checkCommandHost.php?hostname=${encodeURIComponent(realHostName)}&infra=${infra}&section=parents`);
                    contextMenu.style.display = 'none';
                })();
            } else if (event.target.id === 'clear-selection') {
                // Clear the multi-selection
                if (window.clearHostSelection) {
                    window.clearHostSelection();
                }
                contextMenu.style.display = 'none';
            } else if (!isClickInsideMenu) {
                contextMenu.style.display = 'none';
                moveTo?.classList.remove('active');
            }
        } else {
            contextMenu.style.display = 'none';
        }
    });

    // Helper function to position the context menu
    const positionContextMenu = (event, menu) => {
        // Show the menu temporarily to get accurate dimensions
        menu.style.display = 'block';

        // Use actual dimensions, with fallbacks
        const menuHeight = menu.offsetHeight || 200;
        const menuWidth = menu.offsetWidth || 150;
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        const buffer = 10; // Small buffer from viewport edges

        let top = event.clientY;
        let left = event.clientX;

        // Adjust if menu would go beyond bottom edge
        if (top + menuHeight + buffer > viewportHeight) {
            top = event.clientY - menuHeight; // Flip above the click point
        }
        // Adjust if menu would go beyond right edge
        if (left + menuWidth + buffer > viewportWidth) {
            left = event.clientX - menuWidth; // Flip left of the click point
        }

        // Ensure it doesn't go off the top or left edge
        top = Math.max(buffer, top); // Keep a small buffer from top
        left = Math.max(buffer, left); // Keep a small buffer from left

        menu.style.top = `${top}px`;
        menu.style.left = `${left}px`;
    };

    if (!isTouchDevice()) {
        document.querySelector('#move-to')?.addEventListener('mouseenter', () => submenu.style.display = 'block');
        document.querySelector('#move-to')?.addEventListener('mouseleave', () => submenu.style.display = 'none');
    }
};

const populateHostGroupSubmenu = async () => {
    const submenu = document.querySelector('#move-to .submenu');
    if (!submenu) return;

    try {
        const hostGroups = await fetchAllHostGroups();

        submenu.innerHTML = '';

        // Create search item (li outside the ul)
        const searchLi = document.createElement('li');
        searchLi.className = 'search-item';
        
        // Create search input container div
        const searchDiv = document.createElement('div');
        searchDiv.style.display = 'flex';
        searchDiv.style.gap = '5px';
        
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = 'Search host groups...';
        searchInput.style.flexGrow = '1';
        
        // Create "+" button for adding new hostgroups
        const addButton = document.createElement('button');
        addButton.innerHTML = '<i class="fa fa-plus"></i>';
        addButton.title = 'Add new hostgroup';
        addButton.className = 'add-hostgroup-btn';
        
        addButton.addEventListener('click', () => {
            const hostgroupName = prompt('Enter new hostgroup name:');
            if (hostgroupName) {
                // Create loading animation
                const loadingDiv = document.createElement('div');
                loadingDiv.id = 'loading-animation-hostgroup';
                loadingDiv.style.position = 'fixed';
                loadingDiv.style.top = '50%';
                loadingDiv.style.left = '50%';
                loadingDiv.style.transform = 'translate(-50%, -50%)';
                loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                loadingDiv.style.color = 'white';
                loadingDiv.style.padding = '20px';
                loadingDiv.style.borderRadius = '8px';
                loadingDiv.style.zIndex = '1000';
                loadingDiv.innerHTML = 'Adding hostgroup... <div class="loader" style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 10px auto;"></div>';
                document.body.appendChild(loadingDiv);
                
                // Add spin animation
                const style = document.createElement('style');
                style.textContent = `@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }`;
                document.head.appendChild(style);
                
                // Call PHP endpoint to add hostgroup
                fetch('add_hostgroup.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `hostgroup_name=${encodeURIComponent(hostgroupName)}&alias=${encodeURIComponent(hostgroupName)}&config_id=1`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert(`Error: ${data.message}`);
                    }
                })
                .catch(error => {
                    console.error('Error adding hostgroup:', error);
                    alert('Error adding hostgroup');
                })
                .finally(() => {
                    document.body.removeChild(loadingDiv);
                    document.head.removeChild(style);
                });
            }
        });
        
        searchDiv.appendChild(searchInput);
        searchDiv.appendChild(addButton);
        searchLi.appendChild(searchDiv);
        submenu.appendChild(searchLi);

        // Create list container (ul)
        const listContainer = document.createElement('ul');
        listContainer.className = 'list-container';
        submenu.appendChild(listContainer);

        // Function to render the list based on filter
        const renderList = (filter = '') => {
            listContainer.innerHTML = '';

            const filteredGroups = hostGroups.filter(group => {
                const [, alias] = group.split('. ', 2);
                return alias.toLowerCase().startsWith(filter.toLowerCase());
            });

            if (filteredGroups.length === 0) {
                const li = document.createElement('li');
                li.textContent = filter ? 'No matching host groups' : 'No host groups available';
                listContainer.appendChild(li);
                return;
            }

            filteredGroups.forEach(group => {
                const [id, alias] = group.split('. ', 2);
                const li = document.createElement('li');
                li.textContent = alias;
                li.dataset.id = id;

                li.addEventListener('click', async () => {
                    // Check if we have multiple selected hosts
                    const selectedHosts = window.selectedHosts || new Set();
                    const hasMultipleSelected = selectedHosts.size > 1;
                    
                    if (hasMultipleSelected) {
                        // Handle multiple host move
                        const hostGroupId = li.dataset.id;
                        const hostGroupName = alias;
                        const hostCount = selectedHosts.size;

                        // Get all selected host data
                        const selectedHostsData = [];
                        const allHostBubbles = d3.selectAll('.host-bubble');
                        allHostBubbles.each(function(d) {
                            if (selectedHosts.has(d.id)) {
                                selectedHostsData.push(d);
                            }
                        });

                        const ips = selectedHostsData.map(h => h.ip);

                        // Show confirmation
                        const confirmed = confirm(`Move ${hostCount} selected host(s) to hostgroup "${hostGroupName}"?`);
                        if (!confirmed) return;

                        // Create loading animation
                        const loadingDiv = document.createElement('div');
                        loadingDiv.id = 'loading-animation';
                        loadingDiv.style.position = 'fixed';
                        loadingDiv.style.top = '50%';
                        loadingDiv.style.left = '50%';
                        loadingDiv.style.transform = 'translate(-50%, -50%)';
                        loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                        loadingDiv.style.color = 'white';
                        loadingDiv.style.padding = '20px';
                        loadingDiv.style.borderRadius = '8px';
                        loadingDiv.style.zIndex = '1000';
                        loadingDiv.innerHTML = `Moving ${hostCount} host(s)... <div class="loader" style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 10px auto;"></div>`;

                        const style = document.createElement('style');
                        style.textContent = `
                            @keyframes spin {
                                0% { transform: rotate(0deg); }
                                100% { transform: rotate(360deg); }
                            }
                        `;
                        document.head.appendChild(style);
                        document.body.appendChild(loadingDiv);

                        try {
                            const response = await fetch('move_host_to_group.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                },
                                body: `ips=${encodeURIComponent(JSON.stringify(ips))}&hostGroupId=${encodeURIComponent(hostGroupId)}`
                            });

                            const result = await response.json();
                            if (result.success) {
                                // Show detailed results only if there were errors
                                if (result.details && result.details.errorCount > 0) {
                                    const errorDetails = result.details.errors.join('\n');
                                    alert(`${result.message}\n\nErrors:\n${errorDetails}`);
                                }
                                // Remove success alert - just reload to reflect changes
                                window.location.reload(); // Reload to reflect the changes
                            } else {
                                alert(`Error: ${result.message}`);
                            }
                        } catch (error) {
                            console.error('Error moving hosts to group:', error);
                            alert('An error occurred while moving the hosts to the group');
                        } finally {
                            document.body.removeChild(loadingDiv);
                            document.head.removeChild(style);
                        }
                    } else {
                        // Handle single host move (original functionality)
                        if (!currentBubble) return;
                        const bubbleData = d3.select(currentBubble).datum();
                        const hostIp = bubbleData.ip;
                        const hostGroupId = li.dataset.id;

                        // Create and show the loading animation with inline styles
                        const loadingDiv = document.createElement('div');
                        loadingDiv.id = 'loading-animation';
                        loadingDiv.style.position = 'fixed';
                        loadingDiv.style.top = '50%';
                        loadingDiv.style.left = '50%';
                        loadingDiv.style.transform = 'translate(-50%, -50%)';
                        loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                        loadingDiv.style.color = 'white';
                        loadingDiv.style.padding = '20px';
                        loadingDiv.style.borderRadius = '8px';
                        loadingDiv.style.zIndex = '1000';
                        loadingDiv.innerHTML = 'Moving host... <div class="loader" style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 10px auto;"></div>';

                        // Create and add the spin animation style
                        const style = document.createElement('style');
                        style.textContent = `
                            @keyframes spin {
                                0% { transform: rotate(0deg); }
                                100% { transform: rotate(360deg); }
                            }
                        `;
                        document.head.appendChild(style);

                        document.body.appendChild(loadingDiv);

                        try {
                            const response = await fetch('move_host_to_group.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                },
                                body: `ip=${encodeURIComponent(hostIp)}&hostGroupId=${encodeURIComponent(hostGroupId)}`
                            });

                            const result = await response.json();
                            if (result.success) {
                                // Show detailed results only if there were errors
                                if (result.details && result.details.errorCount > 0) {
                                    const errorDetails = result.details.errors.join('\n');
                                    alert(`${result.message}\n\nErrors:\n${errorDetails}`);
                                }
                                // Remove success alert - just reload to reflect changes
                                window.location.reload(); // Reload to reflect the changes
                            } else {
                                alert(`Error: ${result.message}`);
                            }
                        } catch (error) {
                            console.error('Error moving host to group:', error);
                            alert('An error occurred while moving the host to the group');
                        } finally {
                            document.body.removeChild(loadingDiv);
                            document.head.removeChild(style); //remove style tag.
                        }
                    }

                    document.getElementById('context-menu').style.display = 'none';
                    document.getElementById('move-to').classList.remove('active');
                });

                listContainer.appendChild(li);
            });
        };

        renderList();
        searchInput.addEventListener('input', (e) => renderList(e.target.value.trim()));

    } catch (error) {
        console.error('Error populating submenu:', error);
        submenu.innerHTML = '';
        const li = document.createElement('li');
        li.textContent = 'Error loading host groups';
        submenu.appendChild(li);
    }
};

// Helper function to rename a hostgroup
async function renameHostgroup(oldNameEncoded) {
    try {
        const oldName = decodeURIComponent(oldNameEncoded);
        const newName = prompt('Enter a new name for hostgroup:', oldName);
        if (!newName || newName.trim() === '' || newName.trim() === oldName) return;

        // Show loading overlay
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loading-animation-rename';
        loadingDiv.style.position = 'fixed';
        loadingDiv.style.top = '50%';
        loadingDiv.style.left = '50%';
        loadingDiv.style.transform = 'translate(-50%, -50%)';
        loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        loadingDiv.style.color = 'white';
        loadingDiv.style.padding = '20px';
        loadingDiv.style.borderRadius = '8px';
        loadingDiv.style.zIndex = '1000';
        loadingDiv.innerHTML = 'Renaming hostgroup... <div class="loader" style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 10px auto;"></div>';

        // Add spin animation style
        const style = document.createElement('style');
        style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
        document.head.appendChild(style);
        document.body.appendChild(loadingDiv);

        const response = await fetch('rename_hostgroup.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `oldName=${encodeURIComponent(oldName)}&newName=${encodeURIComponent(newName.trim())}`
        });
        const result = await response.json();
        if (result.success) {
            window.location.reload();
        } else {
            alert(`Error: ${result.message}`);
        }
    } catch (error) {
        console.error('Error renaming hostgroup:', error);
        alert('An unexpected error occurred while renaming the hostgroup');
    } finally {
        const loadingDiv = document.getElementById('loading-animation-rename');
        if (loadingDiv) document.body.removeChild(loadingDiv);
        const spinStyle = Array.from(document.head.querySelectorAll('style')).find(s => s.textContent.includes('@keyframes spin'));
        if (spinStyle) document.head.removeChild(spinStyle);
    }
}