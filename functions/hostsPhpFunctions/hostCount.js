/**
 * Host and Service Count Display Module
 * Fetches and displays host and service count information from database
 */

// Constants
const STATUS_UPDATE_INTERVAL = 30000; // 30 seconds
const FILTER_UPDATE_INTERVAL = 1; // 1 millisecond

// Status definitions with proper names and color classes
const STATUS_DEFINITIONS = {
    up: { name: 'Up', color: 'up' },
    down: { name: 'Down', color: 'down' },
    unreachable: { name: 'Unreachable', color: 'unreachable' },
    pending: { name: 'Pending', color: 'pending' }
};

// DOM elements
let hostCountContainer = null;
let serviceCountContainer = null;
let isVisible = true;

// Host status definitions with names and colors
const HOST_STATUS_DEFINITIONS = {
    up: { name: 'Up', color: 'ok' },
    down: { name: 'Down', color: 'down' },
    unreachable: { name: 'Unreachable', color: 'unreachable' },
    pending: { name: 'Pending', color: 'pending' }
};

// Service status definitions with names and colors
const SERVICE_STATUS_DEFINITIONS = {
    ok: { name: 'OK', color: 'ok' },
    warning: { name: 'Warning', color: 'warning' },
    critical: { name: 'Critical', color: 'critical' },
    unknown: { name: 'Unknown', color: 'unknown' },
    pending: { name: 'Pending', color: 'pending' }
};

// Map Nagios numeric statuses to class names
const HOST_STATUS_CLASS_MAPPING = {
    1: 'pending',  // Pending host
    2: 'ok',       // Up host
    4: 'down',     // Down host
    8: 'unknown'   // Unreachable/unknown host
};

const SERVICE_STATUS_CLASS_MAPPING = {
    1: 'pending',  // Pending service
    2: 'ok',       // OK service
    4: 'warning',  // Warning service
    8: 'unknown',  // Unknown service
    16: 'critical' // Critical service
};

// Create a mapping between UI display classes and internal status classes
const UI_TO_STATUS_CLASS_MAPPING = {
    'up': ['up'],             // Host status 'up'
    'down': ['down'],         // Host status 'down'
    'warning': ['warning'],   // Service status 'warning'
    'unknown': ['unknown'],   // Both host and service 'unknown'
    'pending': ['pending'],   // Both host and service 'pending'
    'critical': ['critical'], // Service status 'critical'
    'ok': ['ok']              // Service status 'ok'
};

// Track selected status filters
let selectedHostStatuses = [];
let selectedServiceStatuses = [];

/**
 * Initialize the host and service count displays
 */
function initHostCount() {
    // Check if elements already exist
    if (document.getElementById('host-count-container')) {
        return;
    }
    
    // Create host count container
    hostCountContainer = document.createElement('div');
    hostCountContainer.id = 'host-count-container';
    hostCountContainer.className = 'host-count-container';
    
    // Create service count container
    serviceCountContainer = document.createElement('div');
    serviceCountContainer.id = 'service-count-container';
    serviceCountContainer.className = 'service-count-container';
    
    // Create wrapper for desktop status counts
    const statusCountWrapper = document.createElement('div');
    statusCountWrapper.id = 'status-count-wrapper';
    statusCountWrapper.className = 'status-count-wrapper';
    
    // Add containers to wrapper
    statusCountWrapper.appendChild(hostCountContainer);
    statusCountWrapper.appendChild(serviceCountContainer);
    
    // Add to header
    const header = document.querySelector('header');
    if (header) {
        // Find the first div in the header (which contains the logo and breadcrumbs)
        const headerFirstDiv = header.querySelector('div:first-child');
        if (headerFirstDiv) {
            // Add after the breadcrumbs
            headerFirstDiv.appendChild(statusCountWrapper);
        } else {
            // Fallback: add to header directly
            header.appendChild(statusCountWrapper);
        }
    }
    
    // For mobile view, create copies of host and service counts
    const mobileContainer = document.getElementById('mobile-status-container');
    if (mobileContainer) {
        // Create mobile host count container
        const mobileHostCountContainer = document.createElement('div');
        mobileHostCountContainer.id = 'host-count-container-mobile';
        mobileHostCountContainer.className = 'host-count-container';
        
        // Create mobile service count container
        const mobileServiceCountContainer = document.createElement('div');
        mobileServiceCountContainer.id = 'service-count-container-mobile';
        mobileServiceCountContainer.className = 'service-count-container';
        
        // Add to mobile container
        mobileContainer.appendChild(mobileHostCountContainer);
        mobileContainer.appendChild(mobileServiceCountContainer);
    }
    
    // Always show the status count
    updateVisibility();
    
    // Set interval for periodic updates. Initial update will be triggered by fetchHostsBubbles.js
    //setInterval(updateHostAndServiceCounts, STATUS_UPDATE_INTERVAL);
}

/**
 * Toggle the visibility of the status count display on mobile
 * This function is kept for compatibility but always sets isVisible to true
 */
function toggleVisibility() {
    // Always keep it visible
    isVisible = true;
    updateVisibility();
}

/**
 * Update the visibility based on current state
 * This function now always shows the status count
 */
function updateVisibility() {
    const statusCountWrapper = document.querySelector('.status-count-wrapper');
    const header = document.querySelector('header');
    const canvasContainer = document.getElementById('canvas-container');
    
    if (statusCountWrapper) {
        // Always show the status count wrapper
        statusCountWrapper.classList.add('show');
    }
    
    if (header) {
        // Always add the status-count-visible class to the header
        header.classList.add('status-count-visible');
    }
    
    if (canvasContainer) {
        // Always add the status-count-visible class to the canvas container
        canvasContainer.classList.add('status-count-visible');
    }
}

/**
 * Helper function to collect all visible host IDs in the current view
 * @returns {string} Comma-separated list of host IDs
 */
function getVisibleHostIds() {
    const visibleHosts = [];
    // Get all host bubbles that are currently styled as visible (display is not 'none')
    d3.selectAll('.host-bubble').each(function(d) {
        if (this.style.display !== 'none' && d && d.id) {
            visibleHosts.push(d.id);
        }
    });
    return visibleHosts.join(',');
}

/**
 * Update both host and service counts in one query to the database
 */
function updateHostAndServiceCounts() {
    // Base URL for get_host_status.php
    let url = 'get_host_status.php';
    const params = [];
    
    // Get visible host IDs to limit the query to relevant hosts
    const hostIds = getVisibleHostIds();
    
    // Check if hostgroup parameter exists in the URL
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('hostgroup') && urlParams.get('hostgroup') !== '') {
        params.push(`hostgroup=${encodeURIComponent(urlParams.get('hostgroup'))}`);
    }
    
    // Add host IDs if available and not empty
    if (hostIds && hostIds.length > 0) {
        params.push(`ids=${hostIds}`);
    }
    
    // Add parameters to URL
    if (params.length > 0) {
        url += `?${params.join('&')}`;
    }
    
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            // Process the data to count hosts and services by status
            processStatusData(data);
        })
        .catch(error => {
            console.error('Error fetching status data:', error);
            if (hostCountContainer) {
                hostCountContainer.innerHTML = '<span class="host-count-error">Error</span>';
            }
            if (serviceCountContainer) {
                serviceCountContainer.innerHTML = '<span class="service-count-error">Error</span>';
            }
        });
}

/**
 * Process the database status data to calculate host and service counts
 * @param {Array} data - The host status data from the API
 */
function processStatusData(data) {
    // Initialize counters for hosts
    const hostCounts = {
        up: 0,
        down: 0,
        unreachable: 0,
        pending: 0
    };
    
    // Initialize counters for services
    const serviceCounts = {
        ok: 0,
        warning: 0,
        critical: 0,
        unknown: 0,
        pending: 0
    };
    
    // Process each host's data
    data.forEach(host => {
        // Map the apmStatus to host status
        if (host.apmStatus === 'down') {
            hostCounts.down++;
        } else if (host.apmStatus === 'unreachable') {
            hostCounts.unreachable++;
        } else if (host.apmStatus === 'pending') {
            hostCounts.pending++;
        } else {
            // Any host that is not down, unreachable, or pending is considered "up"
            hostCounts.up++;
        }
        
        // Add service counts
        serviceCounts.ok += parseInt(host.ok_count) || 0;
        serviceCounts.warning += parseInt(host.warning_count) || 0;
        serviceCounts.critical += parseInt(host.critical_count) || 0;
        serviceCounts.unknown += parseInt(host.unknown_count) || 0;
        serviceCounts.pending += parseInt(host.pending_count) || 0;
    });
    
    // Always use the initial counts for display
    updateHostCountDisplay({ data: { count: hostCounts } });
    updateServiceCountDisplay({ data: { count: serviceCounts } });
}

/**
 * Toggle a host status filter
 * @param {string} status - The status to toggle
 */
function toggleHostStatusFilter(status) {
    const index = selectedHostStatuses.indexOf(status);
    
    if (index === -1) {
        // Add to selected statuses
        selectedHostStatuses.push(status);
    } else {
        // Remove from selected statuses
        selectedHostStatuses.splice(index, 1);
    }
    
    // Clear service statuses when toggling host statuses
    // to avoid confusion between host and service filtering
    selectedServiceStatuses = [];
    
    // Apply the filters
    applyStatusFilters();
    
    // Update the visual state of the counters
    updateCounterStyles();
    
    // No need to call updateHostAndServiceCounts() as we want to keep the original counts
}

/**
 * Toggle a service status filter
 * @param {string} status - The status to toggle
 */
function toggleServiceStatusFilter(status) {
    const index = selectedServiceStatuses.indexOf(status);
    
    if (index === -1) {
        // Add to selected statuses
        selectedServiceStatuses.push(status);
    } else {
        // Remove from selected statuses
        selectedServiceStatuses.splice(index, 1);
    }
    
    // Clear host statuses when toggling service statuses
    // to avoid confusion between host and service filtering
    selectedHostStatuses = [];
    
    // Apply the filters
    applyStatusFilters();
    
    // Update the visual state of the counters
    updateCounterStyles();
    
    // No need to call updateHostAndServiceCounts() as we want to keep the original counts
}

/**
 * Apply the selected status filters to the bubbles
 */
function applyStatusFilters() {
    // Check if search is active - if a search is being performed, don't interfere
    const searchBar = document.getElementById('searchBar');
    const searchMode = document.getElementById('searchMode')?.value;
    if (searchBar && searchBar.value.trim() !== '') {
        return; // Don't apply status filters when search is active
    }
    
    // If there are no filters selected, show all and return early
    if (selectedHostStatuses.length === 0 && selectedServiceStatuses.length === 0) {
        // Reset all elements to visible
        d3.selectAll('.host-bubble').style('display', 'block');
        d3.selectAll('.bubble-text').style('display', 'block');
        d3.selectAll('[class*="badge-"]').style('display', 'block');
        d3.selectAll('.parent-child-arrow').style('display', 'block');
        
        // Reset all group bubbles except "No Hostgroup" or "unknown"
        d3.selectAll('.group-bubble').each(function(groupData) {
            const hostgroup = groupData.hostgroup;
            if (hostgroup !== "No Hostgroup" && hostgroup.toLowerCase() !== "unknown") {
                d3.select(this).style('display', 'block');
                d3.selectAll('.group-text')
                    .filter(d => d.hostgroup === hostgroup)
                    .style('display', 'block');
            } else {
                d3.select(this).style('display', 'none');
                d3.selectAll('.group-text')
                    .filter(d => d.hostgroup === hostgroup)
                    .style('display', 'none');
            }
        });
        return;
    }
    
    // Filter host bubbles, labels, and badges based on selected statuses
    d3.selectAll('.host-bubble').each(function(d) {
        const bubble = this;
        let shouldDisplay = false;
        
        // Host status filtering - Show hosts if ANY selected status matches
        if (selectedHostStatuses.length > 0) {
            const bubbleClass = bubble.getAttribute('class');
            
            // Check for subnet bubbles that have the data-host-statuses-present attribute
            const hostStatusesPresentAttr = bubble.getAttribute('data-host-statuses-present');
            
            if (hostStatusesPresentAttr) {
                // This is a subnet bubble with host statuses data
                try {
                    const hostStatusesPresent = JSON.parse(hostStatusesPresentAttr);
                    
                    // Check if any of the selected host statuses is present in this subnet
                    for (const status of selectedHostStatuses) {
                        if (status === 'ok' && hostStatusesPresent.up) {
                            shouldDisplay = true;
                            break;
                        } else if (status === 'down' && hostStatusesPresent.down) {
                            shouldDisplay = true;
                            break;
                        } else if (status === 'unknown' && (hostStatusesPresent.unknown || hostStatusesPresent.unreachable)) {
                            shouldDisplay = true;
                            break;
                        } else if (status === 'pending' && hostStatusesPresent.pending) {
                            shouldDisplay = true;
                            break;
                        }
                    }
                } catch (e) {
                    console.error('Error parsing host statuses present:', e);
                }
            } else {
                // Regular host bubble - use existing logic
                // Check each selected status
                for (const status of selectedHostStatuses) {
                    if (status === 'ok') {
                        // Show hosts that are "UP" (not down, unreachable, or pending)
                        if (!bubbleClass.includes('down') && 
                            !bubbleClass.includes('unreachable') && 
                            !bubbleClass.includes('pending')) {
                            shouldDisplay = true;
                            break;  // Exit the loop, we've already decided to show this host
                        }
                    } else if (bubbleClass.includes(status)) {
                        // For other statuses (down, unreachable, unknown, pending)
                        shouldDisplay = true;
                        break;  // Exit the loop, we've already decided to show this host
                    }
                }
            }
        }
        
        // Service status filtering
        if (selectedServiceStatuses.length > 0 && !shouldDisplay) {
            // Get the service statuses from the data attribute
            let serviceStatuses = {};
            try {
                const serviceStatusesAttr = bubble.getAttribute('data-service-statuses');
                if (serviceStatusesAttr) {
                    serviceStatuses = JSON.parse(serviceStatusesAttr);
                }
            } catch (e) {
                console.error('Error parsing service statuses:', e);
            }
            
            // Check if any service status matches selected service statuses
            if (Object.keys(serviceStatuses).length > 0) {
                for (const serviceName in serviceStatuses) {
                    const serviceStatus = serviceStatuses[serviceName];
                    const serviceStatusClass = SERVICE_STATUS_CLASS_MAPPING[serviceStatus] || 'unknown';
                    
                    if (selectedServiceStatuses.includes(serviceStatusClass)) {
                        shouldDisplay = true;
                        break;
                    }
                }
            }
        }
        
        // Apply visibility to the bubble and related elements
        d3.select(bubble).style('display', shouldDisplay ? 'block' : 'none');
        d3.selectAll('.bubble-text').filter(text => text && text.id === d.id)
            .style('display', shouldDisplay ? 'block' : 'none');
        d3.select(`.badge-${d.id}`).style('display', shouldDisplay ? 'block' : 'none');
    });
    
    // Update group bubbles visibility
    d3.selectAll('.group-bubble').each(function(groupData) {
        const groupBubble = d3.select(this);
        const hostgroup = groupData.hostgroup;
        
        // Skip showing group bubble for "No Hostgroup" or "unknown"
        if (hostgroup === "No Hostgroup" || hostgroup.toLowerCase() === "unknown") {
            groupBubble.style('display', 'none');
            d3.selectAll('.group-text')
                .filter(d => d.hostgroup === hostgroup)
                .style('display', 'none');
            return;
        }
        
        const groupClassName = hostgroup.toLowerCase().replace(/\s+/g, '-');
        const allHostBubbles = d3.selectAll('.host-bubble');
        let totalHostBubbles = 0;
        let hiddenHostBubbles = 0;
        
        allHostBubbles.each(function(hostData) {
            if (hostData.hostgroups && hostData.hostgroups[0].toLowerCase().replace(/\s+/g, '-') === groupClassName) {
                totalHostBubbles++;
                if (d3.select(this).style('display') === 'none') {
                    hiddenHostBubbles++;
                }
            }
        });
        
        const shouldHideGroup = totalHostBubbles > 0 && hiddenHostBubbles === totalHostBubbles;
        groupBubble.style('display', shouldHideGroup ? 'none' : 'block');
        
        d3.selectAll('.group-text')
            .filter(d => d.hostgroup === hostgroup)
            .style('display', shouldHideGroup ? 'none' : 'block');
    });
    
    // Update arrows visibility
    d3.selectAll('.parent-child-arrow').each(function() {
        const arrowData = d3.select(this).datum();
        const parentBubble = d3.selectAll('.host-bubble').filter(d => d.id === arrowData.parentId).node();
        const childBubble = d3.selectAll('.host-bubble').filter(d => d.ip === arrowData.childIp).node();
        
        const parentVisible = parentBubble && d3.select(parentBubble).style('display') !== 'none';
        const childVisible = childBubble && d3.select(childBubble).style('display') !== 'none';
        const shouldDisplay = parentVisible && childVisible;
        
        d3.select(this).style('display', shouldDisplay ? 'block' : 'none');
    });
}

/**
 * Update the visual state of the status counters based on selection
 */
function updateCounterStyles() {
    // Update host count styles
    document.querySelectorAll('#host-count-container .host-count').forEach(counter => {
        const status = counter.getAttribute('data-status');
        const isSelected = selectedHostStatuses.includes(status);
        
        if (isSelected) {
            counter.classList.add('selected');
        } else {
            counter.classList.remove('selected');
        }
    });
    
    // Update service count styles
    document.querySelectorAll('#service-count-container .service-count').forEach(counter => {
        const status = counter.getAttribute('data-status');
        const isSelected = selectedServiceStatuses.includes(status);
        
        if (isSelected) {
            counter.classList.add('selected');
        } else {
            counter.classList.remove('selected');
        }
    });
}

/**
 * Reset all status filters
 */
function resetStatusFilters() {
    // Clear status filters
    selectedHostStatuses = [];
    selectedServiceStatuses = [];
    
    // Clear search bar and stop search interval
    const searchBar = document.getElementById('searchBar');
    if (searchBar) {
        searchBar.value = '';
    }
    // switch searchmode to hosts
    const searchMode = document.getElementById('searchMode');
    if (searchMode) {
        searchMode.value = 'hosts';
    }
    // Clear any existing search interval (defined in filtering.js)
    if (typeof searchInterval !== 'undefined' && searchInterval) {
        clearInterval(searchInterval);
        searchInterval = null;
    }
    
    // Apply the empty filters (shows all)
    applyStatusFilters();
    
    // Update the visual state of the counters
    updateCounterStyles();
    
    // No need to update the counts when resetting filters, use the original counts
}

/**
 * Update the host count display with the fetched data
 * @param {Object} data - The host count data from the API
 */
function updateHostCountDisplay(data) {
    if (!hostCountContainer) return;
    
    // Check if we have valid data
    if (!data || !data.data || !data.data.count) {
        hostCountContainer.innerHTML = '<span class="host-count-error">No Data</span>';
        return;
    }
    
    const counts = data.data.count;
    
    // Create the display HTML
    let html = '<div class="host-count-title">Hosts:</div>';
    
    // Add counts for all statuses, even those with zero counts
    for (const [status, definition] of Object.entries(HOST_STATUS_DEFINITIONS)) {
        const count = counts[status] || 0;
        const isSelected = selectedHostStatuses.includes(definition.color);
        const selectedClass = isSelected ? ' selected' : '';
        html += `<span class="host-count ${definition.color}${selectedClass}" title="${definition.name}" data-status="${definition.color}" data-type="host">${count}</span>`;
    }
    
    // Update the desktop container
    hostCountContainer.innerHTML = html;
    
    // Also update mobile container if it exists
    const mobileHostCountContainer = document.getElementById('host-count-container-mobile');
    if (mobileHostCountContainer) {
        mobileHostCountContainer.innerHTML = html;
    }
    
    // Add click handlers to the status counts (desktop)
    document.querySelectorAll('#host-count-container .host-count').forEach(counter => {
        counter.addEventListener('click', () => {
            const status = counter.getAttribute('data-status');
            toggleHostStatusFilter(status);
        });
    });
    
    // Add click handlers to the status counts (mobile)
    document.querySelectorAll('#host-count-container-mobile .host-count').forEach(counter => {
        counter.addEventListener('click', () => {
            const status = counter.getAttribute('data-status');
            toggleHostStatusFilter(status);
        });
    });
}

/**
 * Update the service count display with the fetched data
 * @param {Object} data - The service count data from the API
 */
function updateServiceCountDisplay(data) {
    if (!serviceCountContainer) return;
    
    // Check if we have valid data
    if (!data || !data.data || !data.data.count) {
        serviceCountContainer.innerHTML = '<span class="service-count-error">No Data</span>';
        return;
    }
    
    const counts = data.data.count;
    
    // Create the display HTML
    let html = '<div class="service-count-title">Services:</div>';
    
    // Add counts for all statuses, even those with zero counts
    for (const [status, definition] of Object.entries(SERVICE_STATUS_DEFINITIONS)) {
        const count = counts[status] || 0;
        const isSelected = selectedServiceStatuses.includes(definition.color);
        const selectedClass = isSelected ? ' selected' : '';
        html += `<span class="service-count ${definition.color}${selectedClass}" title="${definition.name}" data-status="${definition.color}" data-type="service">${count}</span>`;
    }
    
    // Update the desktop container
    serviceCountContainer.innerHTML = html;
    
    // Also update mobile container if it exists
    const mobileServiceCountContainer = document.getElementById('service-count-container-mobile');
    if (mobileServiceCountContainer) {
        mobileServiceCountContainer.innerHTML = html;
    }
    
    // Add reset button after services (desktop)
    const resetButton = document.createElement('span');
    resetButton.className = 'status-filter-reset';
    resetButton.innerHTML = '<i class="fa fa-refresh" aria-hidden="true"></i>';
    resetButton.title = 'Reset all filters';
    resetButton.addEventListener('click', resetStatusFilters);
    serviceCountContainer.appendChild(resetButton);
    
    // Add reset button after services (mobile)
    if (mobileServiceCountContainer) {
        const mobileResetButton = document.createElement('span');
        mobileResetButton.className = 'status-filter-reset';
        mobileResetButton.innerHTML = '<i class="fa fa-refresh" aria-hidden="true"></i>';
        mobileResetButton.title = 'Reset all filters';
        mobileResetButton.addEventListener('click', resetStatusFilters);
        mobileServiceCountContainer.appendChild(mobileResetButton);
    }
    
    // Add click handlers to the status counts (desktop)
    document.querySelectorAll('#service-count-container .service-count').forEach(counter => {
        counter.addEventListener('click', () => {
            const status = counter.getAttribute('data-status');
            toggleServiceStatusFilter(status);
        });
    });
    
    // Add click handlers to the status counts (mobile)
    document.querySelectorAll('#service-count-container-mobile .service-count').forEach(counter => {
        counter.addEventListener('click', () => {
            const status = counter.getAttribute('data-status');
            toggleServiceStatusFilter(status);
        });
    });
}

// Add CSS styles for selected counts
function addStatusCountStyles() {
    const style = document.createElement('style');
    style.innerHTML = `
        /* Host Count Display Styles */
        .host-count-container {
            display: flex;
            align-items: center;
            padding: 5px 10px;
        }
        
        .host-count-title {
            font-weight: 500;
            margin-right: 8px;
            color: #ffffff;
        }
        
        .host-count {
            cursor: pointer;
            position: relative;
            transition: transform 0.2s, box-shadow 0.2s;
            border-radius: 50%;
            padding: 0;
            margin: 0 2px;
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(0,0,0,0.2);
            color: white;
            font-size: 12px;
            font-weight: 600;
        }
        
        .host-count:hover, .service-count:hover {
            transform: scale(1.1);
            box-shadow: 0 0 5px rgba(0,0,0,0.2);
        }
        
        .host-count.selected, .service-count.selected {
            box-shadow: 0 0 0 1px white, 0 0 0 2px currentColor;
            transform: scale(1.1);
            font-weight: bold;
        }
        
        /* Service Count Display Styles */
        .service-count-container {
            display: flex;
            align-items: center;
            padding: 5px 10px;
        }
        
        .service-count-title {
            font-weight: 500;
            margin-right: 8px;
            color: #ffffff;
        }
        
        .service-count {
            cursor: pointer;
            position: relative;
            transition: transform 0.2s, box-shadow 0.2s;
            border-radius: 50%;
            padding: 0;
            margin: 0 2px;
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(0,0,0,0.2);
            color: white;
            font-size: 12px;
            font-weight: 600;
        }
        
        /* Status color classes */
        .host-count.ok, .service-count.ok {
            background-color: #4CAF50;
        }
        
        .host-count.down, .service-count.down {
            background-color: #F44336;
        }
        
        .host-count.unreachable {
            background-color: #FF9800;
        }
        
        .host-count.warning, .service-count.warning {
            background-color: #FFC107;
        }
        
        .host-count.critical, .service-count.critical {
            background-color: #F44336;
        }
        
        .host-count.unknown, .service-count.unknown {
            background-color: #64748b;
        }
        
        .host-count.pending, .service-count.pending {
            background-color: #9E9E9E;
        }
        
        .status-filter-reset {
            margin-left: 10px;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s, transform 0.2s;
            padding: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .status-filter-reset:hover {
            opacity: 1;
            transform: rotate(90deg);
            background: rgba(255,255,255,0.7);
        }
        
        .status-filter-reset i {
            color: #ffffff;
            font-size: 14px;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
    `;
    document.head.appendChild(style);
}

/**
 * Start continuous filtering to catch new elements
 */
function startContinuousFiltering() {
    // Create continuous filter interval that applies filters every millisecond
    const filterInterval = setInterval(() => {
        // Check if search is active before applying filters
        const searchBar = document.getElementById('searchBar');
        // Only apply filters if we have selected status filters
        if (!(searchBar && searchBar.value.trim() !== '') && 
            (selectedHostStatuses.length > 0 || selectedServiceStatuses.length > 0)) {
            applyStatusFilters();
        }
    }, FILTER_UPDATE_INTERVAL);
    
    // Store interval ID on window object for potential cleanup
    window.continuousFilterInterval = filterInterval;
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initHostCount();
    addStatusCountStyles();
    startContinuousFiltering(); // Start continuous filtering
}); 