/**
 * Feature Status Indicators functionality for hosts.php
 * Handles flapping, notifications, event handlers, active checks, and passive checks indicators
 */

// Global variable to store combined feature data
window.combinedFeatureData = null;

/**
 * Initialize feature status indicators
 */
function initFeatureStatusIndicators() {
    // Add CSS styles for feature indicators
    addFeatureIndicatorStyles();
    
    // Set up click handlers for indicators
    setupIndicatorClickHandlers();
    
    // Update indicators initially
    updateFeatureStatusIndicators();
    
    // Set up periodic updates (every 30 seconds)
    setInterval(updateFeatureStatusIndicators, 30000);
    
    // Handle mobile responsiveness
    handleMobileResponsiveness();
}

/**
 * Add CSS styles for feature status indicators
 */
function addFeatureIndicatorStyles() {
    const style = document.createElement('style');
    style.innerHTML = `
        /* Host Status Indicators */
        .hostlist-status-indicators {
            display: flex;
            background-color: transparent;
            padding: 0;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            margin-left: 15px;
            height: 26px;
        }
        
        .status-indicator {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.2s, box-shadow 0.2s;
            background-color: rgba(255,255,255,0.08);
            box-shadow: 0 0 0 1px rgba(255,255,255,0.15);
        }
        
        .status-indicator:hover {
            background-color: rgba(255,255,255,0.18);
            box-shadow: 0 0 0 1px rgba(255,255,255,0.25);
        }
        
        .status-indicator i {
            color: #ccc;
            font-size: 13px;
            line-height: 26px;
        }
        
        /* Badge Styles */
        .indicator-badge {
            position: absolute;
            top: 0px;
            right: 0px;
            transform: translate(40%, -40%);
            background-color: #F44336;
            color: white;
            border-radius: 50%;
            font-size: 9px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 15px;
            height: 15px;
            text-align: center;
            display: none;
            border: 1px solid #403c3c;
            box-sizing: border-box;
        }
        
        .status-indicator.active i {
            color: #b4ca45;
        }
        .status-indicator.active .indicator-badge {
            display: none;
        }
        
        .status-indicator.inactive i {
            color: #F44336;
        }
        .status-indicator.inactive .indicator-badge {
            background-color: #F44336;
            display: flex;
        }
        
        .status-indicator.warning i {
            color: #ffc107;
        }
        .status-indicator.warning .indicator-badge {
            background-color: #ffc107;
            color: #333;
            display: flex;
        }
        
        .status-indicator.error i {
            color: #F44336;
        }
        
        /* Host Status Popover */
        .status-hosts-popover {
            position: absolute;
            background-color: #3a3a3a;
            color: #f0f0f0;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            padding: 8px 12px;
            min-width: 200px;
            max-width: 300px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 9999;
            display: none;
            border: 1px solid #555;
        }
        
        .status-hosts-popover h4 {
            margin: 0 0 8px 0;
            padding-bottom: 6px;
            border-bottom: 1px solid #555;
            font-size: 13px;
            color: #ffffff;
            text-align: center;
            padding-right: 20px;
        }
        
        .status-hosts-popover ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        
        .status-hosts-popover li {
            padding: 8px 10px;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.15s ease, color 0.15s ease;
            font-size: 12px;
            color: #e0e0e0;
            margin-bottom: 2px;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .status-hosts-popover li:last-child {
            border-bottom: none;
        }
        
        .status-hosts-popover li:hover {
            background-color: rgba(255,255,255,0.1);
            color: #ffffff;
        }
        
        .status-hosts-popover .close-popover {
            position: absolute;
            top: 6px;
            right: 8px;
            cursor: pointer;
            color: #aaa;
            font-size: 16px;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            border-radius: 50%;
        }
        
        .status-hosts-popover .close-popover:hover {
            color: #fff;
            background-color: rgba(255,255,255,0.1);
        }
        
        /* Mobile Status Container Styles */
        @media (max-width: 768px) {
            /* Status indicators in mobile screens */
            .hostlist-status-indicators {
                margin-top: 5px;
                gap: 4px;
                flex-wrap: wrap;
                justify-content: flex-start;
            }
            
            .status-indicator {
                font-size: 10px !important;
                width: 18px !important;
                height: 18px !important;
                padding: 2px 4px !important;
            }
            
            .status-indicator i {
                font-size: 11px !important;
            }
            
            /* Adjust status indicators in mobile container */
            .mobile-status-container .hostlist-status-indicators {
                margin-left: 0;
                margin-top: 5px;
                width: 100%;
                justify-content: center;
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * Set up click handlers for feature status indicators
 */
function setupIndicatorClickHandlers() {
    const indicators = [
        'feature-flap-detection-status',
        'feature-notifications-status', 
        'feature-event-handlers-status',
        'feature-active-checks-status',
        'feature-passive-checks-status',
        'feature-acknowledged-problems-status'
    ];
    
    indicators.forEach(indicatorId => {
        // Desktop indicators
        const indicator = document.getElementById(indicatorId);
        if (indicator) {
            indicator.addEventListener('click', (e) => {
                e.stopPropagation();
                const featureType = indicatorId.replace('feature-', '').replace('-status', '');
                showFeatureIssuePopover(featureType, indicator);
            });
        }
        
        // Mobile indicators
        const mobileIndicator = document.getElementById(indicatorId + '-mobile');
        if (mobileIndicator) {
            mobileIndicator.addEventListener('click', (e) => {
                e.stopPropagation();
                const featureType = indicatorId.replace('feature-', '').replace('-status', '');
                showFeatureIssuePopover(featureType, mobileIndicator);
            });
        }
    });
    
    // Close popover when clicking outside
    document.addEventListener('click', () => {
        const popover = document.getElementById('feature-issue-popover');
        if (popover) {
            popover.style.display = 'none';
        }
    });
}

/**
 * Fetch host object data from Nagios API
 */
async function fetchHostObjectData() {
    try {
        const apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true`;
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.result.type_code !== 0) {
            throw new Error(`API error: ${data.result.message}`);
        }
        
        return data;
    } catch (error) {
        console.error('Error fetching host object data:', error);
        throw error;
    }
}

/**
 * Update feature status indicators by fetching data from Nagios APIs
 */
async function updateFeatureStatusIndicators() {
    try {
        // Fetch all necessary data
        const hostObjectDataPromise = fetchHostObjectData(); // For host IPs, subnets, and details
        const hostStatusUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=hostlist&details=true`;
        const serviceStatusUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=servicelist&details=true`;

        const [hostObjectData, hostStatusResponse, serviceStatusResponse] = await Promise.all([
            hostObjectDataPromise,
            fetch(hostStatusUrl),
            fetch(serviceStatusUrl)
        ]);

        if (!hostStatusResponse.ok) throw new Error(`HTTP error (host status): ${hostStatusResponse.status}`);
        if (!serviceStatusResponse.ok) throw new Error(`HTTP error (service status): ${serviceStatusResponse.status}`);

        const hostStatusDataAll = await hostStatusResponse.json();
        const serviceStatusDataAll = await serviceStatusResponse.json();

        if (hostStatusDataAll.result.type_code !== 0) throw new Error(`API error (host status): ${hostStatusDataAll.result.message}`);
        if (serviceStatusDataAll.result.type_code !== 0) throw new Error(`API error (service status): ${serviceStatusDataAll.result.message}`);
        
        const combinedFeatures = {
            'flap-detection': { enabled: [], disabled: [], flapping: [], total_relevant: 0 },
            'notifications': { enabled: [], disabled: [], total_relevant: 0 },
            'event-handlers': { enabled: [], disabled: [], total_relevant: 0 },
            'active-checks': { enabled: [], disabled: [], total_relevant: 0 },
            'passive-checks': { enabled: [], disabled: [], total_relevant: 0 },
            'acknowledged-problems': { acknowledged: [], total_relevant: 0 }
        };

        // Process host data
        if (hostStatusDataAll.data && hostStatusDataAll.data.hostlist) {
            for (const [hostName, hostStatus] of Object.entries(hostStatusDataAll.data.hostlist)) {
                const hostDetails = hostObjectData.data.hostlist ? hostObjectData.data.hostlist[hostName] : null;
                const hostItem = {
                    type: 'host',
                    name: hostName,
                    display_name: hostName, 
                    address: hostDetails?.address || hostName,
                    subnet: hostDetails?.subnet || 'External',
                    // raw statuses for robust checking
                    is_flapping: Number(hostStatus.is_flapping) === 1,
                    flap_detection_enabled: Number(hostStatus.flap_detection_enabled) === 1,
                    notifications_enabled: Number(hostStatus.notifications_enabled) === 1,
                    event_handler_enabled: Number(hostStatus.event_handler_enabled) === 1,
                    active_checks_enabled: Number(hostStatus.checks_enabled) === 1,
                    passive_checks_enabled: Number(hostStatus.accept_passive_checks) === 1,
                    problem_has_been_acknowledged: Number(hostStatus.problem_has_been_acknowledged) === 1
                };

                combinedFeatures['flap-detection'].total_relevant++;
                if (hostItem.flap_detection_enabled) {
                    combinedFeatures['flap-detection'].enabled.push(hostItem);
                    if (hostItem.is_flapping) combinedFeatures['flap-detection'].flapping.push(hostItem);
                } else combinedFeatures['flap-detection'].disabled.push(hostItem);

                combinedFeatures['notifications'].total_relevant++;
                if (hostItem.notifications_enabled) combinedFeatures['notifications'].enabled.push(hostItem);
                else combinedFeatures['notifications'].disabled.push(hostItem);

                combinedFeatures['event-handlers'].total_relevant++;
                if (hostItem.event_handler_enabled) combinedFeatures['event-handlers'].enabled.push(hostItem);
                else combinedFeatures['event-handlers'].disabled.push(hostItem);

                combinedFeatures['active-checks'].total_relevant++;
                if (hostItem.active_checks_enabled) combinedFeatures['active-checks'].enabled.push(hostItem);
                else combinedFeatures['active-checks'].disabled.push(hostItem);

                combinedFeatures['passive-checks'].total_relevant++;
                if (hostItem.passive_checks_enabled) combinedFeatures['passive-checks'].enabled.push(hostItem);
                else combinedFeatures['passive-checks'].disabled.push(hostItem);

                combinedFeatures['acknowledged-problems'].total_relevant++;
                if (hostItem.problem_has_been_acknowledged) {
                    combinedFeatures['acknowledged-problems'].acknowledged.push(hostItem);
                }
            }
        }

        // Process service data
        if (serviceStatusDataAll.data && serviceStatusDataAll.data.servicelist) {
            for (const [hostName, services] of Object.entries(serviceStatusDataAll.data.servicelist)) {
                const hostDetails = hostObjectData.data.hostlist ? hostObjectData.data.hostlist[hostName] : null;
                for (const [serviceName, serviceStatus] of Object.entries(services)) {
                    const serviceItem = {
                        type: 'service',
                        host_name: hostName,
                        service_name: serviceName,
                        display_name: `${hostName} - ${serviceName}`,
                        host_address: hostDetails?.address || hostName,
                        host_subnet: hostDetails?.subnet || 'External',
                        // raw statuses for robust checking
                        is_flapping: Number(serviceStatus.is_flapping) === 1,
                        flap_detection_enabled: Number(serviceStatus.flap_detection_enabled) === 1,
                        notifications_enabled: Number(serviceStatus.notifications_enabled) === 1,
                        event_handler_enabled: Number(serviceStatus.event_handler_enabled) === 1,
                        active_checks_enabled: Number(serviceStatus.checks_enabled) === 1,
                        passive_checks_enabled: Number(serviceStatus.accept_passive_checks) === 1,
                        problem_has_been_acknowledged: Number(serviceStatus.problem_has_been_acknowledged) === 1
                    };

                    combinedFeatures['flap-detection'].total_relevant++;
                    if (serviceItem.flap_detection_enabled) {
                        combinedFeatures['flap-detection'].enabled.push(serviceItem);
                        if (serviceItem.is_flapping) combinedFeatures['flap-detection'].flapping.push(serviceItem);
                    } else combinedFeatures['flap-detection'].disabled.push(serviceItem);

                    combinedFeatures['notifications'].total_relevant++;
                    if (serviceItem.notifications_enabled) combinedFeatures['notifications'].enabled.push(serviceItem);
                    else combinedFeatures['notifications'].disabled.push(serviceItem);

                    combinedFeatures['event-handlers'].total_relevant++;
                    if (serviceItem.event_handler_enabled) combinedFeatures['event-handlers'].enabled.push(serviceItem);
                    else combinedFeatures['event-handlers'].disabled.push(serviceItem);

                    combinedFeatures['active-checks'].total_relevant++;
                    if (serviceItem.active_checks_enabled) combinedFeatures['active-checks'].enabled.push(serviceItem);
                    else combinedFeatures['active-checks'].disabled.push(serviceItem);

                    combinedFeatures['passive-checks'].total_relevant++;
                    if (serviceItem.passive_checks_enabled) combinedFeatures['passive-checks'].enabled.push(serviceItem);
                    else combinedFeatures['passive-checks'].disabled.push(serviceItem);

                    combinedFeatures['acknowledged-problems'].total_relevant++;
                    if (serviceItem.problem_has_been_acknowledged) {
                        combinedFeatures['acknowledged-problems'].acknowledged.push(serviceItem);
                    }
                }
            }
        }
        
        // Update indicators based on combined data
        updateIndicator('feature-flap-detection', combinedFeatures['flap-detection'], combinedFeatures['flap-detection'].flapping.length > 0);
        updateIndicator('feature-notifications', combinedFeatures['notifications']);
        updateIndicator('feature-event-handlers', combinedFeatures['event-handlers']);
        updateIndicator('feature-active-checks', combinedFeatures['active-checks']);
        updateIndicator('feature-passive-checks', combinedFeatures['passive-checks']);
        updateIndicator('feature-acknowledged-problems', combinedFeatures['acknowledged-problems']);
        
        window.combinedFeatureData = combinedFeatures;
        
    } catch (error) {
        console.error('Error updating feature status indicators:', error);
        document.querySelectorAll('.status-indicator[id^="feature-"]').forEach(indicator => {
            indicator.className = 'status-indicator error';
            const badge = indicator.querySelector('.indicator-badge');
            if (badge) {
                badge.textContent = 'ERR';
                badge.style.display = 'flex';
            }
        });
    }
}

/**
 * Update a specific indicator based on combined host and service data
 */
function updateIndicator(indicatorIdBase, data, isFlappingWarning = false) { 
    const indicators = [
        document.getElementById(`${indicatorIdBase}-status`),
        document.getElementById(`${indicatorIdBase}-status-mobile`)
    ];
    
    indicators.forEach(indicator => {
        if (!indicator) return;
        
        const badge = indicator.querySelector('.indicator-badge');
        
        indicator.className = 'status-indicator'; 
        badge.style.display = 'none';
        badge.textContent = '';

        if (indicatorIdBase === 'feature-flap-detection') {
            const flappingCount = data.flapping ? data.flapping.length : 0;
            const disabledFlapDetectionCount = data.disabled ? data.disabled.length : 0;
            const totalFlapProblems = flappingCount + disabledFlapDetectionCount;

            if (totalFlapProblems > 0) {
                indicator.classList.add('warning');
                badge.textContent = totalFlapProblems;
                badge.style.display = 'flex';
                indicator.style.display = 'flex'; // Show indicator when there are issues
            } else {
                indicator.style.display = 'none'; // Hide indicator when everything is fine
            }
        } else if (indicatorIdBase === 'feature-acknowledged-problems') {
            const acknowledgedCount = data.acknowledged ? data.acknowledged.length : 0;
            
            if (acknowledgedCount > 0) {
                indicator.classList.add('warning');
                badge.textContent = acknowledgedCount;
                badge.style.display = 'flex';
                indicator.style.display = 'flex'; // Show indicator when there are acknowledged problems
            } else {
                indicator.style.display = 'none'; // Hide indicator when no problems are acknowledged
            }
        } else {
            // Original logic for other indicators (notifications, event handlers, etc.)
            const disabledCount = data.disabled.length;
            // total_relevant: total number of hosts + services considered for this feature.
            // enabled.length: number of items where feature is explicitly enabled and not problematic.

            if (disabledCount === 0) { // No items have this feature disabled
                indicator.style.display = 'none'; // Hide indicator when everything is fine
            } else if (disabledCount > 0 && data.total_relevant > 0 && disabledCount === data.total_relevant) { // All relevant items have this feature disabled
                indicator.classList.add('inactive');
                badge.textContent = disabledCount;
                badge.style.display = 'flex';
                indicator.style.display = 'flex'; // Show indicator when there are issues
            } else if (disabledCount > 0) { // Some items have this feature disabled
                indicator.classList.add('warning');
                badge.textContent = disabledCount;
                badge.style.display = 'flex';
                indicator.style.display = 'flex'; // Show indicator when there are issues
            } else if (data.total_relevant === 0) { // No relevant items found for this feature
                indicator.style.display = 'none'; // Hide indicator when no relevant data
            } else {
                // Default to hidden if no other condition met
                indicator.style.display = 'none';
            }
        }
    });
}

/**
 * Show popover for feature issues (combined hosts and services)
 */
function showFeatureIssuePopover(type, indicatorElement) { 
    const popover = document.getElementById('feature-issue-popover');
    const popoverTitleEl = document.getElementById('popover-title');
    const popoverListEl = document.getElementById('popover-list');
    const badge = indicatorElement.querySelector('.indicator-badge');

    if (badge.style.display === 'none' || !badge.textContent) {
        popover.style.display = 'none';
        return;
    }
    
    popoverListEl.innerHTML = ''; 
    
    let featureName = '';
    switch(type) {
        case 'flap-detection': featureName = 'Flap Detection'; break;
        case 'notifications': featureName = 'Notifications'; break;
        case 'event-handlers': featureName = 'Event Handlers'; break;
        case 'active-checks': featureName = 'Active Checks'; break;
        case 'passive-checks': featureName = 'Passive Checks'; break;
        case 'acknowledged-problems': featureName = 'Acknowledged Problems'; break;
        default: featureName = type; 
    }

    const featureData = window.combinedFeatureData;
    if (!featureData || !featureData[type]) {
        popoverListEl.innerHTML = '<li>Data not available.</li>';
        popoverTitleEl.textContent = `${featureName} - Error`;
        positionPopover(indicatorElement);
        return;
    }
    
    const currentTypeData = featureData[type];
    const itemsToDisplay = [];
    let listTitle = '';

    if (type === 'flap-detection') {
        const flappingItems = currentTypeData.flapping || [];
        const disabledDetectionItems = currentTypeData.disabled || [];
        const totalProblems = flappingItems.length + disabledDetectionItems.length;
        listTitle = `Flap Detection Issues (${totalProblems})`;

        flappingItems.forEach(item => {
            itemsToDisplay.push({ ...item, issue_type: 'Currently Flapping' });
        });
        disabledDetectionItems.forEach(item => {
            itemsToDisplay.push({ ...item, issue_type: 'Flap Detection Disabled' });
        });

    } else if (type === 'acknowledged-problems') {
        const acknowledgedItems = currentTypeData.acknowledged || [];
        const count = acknowledgedItems.length;
        listTitle = `Acknowledged Problems (${count})`;
        itemsToDisplay.push(...acknowledgedItems.map(item => ({ ...item, issue_type: 'Problem Acknowledged' })));

    } else if (indicatorElement.classList.contains('inactive') || indicatorElement.classList.contains('warning')) {
        const count = parseInt(badge.textContent) || 0;
        listTitle = `Items with ${featureName} Disabled (${count})`;
        itemsToDisplay.push(...(currentTypeData.disabled || []).map(item => ({ ...item, issue_type: `${featureName} Disabled` })));
    } else {
        popover.style.display = 'none';
        return;
    }

    popoverTitleEl.textContent = listTitle;
    if (itemsToDisplay.length === 0) {
        popoverListEl.innerHTML = `<li>No relevant items found.</li>`;
    } else {
        itemsToDisplay.forEach(item => {
            const li = document.createElement('li');
            let displayText = '';
            if (item.type === 'host') {
                displayText = `[Host] ${item.display_name} (${item.address})`;
            } else { // service
                displayText = `[Service] ${item.display_name}`;
            }
            // Add issue type for flap detection or generic disabled message
            if (item.issue_type) {
                displayText += ` - ${item.issue_type}`;
            }

            li.textContent = displayText;
            li.onclick = function() {
                if (item.type === 'host') {
                    openHostInModal(item.name, item.address, item.subnet);
                } else { // service
                    openServiceInModal(item.host_name, item.host_address, item.host_subnet, item.service_name);
                }
                popover.style.display = 'none';
            };
            popoverListEl.appendChild(li);
        });
    }
    
    // Position the popover relative to the indicator element
    positionPopover(indicatorElement);
}

/**
 * Position the popover relative to the indicator element
 */
function positionPopover(indicatorElement) {
    const popover = document.getElementById('feature-issue-popover');
    if (!popover || !indicatorElement) return;
    
    // Check if we're on mobile
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        // For mobile: position below the status indicators container
        const statusIndicatorsContainer = document.querySelector('.mobile-status-container .hostlist-status-indicators') || 
                                         document.querySelector('.hostlist-status-indicators');
        
        if (statusIndicatorsContainer) {
            const containerRect = statusIndicatorsContainer.getBoundingClientRect();
            popover.style.position = 'fixed';
            popover.style.top = (containerRect.bottom + window.scrollY + 10) + 'px'; // 10px gap below indicators
            popover.style.left = '50%';
            popover.style.transform = 'translateX(-50%)'; // Center horizontally only
            popover.style.maxHeight = '60vh'; // Reduced to leave more space
            popover.style.zIndex = '10000'; // Ensure it's on top
            popover.style.display = 'block';
        } else {
            // Fallback to position below the clicked indicator
            const rect = indicatorElement.getBoundingClientRect();
            popover.style.position = 'fixed';
            popover.style.top = (rect.bottom + window.scrollY + 10) + 'px';
            popover.style.left = '50%';
            popover.style.transform = 'translateX(-50%)';
            popover.style.maxHeight = '60vh';
            popover.style.zIndex = '10000';
            popover.style.display = 'block';
        }
    } else {
        // For desktop: position directly below the indicator
        const rect = indicatorElement.getBoundingClientRect();
        
        // Position directly below indicator with minimal gap
        popover.style.position = 'absolute';
        popover.style.top = (rect.bottom + window.scrollY + 5) + 'px'; // Added 5px gap
        popover.style.left = (rect.left + window.scrollX) + 'px';
        popover.style.transform = 'none';
        popover.style.zIndex = '10000'; // Ensure it's on top
        popover.style.display = 'block';
        
        // Make sure popover isn't cut off at screen edges
        const popoverRect = popover.getBoundingClientRect();
        if (popoverRect.right > window.innerWidth) {
            popover.style.left = (window.innerWidth - popoverRect.width - 10) + 'px';
        }
        
        // Check bottom edge
        if (popoverRect.bottom > window.innerHeight) {
            // Place above the indicator instead
            popover.style.top = (rect.top + window.scrollY - popoverRect.height - 5) + 'px';
        }
    }
}

/**
 * Handle mobile responsiveness for feature status indicators
 */
function handleMobileResponsiveness() {
    function updateMobileDisplay() {
        const isMobile = window.innerWidth <= 768;
        const mobileContainer = document.getElementById('mobile-status-container');
        const canvasContainer = document.getElementById('canvas-container');
        const header = document.querySelector('header');
        
        if (isMobile && mobileContainer) {
            // Show mobile container
            mobileContainer.style.display = 'flex';
            
            // Hide desktop status count wrapper
            const statusCountWrapper = document.getElementById('status-count-wrapper');
            if (statusCountWrapper) {
                statusCountWrapper.style.display = 'none';
            }
            
            // Adjust canvas container position to account for mobile status container
            if (canvasContainer && header) {
                const headerHeight = header.offsetHeight;
                const mobileContainerHeight = mobileContainer.offsetHeight;
                canvasContainer.style.top = `${headerHeight + mobileContainerHeight}px`;
                canvasContainer.style.height = `calc(100vh - ${headerHeight + mobileContainerHeight}px)`;
            }
        } else {
            // Hide mobile container in desktop view
            if (mobileContainer) {
                mobileContainer.style.display = 'none';
            }
            
            // Show desktop status count wrapper
            const statusCountWrapper = document.getElementById('status-count-wrapper');
            if (statusCountWrapper) {
                statusCountWrapper.style.display = 'flex';
            }
            
            // Reset canvas container position
            if (canvasContainer && header) {
                const headerHeight = header.offsetHeight;
                canvasContainer.style.top = `${headerHeight}px`;
                canvasContainer.style.height = `calc(100vh - ${headerHeight}px)`;
            }
        }
    }
    
    // Initial check
    updateMobileDisplay();
    
    // Listen for resize events
    window.addEventListener('resize', updateMobileDisplay);
    
    // Also update when mobile container content changes
    const observer = new MutationObserver(updateMobileDisplay);
    const mobileContainer = document.getElementById('mobile-status-container');
    if (mobileContainer) {
        observer.observe(mobileContainer, { childList: true, subtree: true, attributes: true });
    }
}

/**
 * Helper function to open host in modal (should be available from other scripts)
 */
function openHostInModal(hostname, ip, subnet) {
    // Use the same modal opening approach as in fetchHostsBubbles.js
    const urlParams = new URLSearchParams(window.location.search);
    const infraParam = urlParams.get('infra');
    const url = `host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(infraParam)}&hostip=${encodeURIComponent(ip)}&subnet=${encodeURIComponent(subnet || 'External')}`;
    
    // Check if global showModal function exists (from hosts.php)
    if (typeof window.showModal === 'function') {
        // If the global function exists, call it directly with host type
        window.showModal(url, 'host');
    } else {
        // If no global showModal function, implement full modal opening logic here
        const modal = document.getElementById('infoModal');
        const iframe = document.getElementById('modal-frame') || document.getElementById('iframeModal-frame');
        const modalBody = document.getElementById('iframeModal-content');
        
        if (modal && iframe) {
            // Reset iframe
            modal.classList.remove('loaded');
            iframe.style.display = 'none';
            iframe.src = url;
            
            // Show modal
            modal.style.display = 'block';
            modal.classList.add('show');
            
            // Set attribute to open host modal
            iframe.setAttribute('data-open-host-modal', 'true');
            
            // Set up onload handler to open the host modal within the iframe
            iframe.onload = function() {
                const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                const navElement = iframeDocument.querySelector("nav");
                if (navElement) {
                    navElement.style.display = "none";
                }
                
                // Try to trigger a click on the host card to open host modal
                setTimeout(() => {
                    try {
                        const hostCard = iframeDocument.getElementById('host-card');
                        if (hostCard) {
                            hostCard.click();
                        }
                    } catch (e) {
                        console.error('Failed to auto-open host modal:', e);
                    }
                    iframe.removeAttribute('data-open-host-modal');
                }, 500);
                
                // Complete loading
                modal.classList.add('loaded');
                iframe.style.display = 'block';
            };
        } else {
            window.location.href = url;
        }
    }
}

/**
 * Helper function to open service in modal
 */
function openServiceInModal(hostname, hostAddress, hostSubnet, serviceName) {
    // For services, we need to open the host.php page and then trigger the service modal
    const urlParams = new URLSearchParams(window.location.search);
    const infraParam = urlParams.get('infra');
    const url = `host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(infraParam)}&hostip=${encodeURIComponent(hostAddress)}&subnet=${encodeURIComponent(hostSubnet || 'External')}`;
    
    // Check if global showModal function exists (from hosts.php)
    if (typeof window.showModal === 'function') {
        // If the global function exists, call it directly with service parameters
        window.showModal(url, 'service', serviceName);
    } else {
        // If no global showModal function, implement full modal opening logic here
        const modal = document.getElementById('infoModal');
        const iframe = document.getElementById('modal-frame') || document.getElementById('iframeModal-frame');
        const modalBody = document.getElementById('iframeModal-content');
        
        if (modal && iframe) {
            // Reset iframe
            modal.classList.remove('loaded');
            iframe.style.display = 'none';
            iframe.src = url;
            
            // Show modal
            modal.style.display = 'block';
            modal.classList.add('show');
            
            // Set attributes to open service modal
            iframe.setAttribute('data-open-service-modal', 'true');
            iframe.setAttribute('data-service-name', serviceName);
            
            // Set up onload handler to open the service modal within the iframe
            iframe.onload = function() {
                const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                const navElement = iframeDocument.querySelector("nav");
                if (navElement) {
                    navElement.style.display = "none";
                }
                
                // Try to trigger a click on the service card
                setTimeout(() => {
                    try {
                        const encodedServiceName = serviceName.replace(/\s+/g, '+');
                        const serviceCard = iframeDocument.querySelector(`.service-card[data-service="${encodedServiceName}"]`);
                        if (serviceCard) {
                            serviceCard.click();
                        }
                    } catch (e) {
                        console.error('Failed to auto-open service modal:', e);
                    }
                    iframe.removeAttribute('data-open-service-modal');
                    iframe.removeAttribute('data-service-name');
                }, 500);
                
                // Complete loading
                modal.classList.add('loaded');
                iframe.style.display = 'block';
            };
        } else {
            window.location.href = url;
        }
    }
} 