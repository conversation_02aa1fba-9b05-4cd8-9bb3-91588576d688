const statusMappings = {
    host: { 1: 'pending', 2: 'ok', 4: 'down', 8: 'unknown' },
    service: { 1: 'pending', 4: 'warning', 8: 'unknown', 16: 'critical' }
};

// Status cache to reduce redundant API calls
const statusCache = {
    hosts: new Map(), // Map of hostname/IP to status data
    hostnames: new Map(), // Map of IP to hostname
    expiry: 60000,    // Cache lifetime in ms (1 minute)
    getHostStatus: function(hostId) {
        const entry = this.hosts.get(hostId);
        if (entry && (Date.now() - entry.timestamp < this.expiry)) {
            return entry.data;
        }
        return null;
    },
    setHostStatus: function(hostId, statusData) {
        this.hosts.set(hostId, {
            data: statusData,
            timestamp: Date.now()
        });
    },
    getHostname: function(ip) {
        const entry = this.hostnames.get(ip);
        if (entry && (Date.now() - entry.timestamp < this.expiry)) {
            return entry.hostname;
        }
        return null;
    },
    setHostname: function(ip, hostname) {
        this.hostnames.set(ip, {
            hostname: hostname,
            timestamp: Date.now()
        });
    }
};

// Helper function to process a single host with retries
async function processHostWithRetries(bubble, maxRetries = 2, retryDelay = 500) {
    const d = d3.select(bubble).datum();
    if (!d || d.apmStatus === 'ask') return; // Skip if no data or apmStatus is 'ask'
    
    let retries = 0;
    let success = false;
    
    while (!success && retries <= maxRetries) {
        try {
            const hostname = d.hostname;
            const ip = d.ip;
            const cacheKey = `${ip}-${hostname}`;
            
            // Try to get status from cache first
            let statusData = statusCache.getHostStatus(cacheKey);
            
            if (!statusData) {
                // Cache miss - fetch from API
                let realHostName;
                try {
                    realHostName = (await getHostnameByIP(ip)) ?? hostname;
                } catch (error) {
                    console.error(`Error resolving hostname for IP ${ip} (attempt ${retries+1}/${maxRetries+1}): ${error.message}`);
                    throw error; // Rethrow to trigger retry
                }

                statusData = await getHostStatusClass(realHostName);
                
                // Store in cache for future use
                statusCache.setHostStatus(cacheKey, statusData);
            }
            
            const { bubbleClass, criticalCount, hostStatus, serviceStatuses } = statusData;
            
            // Always update the database status
            await updateApmStatus(d.id, bubbleClass);

            // Calculate total non-OK services from serviceStatuses
            const nonOkCount = Object.values(serviceStatuses).filter(status => status !== 2 && status !== 0).length;

            // Update the bubble's class and attributes
            d3.select(bubble)
                .attr("class", `host-bubble ${bubbleClass}`)
                .attr("data-host-status", hostStatus)
                .attr("data-service-statuses", JSON.stringify(serviceStatuses));

            // Add or update the badge as a sibling in the same parent <g>
            let badge = d3.select(bubble.parentNode).select(`.badge-${d.id}`);
            if (badge.empty()) {
                badge = d3.select(bubble.parentNode)
                    .append("g")
                    .attr("class", `badge badge-${d.id}`)
                    .attr("transform", `translate(${d.x + d.size * 0.7}, ${d.y - d.size * 0.7})`);

                // Add the badge circle (mobile app style: small red circle)
                badge.append("circle")
                    .attr("r", 10)
                    .style("fill", "#ff4444")
                    .style("stroke", "#fff")
                    .style("stroke-width", "1px");

                // Add the badge text (white number centered)
                badge.append("text")
                    .attr("text-anchor", "middle")
                    .attr("dy", ".35em")
                    .style("font-size", "12px")
                    .style("font-weight", "bold")
                    .style("fill", "#fff");
            }

            // Update badge content and visibility - show all non-OK services
            if (nonOkCount > 0) {
                badge.select("text").text(nonOkCount);
                badge.attr("visibility", "visible");
            } else {
                badge.attr("visibility", "hidden");
            }

            // Initial badge position (updated in ticked)
            badge.attr("transform", `translate(${d.x + d.size * 0.7}, ${d.y - d.size * 0.7})`);
            
            success = true; // If we get here, everything worked
        } catch (error) {
            retries++;
            if (retries <= maxRetries) {
                console.warn(`Retrying host ${d.id} (attempt ${retries}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            } else {
                console.error(`Failed to process bubble ${d?.id} after ${maxRetries+1} attempts: ${error.message}`);
            }
        }
    }
    
    return success;
}

/*
Updates the status of all bubble elements in real-time
Uses a controlled batch processing approach with database queries
instead of direct API calls.
*/
// Track which bubbles are currently being processed
const processingBubbles = new Set();

async function realTimeBubbleStatus(svg, hostData, retries = 3, delayMs = 2000) {
    try {
        // If we have previously removed some hosts, filter them out so that
        // D3 doesn\'t attempt to recreate their bubbles on the next check.
        const dataToUse = (window.removedHostIds && window.removedHostIds.size > 0)
            ? hostData.filter(h => h && !window.removedHostIds.has(h.id))
            : hostData;

        const hostBubbles = svg.selectAll(".host-bubble").data(dataToUse);
        
        // TRACK INITIAL NOT-ADDED HOSTS ------------------------------------
        // If this is the first time we run, remember whether any hosts were
        // in the "not-added" state so we can refresh the layout once they
        // have all transitioned to a real status.
        if (typeof window.hadNotAddedHostsInitially === 'undefined') {
            const notAddedSelection = hostBubbles.filter('.not-added');
            window.hadNotAddedHostsInitially = notAddedSelection.size() > 0;
            // Keep track of which specific hosts started out as not-added
            window.initialNotAddedHostIds = new Set(
                notAddedSelection.data().map(d => d && d.id)
            );
        }
        // -------------------------------------------------------------------
        
        const bubbleNodes = hostBubbles.nodes();
        
        // Filter out bubbles that are currently being processed
        const bubblesToProcess = bubbleNodes.filter(bubble => {
            const d = d3.select(bubble).datum();
            return d && !processingBubbles.has(d.id);
        });
        
        if (bubblesToProcess.length === 0) {
            console.log("No bubbles to process or all are already being processed");
            return;
        }
        
        // Configuration for batch processing
        const batchSize = 100;
        const batchDelay = 300;
        
        // Fetch status data for all hosts in a single request
        const statusData = await fetchHostStatusFromDatabase();
        
        // Process bubbles in controlled batches
        for (let i = 0; i < bubblesToProcess.length; i += batchSize) {
            const batch = bubblesToProcess.slice(i, i + batchSize);
            const batchPromises = batch.map(async (bubble, batchIndex) => {
                const d = d3.select(bubble).datum();
                if (!d) return false;
                
                // Mark this bubble as being processed
                processingBubbles.add(d.id);
                
                try {
                    // Process this host with the pre-fetched data
                    await updateBubbleFromDatabase(bubble, statusData);
                    return true;
                } finally {
                    // Remove from processing set when done
                    processingBubbles.delete(d.id);
                }
            });
            
            // Wait for current batch to complete
            await Promise.all(batchPromises);
            
            // Add delay between batches
            if (i + batchSize < bubblesToProcess.length) {
                await new Promise(resolve => setTimeout(resolve, batchDelay));
            }
        }
        
        console.log("Bubble status updated from database");
        
        // REFRESH WHEN LAST NOT-ADDED HOST IS RESOLVED -----------------------
        if (window.hadNotAddedHostsInitially) {
            const remainingNotAdded = hostBubbles.filter('.not-added').size();
            // Only refresh if at least one of the originally not-added hosts still exists (i.e., it was resolved, not removed)
            const stillPresentFromInitial = window.initialNotAddedHostIds && window.initialNotAddedHostIds.size > 0;
            if (remainingNotAdded === 0 && stillPresentFromInitial) {
                // Prevent recursive reloads
                window.hadNotAddedHostsInitially = false;

                // Notify user before refreshing
                showToast('All pending hosts processed – refreshing map...', 4500);

                // Give the user time to see the toast before reload
                setTimeout(() => window.location.reload(), 4500);
            }
        }
        // -------------------------------------------------------------------
    } catch (error) {
        console.error(`Error in realTimeBubbleStatus: ${error.message}`);
        if (retries > 0) {
            console.log(`Retrying entire update... (${retries} attempts left)`);
            await new Promise(resolve => setTimeout(resolve, delayMs));
            return realTimeBubbleStatus(svg, hostData, retries - 1, delayMs);
        } else {
            console.error("Max retries reached. Giving up.");
            throw error;
        }
    }
}

// Fetch status data for all hosts from the database
async function fetchHostStatusFromDatabase() {
    try {
        // Get hostgroup from URL if present
        const urlParams = new URLSearchParams(window.location.search);
        const hostgroup = urlParams.get('hostgroup');
        
        // Build the endpoint URL with any necessary filters
        let endpoint = 'get_host_status.php';
        if (hostgroup) {
            endpoint += `?hostgroup=${encodeURIComponent(hostgroup)}`;
            console.log(`Fetching host status for hostgroup: ${hostgroup}`);
        }
        
        console.log(`Fetching host status from: ${endpoint}`);
        const response = await fetch(endpoint);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Check for error response
        if (data.error) {
            throw new Error(data.error);
        }
        
        console.log(`Received status data for ${data.length} hosts`);
        
        // Convert array to map for faster lookups
        const statusMap = new Map();
        data.forEach(host => {
            statusMap.set(parseInt(host.id), {
                apmStatus: host.apmStatus,
                pending_count: parseInt(host.pending_count) || 0,
                ok_count: parseInt(host.ok_count) || 0,
                warning_count: parseInt(host.warning_count) || 0,
                unknown_count: parseInt(host.unknown_count) || 0,
                critical_count: parseInt(host.critical_count) || 0
            });
        });
        
        return statusMap;
    } catch (error) {
        console.error('Error fetching status data:', error);
        throw error;
    }
}

// Update a single bubble with data from the database
async function updateBubbleFromDatabase(bubble, statusMap) {
    const d = d3.select(bubble).datum();
    if (!d) return false;
    
    // Get status data for this host
    const statusData = statusMap.get(parseInt(d.id));
    
    // If no data found for this host, remove its visual elements from the UI
    if (!statusData) {
        // Initialize global set to track hosts already removed
        if (!window.removedHostIds) {
            window.removedHostIds = new Set();
        }

        // If we've already processed removal for this host, skip the rest
        if (window.removedHostIds.has(d.id)) {
            return false;
        }

        // Mark this host as removed so we don't process it again
        window.removedHostIds.add(d.id);

        console.log(`Host ID ${d.id} (${d.hostname}) no longer exists in DB – removing bubble.`);

        // Remove the circle (bubble)
        d3.select(bubble).remove();

        // Try within parent group first
        d3.select(bubble.parentNode).select(`.badge-${d.id}`).remove();
        // Fallback: remove any badge with this id anywhere in the SVG
        d3.selectAll(`.badge-${d.id}`).remove();

        // Remove arrows connected to this bubble (as parent or child)
        d3.selectAll('.parent-child-arrow')
            .filter(function() {
                const arrowData = d3.select(this).datum();
                return arrowData && (arrowData.parentId === d.id || arrowData.childIp === d.ip);
            })
            .remove();

        // Remove the label associated with this bubble
        d3.selectAll('#map g .bubble-text')
            .filter(l => l && l.id === d.id)
            .remove();

        // If this host was one of the initially not-added hosts, remove it from that tracking set
        if (window.initialNotAddedHostIds && window.initialNotAddedHostIds.has(d.id)) {
            window.initialNotAddedHostIds.delete(d.id);
        }

        // Show toast message (only the first time, handled by removedHostIds check above)
        showToast(`Host ID ${d.id} (${d.hostname}) is unreachable and does not exist in APM – removing bubble.`);

        // Mark datum so any future logic that still references the original
        // hostData array knows this host has been removed.
        if (d) {
            d.removed = true;
        }

        // If this was the last host in its group, remove the group bubble and label
        if (d && Array.isArray(d.hostgroups) && d.hostgroups.length > 0) {
            const groupName = d.hostgroups[0];
            const remainingHosts = d3.selectAll('.host-bubble')
                .filter(h => h && Array.isArray(h.hostgroups) && h.hostgroups[0] === groupName);
            if (remainingHosts.empty()) {
                // Remove group bubble and its text
                d3.selectAll('.group-bubble')
                    .filter(h => h && h.hostgroup === groupName)
                    .remove();
                d3.selectAll('.group-text')
                    .filter(h => h && h.hostgroup === groupName)
                    .remove();
            }
        }

        return false;
    }
    
    // Simply use the apmStatus directly from the database
    const bubbleClass = statusData.apmStatus || 'not-added';
    
    // Calculate total non-OK services (pending + warning + unknown + critical)
    const nonOkCount = parseInt(statusData.pending_count || 0) + 
                       parseInt(statusData.warning_count || 0) + 
                       parseInt(statusData.unknown_count || 0) + 
                       parseInt(statusData.critical_count || 0);
    
    // Create a serviceStatuses object that represents what services would be in each status
    // This is needed for filtering to work
    const serviceStatuses = {};
    
    // Add fake service entries to match the counts in each status
    // This ensures the filtering can detect which services are in which state
    if (statusData.pending_count > 0) {
        for (let i = 0; i < statusData.pending_count; i++) {
            serviceStatuses[`pending_service_${i}`] = 1; // 1 = pending
        }
    }
    
    if (statusData.ok_count > 0) {
        for (let i = 0; i < statusData.ok_count; i++) {
            serviceStatuses[`ok_service_${i}`] = 2; // 2 = ok
        }
    }
    
    if (statusData.warning_count > 0) {
        for (let i = 0; i < statusData.warning_count; i++) {
            serviceStatuses[`warning_service_${i}`] = 4; // 4 = warning
        }
    }
    
    if (statusData.unknown_count > 0) {
        for (let i = 0; i < statusData.unknown_count; i++) {
            serviceStatuses[`unknown_service_${i}`] = 8; // 8 = unknown
        }
    }
    
    if (statusData.critical_count > 0) {
        for (let i = 0; i < statusData.critical_count; i++) {
            serviceStatuses[`critical_service_${i}`] = 16; // 16 = critical
        }
    }
    
    // Preserve the 'selected' class if it exists
    const bubbleElement = d3.select(bubble);
    const isSelected = bubbleElement.classed('selected');
    
    // Update the bubble's class, preserving the selected state
    let newClasses = `host-bubble ${bubbleClass}`;
    if (isSelected) {
        newClasses += ' selected';
    }
    
    bubbleElement
        .attr("class", newClasses)
        .attr("data-host-status", getStatusCodeFromClass(bubbleClass))
        .attr("data-service-statuses", JSON.stringify(serviceStatuses));
    
    // Add or update the badge as a sibling in the same parent <g>
    let badge = d3.select(bubble.parentNode).select(`.badge-${d.id}`);
    if (badge.empty()) {
        badge = d3.select(bubble.parentNode)
            .append("g")
            .attr("class", `badge badge-${d.id}`)
            .attr("transform", `translate(${d.x + d.size * 0.7}, ${d.y - d.size * 0.7})`);

        // Add the badge circle (mobile app style: small red circle)
        badge.append("circle")
            .attr("r", 10)
            .style("fill", "#ff4444")
            .style("stroke", "#fff")
            .style("stroke-width", "1px");

        // Add the badge text (white number centered)
        badge.append("text")
            .attr("text-anchor", "middle")
            .attr("dy", ".35em")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("fill", "#fff");
    }

    // Update badge content and visibility - show all non-OK services
    if (nonOkCount > 0) {
        badge.select("text").text(nonOkCount);
        badge.attr("visibility", "visible");
    } else {
        badge.attr("visibility", "hidden");
    }

    // Initial badge position (updated in ticked)
    badge.attr("transform", `translate(${d.x + d.size * 0.7}, ${d.y - d.size * 0.7})`);
    
    return true;
}

// Helper function to convert bubble class to status code
function getStatusCodeFromClass(bubbleClass) {
    switch (bubbleClass) {
        case 'pending':
            return 1;
        case 'ok':
            return 2;
        case 'down':
            return 4;
        case 'unknown':
            return 8;
        case 'warning':
            return 4; // Warning maps to 4 in service statuses
        case 'critical':
            return 16; // Critical maps to 16 in service statuses
        case 'not-added':
            return 0;
        default:
            console.log(`Unknown bubble class: ${bubbleClass}, using default code 0`);
            return 0;
    }
}

async function getHostnameByIP(ip) {
    // First check the cache
    const cachedHostname = statusCache.getHostname(ip);
    if (cachedHostname !== null) {
        return cachedHostname;
    }

    const nagiosUrl = `https://${window.location.hostname}//nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true`;
    try {
        const response = await fetch(nagiosUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();
        for (const hostname in data.data.hostlist) {
            const host = data.data.hostlist[hostname];
            
            // Cache all hostname/IP mappings for future lookups
            statusCache.setHostname(host.address, host.name);
            
            if (host.address === ip) {
                return host.name;
            }
        }
        // Cache a null result to avoid repeated lookups
        statusCache.setHostname(ip, null);
        return null;
    } catch (error) {
        console.error('Error fetching host data:', error);
        throw error;
    }
}

async function getAllServiceStatuses(hostname) {
    try {
        const apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=servicelist&hostname=${encodeURIComponent(hostname)}`;
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const jsonData = await response.json();
        if (jsonData.result.type_code !== 0) throw new Error(`Query failed: ${jsonData.result.type_text}`);
        
        // Get the full list of services and their statuses
        const serviceData = jsonData.data.servicelist[hostname];
        if (!serviceData) return { serviceStatuses: {}, worstStatus: 0, criticalCount: 0 };

        // Count by status (for summary)
        const statusCounts = {
            1: 0,  // pending
            2: 0,  // ok
            4: 0,  // warning
            8: 0,  // unknown
            16: 0  // critical
        };

        // Create a mapping of service name to status code
        const serviceStatuses = {};
        for (const [serviceName, statusCode] of Object.entries(serviceData)) {
            serviceStatuses[serviceName] = statusCode;
            
            // Increment counters for this status
            if (statusCounts[statusCode] !== undefined) {
                statusCounts[statusCode]++;
            }
        }

        const statuses = Object.values(serviceData);
        const maxStatus = Math.max(...statuses);
        
        // If max is 2 and there's a 1 present, return 1 (pending); otherwise return maxStatus
        let worstStatus = (maxStatus === 2 && statuses.includes(1)) ? 1 : maxStatus;

        // Count only statuses that are not OK (status 2) and not 0
        let criticalCount = statuses.filter(status => status !== 2 && status !== 0).length;

        return { 
            serviceStatuses, 
            worstStatus, 
            criticalCount,
            statusCounts 
        };
    } catch (error) {
        console.error('Error:', error);
        return { 
            serviceStatuses: {}, 
            worstStatus: 0, 
            criticalCount: 0,
            statusCounts: { 1: 0, 2: 0, 4: 0, 8: 0, 16: 0 }
        };
    }
}

async function checkWorstStatus(hostname) {
    const { worstStatus, criticalCount } = await getAllServiceStatuses(hostname);
    return { worstStatus, criticalCount };
}

async function checkHostStatus(hostname) {
    try {
        const apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=host&hostname=${encodeURIComponent(hostname)}`;
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const jsonData = await response.json();
        if (jsonData.result.type_code !== 0) throw new Error(`Query failed: ${jsonData.result.type_text}`);
        return jsonData.data?.host?.status !== undefined ? jsonData.data.host.status : 0;
    } catch (error) {
        return 0;
    }
}

async function getHostStatusClass(host) {
    const hostStatus = await checkHostStatus(host);
    
    // Default values for a host not found in Nagios
    if (hostStatus === 0) {
        return { 
            bubbleClass: 'not-added', 
            criticalCount: 0, 
            hostStatus: 0,
            serviceStatuses: {}
        };
    }
    
    let bubbleClass = statusMappings.host[hostStatus] || 'not-added';
    let criticalCount = 0; // Default to 0 critical services
    let serviceStatuses = {};

    // Check services regardless of host status
    const { worstStatus, criticalCount: count, serviceStatuses: services } = await getAllServiceStatuses(host);
    
    if (hostStatus === 2) {
        // Host is UP, use service status for bubble class
        bubbleClass = statusMappings.service[worstStatus] || 'ok';
        criticalCount = count;
    } else {
        // Host is DOWN/PENDING/UNKNOWN, keep host status for bubble class but include service info
        criticalCount = Math.max(1, count); // At least 1 critical (the host itself)
    }
    
    serviceStatuses = services;

    return { 
        bubbleClass, 
        criticalCount, 
        hostStatus,
        serviceStatuses 
    };
}

async function updateApmStatus(id, status) {
    // Make sure we explicitly set notAdded=Yes when status is not-added
    const notAddedParam = status === "not-added" ? "&notAdded=Yes" : "";
    const url = `updateApmStatus.php?id=${encodeURIComponent(id)}${notAddedParam}`;

    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.text();
    } catch (error) {
        console.error('Error updating APM status:', error); // Log any errors
    }
}

function getInfrasNames(){
    // Fetch infrastructure names from get_infra_name.php and add them to the scan modal dropdown
    fetch('get_infra_name.php')
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.json();
    })
    .then(data => {
        const infraSelect = document.getElementById('infra');
        infraSelect.innerHTML = ''; // Clear the loading option

        if (data.length > 0) {
            data.forEach(infra => {
                const option = document.createElement('option');
                option.value = infra.name;
                option.textContent = infra.name;
                infraSelect.appendChild(option);
            });
        } else {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'No infrastructure found';
            infraSelect.appendChild(option);
        }
    })
    .catch(error => {
        console.error('Error fetching infrastructure names:', error);
        const infraSelect = document.getElementById('infra');
        infraSelect.innerHTML = ''; // Clear the loading option

        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'Failed to load';
        infraSelect.appendChild(option);
    });
}

function blacklistHost(ip, currentBubble, bubbleData) {
    // Show a confirmation dialog to the user
    var isConfirmed = confirm("Are you sure you want to stop monitoring this host?");

    if (!isConfirmed) {
      return; // Exit the function if the user cancels
    }

    // Send the request to blacklist the host
    var xhr = new XMLHttpRequest();
    xhr.open('GET', 'blacklistHost.php?ip=' + encodeURIComponent(ip) + '&infra=' + encodeURIComponent(urlParams.get('infra')), true);
    xhr.send();
    
    // Immediately remove visual elements instead of reloading the page
    // Initialize global set to track hosts already removed (reuse logic from updateBubbleFromDatabase)
    if (!window.removedHostIds) {
        window.removedHostIds = new Set();
    }
    window.removedHostIds.add(bubbleData.id);

    // Remove the circle (bubble)
    d3.select(currentBubble).remove();

    // Try within parent group first
    d3.select(currentBubble.parentNode).select(`.badge-${bubbleData.id}`).remove();
    // Fallback: remove any badge with this id anywhere in the SVG
    d3.selectAll(`.badge-${bubbleData.id}`).remove();

    // Remove arrows connected to this bubble (as parent or child)
    d3.selectAll('.parent-child-arrow')
        .filter(function() {
            const arrowData = d3.select(this).datum();
            return arrowData && (arrowData.parentId === bubbleData.id || arrowData.childIp === bubbleData.ip);
        })
        .remove();

    // Remove the label associated with this bubble
    d3.selectAll('#map g .bubble-text')
        .filter(l => l && l.id === bubbleData.id)
        .remove();

    // Show a toast to inform the user
    showToast(`Host ID ${bubbleData.id} (${bubbleData.hostname}) has been removed from monitoring.`);

    // If this was the last host in its group, remove the group bubble and label
    if (bubbleData && Array.isArray(bubbleData.hostgroups) && bubbleData.hostgroups.length > 0) {
        const groupName = bubbleData.hostgroups[0];
        const remainingHosts = d3.selectAll('.host-bubble')
            .filter(h => h && Array.isArray(h.hostgroups) && h.hostgroups[0] === groupName);
        if (remainingHosts.empty()) {
            // Remove group bubble and its text
            d3.selectAll('.group-bubble')
                .filter(h => h && h.hostgroup === groupName)
                .remove();
            d3.selectAll('.group-text')
                .filter(h => h && h.hostgroup === groupName)
                .remove();
        }
    }

    // No page reload needed
}

function isValidNetworkString(inputStr) {
    // Check if input is a string and not empty
    if (typeof inputStr !== 'string' || inputStr.trim() === '') {
        return false;
    }

    inputStr = inputStr.trim();

    // URL pattern (http:// or https:// followed by valid domain)
    const urlPattern = /^https?:\/\/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/.*)?$/;
    if (urlPattern.test(inputStr)) {
        return true;
    }

    // Split by commas for potential IP list
    const ipList = inputStr.split(',').map(ip => ip.trim());

    // IPv4 pattern
    const ipv4Pattern = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

    // Simplified IPv6 pattern (not fully comprehensive but catches most common cases)
    const ipv6Pattern = /^([0-9a-fA-F]{0,4}:){7}[0-9a-fA-F]{0,4}$/;

    // Subnet pattern (IP followed by /0-32)
    const subnetPattern = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(3[0-2]|[12]?[0-9])$/;

    // IP range pattern (IPv4 followed by hyphen and end range 0-255)
    const ipRangePattern = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)-\s*(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

    if (ipList.length === 1) {
        const singleItem = ipList[0];

        // Check subnet
        if (subnetPattern.test(singleItem)) {
            return true;
        }

        // Check IP range
        if (ipRangePattern.test(singleItem)) {
            const [baseIp, endRange] = singleItem.split('-').map(part => part.trim());
            const startOctet = parseInt(baseIp.split('.')[3], 10);
            const endOctet = parseInt(endRange, 10);
            return startOctet <= endOctet; // Ensure start is less than or equal to end
        }

        // Check single IP (IPv4 or IPv6)
        if (ipv4Pattern.test(singleItem) || ipv6Pattern.test(singleItem)) {
            return true;
        }

        return false;
    }

    // If there's more than one item (comma present), disallow CIDR and ranges
    if (ipList.length > 1) {
        return ipList.every(ip => 
            // Only allow pure IPv4 or IPv6 addresses when commas are used
            (ipv4Pattern.test(ip) || ipv6Pattern.test(ip)) &&
            !subnetPattern.test(ip) &&  // Disallow CIDR
            !ipRangePattern.test(ip)    // Disallow ranges
        );
    }

    return false;
}

// Simple toast message for transient notifications
function showToast(message, duration = 3000) {
    // Avoid multiple toasts stacking if called in quick succession
    const existing = document.querySelector('.bubble-removal-toast');
    if (existing) existing.remove();

    const toast = document.createElement('div');
    toast.className = 'bubble-removal-toast';
    toast.textContent = message;

    // Basic inline styles to avoid external CSS dependency
    toast.style.position = 'fixed';
    toast.style.bottom = '20px';
    toast.style.right = '20px';
    toast.style.background = 'rgba(0, 0, 0, 0.85)';
    toast.style.color = '#fff';
    toast.style.padding = '8px 14px';
    toast.style.borderRadius = '6px';
    toast.style.fontSize = '14px';
    toast.style.boxShadow = '0 4px 12px rgba(0,0,0,0.3)';
    toast.style.zIndex = 2000;
    toast.style.opacity = '0';
    toast.style.transition = 'opacity 0.3s ease';

    document.body.appendChild(toast);

    // Fade in
    requestAnimationFrame(() => {
        toast.style.opacity = '1';
    });

    // Fade out after duration
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.addEventListener('transitionend', () => toast.remove());
    }, duration);
}