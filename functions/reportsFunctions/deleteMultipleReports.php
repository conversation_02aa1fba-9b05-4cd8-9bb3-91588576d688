<?php
// deleteMultipleReports.php - deletes multiple saved reports from the server
// Expected POST param: files (JSON array of filenames)

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$filesJson = isset($_POST['files']) ? $_POST['files'] : '';
$files = json_decode($filesJson, true);

if (!is_array($files) || empty($files)) {
    echo json_encode(['success' => false, 'message' => 'No files specified']);
    exit;
}

$REPORTS_DIR = __DIR__ . '/saved_reports';
$deletedCount = 0;
$errors = [];

foreach ($files as $filename) {
    // Validate filename (allow both manual and scheduled report patterns)
    // Manual reports: blesk_report_{type}_{timestamp}.pdf (e.g., blesk_report_hostgroups_20250721_143022.pdf)
    // Scheduled reports: blesk_report_{days}d_{timestamp}.pdf (e.g., blesk_report_1d_20250721_143022.pdf)
    if (empty($filename) || !preg_match('/^blesk_report_([a-z]+|\d+d)_\d{8}_\d{6}\.pdf$/', $filename)) {
        $errors[] = "Invalid filename: $filename";
        continue;
    }

    $filepath = $REPORTS_DIR . '/' . $filename;
    $metadataFile = $REPORTS_DIR . '/' . pathinfo($filename, PATHINFO_FILENAME) . '.json';

    // Check if file exists
    if (!file_exists($filepath)) {
        $errors[] = "File not found: $filename";
        continue;
    }

    // Delete both the PDF file and metadata file
    $pdfDeleted = unlink($filepath);
    $metadataDeleted = true;

    if (file_exists($metadataFile)) {
        $metadataDeleted = unlink($metadataFile);
    }

    if ($pdfDeleted && $metadataDeleted) {
        $deletedCount++;
    } else {
        $errors[] = "Failed to delete: $filename";
    }
}

if ($deletedCount > 0) {
    $message = "Successfully deleted $deletedCount report(s)";
    if (!empty($errors)) {
        $message .= ". Errors: " . implode(', ', $errors);
    }
    echo json_encode(['success' => true, 'message' => $message, 'deletedCount' => $deletedCount]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to delete any reports. Errors: ' . implode(', ', $errors)]);
}
?> 