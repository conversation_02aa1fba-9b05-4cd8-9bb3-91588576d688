<?php
// addReportCron.php - handles scheduling a daily email report via cron
// Expected POST param: email

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$email = isset($_POST['email']) ? trim($_POST['email']) : '';

// Validate multiple emails separated by semicolons
$emails = array_map('trim', explode(';', $email));
$validEmails = [];
foreach ($emails as $singleEmail) {
    if (!empty($singleEmail)) {
        if (!filter_var($singleEmail, FILTER_VALIDATE_EMAIL)) {
            echo json_encode(['success' => false, 'message' => 'Invalid email address: ' . $singleEmail]);
            exit;
        }
        $validEmails[] = $singleEmail;
    }
}

if (empty($validEmails)) {
    echo json_encode(['success' => false, 'message' => 'At least one valid email address must be provided.']);
    exit;
}

// Join back the validated emails for storage
$email = implode(';', $validEmails);

// Path where the cron definition will be stored
$cronFilePath = '/etc/cron.d/blesk_daily_report';
// Absolute path to the scheduled sender script
$scriptPath   = '/var/www/html/bubblemaps/functions/reportsFunctions/sendScheduledReport.php';

$frequency = isset($_POST['frequency']) ? strtolower(trim($_POST['frequency'])) : 'daily';
$time      = isset($_POST['time']) ? trim($_POST['time']) : '00:05';
$range     = isset($_POST['range']) ? intval($_POST['range']) : 1;
$hostStatuses = isset($_POST['hostStatuses']) ? preg_replace('/[^a-z,]/i','', strtolower($_POST['hostStatuses'])) : 'up,down,unreachable';
$svcStatuses  = isset($_POST['svcStatuses']) ? preg_replace('/[^a-z,]/i','', strtolower($_POST['svcStatuses'])) : 'ok,warning,critical,unknown';
$saveToServer = isset($_POST['saveToServer']) ? ($_POST['saveToServer'] === 'true') : true;

if (!in_array($frequency, ['daily','weekly','monthly'])) $frequency = 'daily';
if ($range < 1 || $range > 365) {
    $range = $frequency==='daily'?1:($frequency==='weekly'?7:30);
}

// Parse time HH:MM
if (!preg_match('/^(\d{1,2}):(\d{2})$/', $time, $m)) {
    $hour = 0; $min = 5;
} else {
    $hour = max(0,min(23,intval($m[1])));
    $min  = max(0,min(59,intval($m[2])));
}

switch ($frequency) {
    case 'weekly':
        $cronSchedule = "$min $hour * * 0"; // Sunday
        break;
    case 'monthly':
        $cronSchedule = "$min $hour 1 * *";
        break;
    case 'daily':
    default:
        $cronSchedule = "$min $hour * * *";
        break;
}

$cronLine = sprintf("%s root /usr/bin/php %s %s %d %s %s >>/var/log/blesk_report.log 2>&1", $cronSchedule, escapeshellarg($scriptPath), escapeshellarg($email), $range, escapeshellarg($hostStatuses), escapeshellarg($svcStatuses));
$cronContent  = "# Blesk – scheduled daily report\n" . $cronLine;
$cronLineOnly = $cronLine; // single cron line string

try {
    // Write cron using sudo + tee (avoids direct permission issues like in backupConfigHandler)
    $writeCmd = 'echo ' . escapeshellarg($cronLine . "\n") . ' | sudo tee ' . escapeshellarg($cronFilePath) . ' > /dev/null';
    exec($writeCmd, $out, $statusWrite);
    if ($statusWrite !== 0) {
        throw new RuntimeException('Failed to write cron file via sudo (exit ' . $statusWrite . ').');
    }

    // Ensure correct permissions
    $chmodCmd = 'sudo chmod 644 ' . escapeshellarg($cronFilePath);
    exec($chmodCmd, $out2, $statusChmod);
    if ($statusChmod !== 0) {
        error_log('[addReportCron] chmod failed for cron file (exit ' . $statusChmod . ')');
        // not fatal
    }

    echo json_encode(['success' => true, 'message' => 'Daily report successfully scheduled (cron created).']);
} catch (Throwable $e) {
    error_log('[addReportCron] ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error scheduling report: ' . $e->getMessage()]);
}
?> 