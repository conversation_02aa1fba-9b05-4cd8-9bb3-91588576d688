<?php
// listSavedReports.php - returns JSON list of all saved reports
// Method: GET
// Response: { success: bool, reports: array }

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$REPORTS_DIR = __DIR__ . '/saved_reports';

if (!is_dir($REPORTS_DIR)) {
    echo json_encode(['success' => true, 'reports' => []]);
    exit;
}

$reports = [];
$files = glob($REPORTS_DIR . '/*.json');

foreach ($files as $metadataFile) {
    $content = file_get_contents($metadataFile);
    if ($content === false) continue;
    
    $metadata = json_decode($content, true);
    if (!$metadata || !isset($metadata['filename'])) continue;
    
    $pdfFile = $REPORTS_DIR . '/' . $metadata['filename'];
    if (!file_exists($pdfFile)) continue;
    
    // Get file size
    $fileSize = filesize($pdfFile);
    $fileSizeFormatted = formatFileSize($fileSize);
    
    // Add file info to metadata
    $metadata['fileSize'] = $fileSize;
    $metadata['fileSizeFormatted'] = $fileSizeFormatted;
    $metadata['exists'] = true;
    
    $reports[] = $metadata;
}

// Sort by creation date (newest first)
usort($reports, function($a, $b) {
    return ($b['created'] ?? 0) - ($a['created'] ?? 0);
});

echo json_encode([
    'success' => true,
    'reports' => $reports
]);

function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?> 