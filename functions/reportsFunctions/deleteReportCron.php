<?php
// deleteReportCron.php – disables the scheduled report by deleting the cron file
// Method: POST

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$cronFilePath = '/etc/cron.d/blesk_daily_report';

// Use sudo to remove file to match permissions strategy in addReportCron.php
$cmd = 'sudo rm -f ' . escapeshellarg($cronFilePath);
exec($cmd, $out, $status);

if ($status === 0) {
    echo json_encode(['success' => true, 'message' => 'Scheduled report disabled.']);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to disable scheduled report (exit ' . $status . ').']);
}
?> 