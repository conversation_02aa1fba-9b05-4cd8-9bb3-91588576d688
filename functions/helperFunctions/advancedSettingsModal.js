function styleIframeContent(iframeSrc) {
    const iframe = document.getElementById('modal-frame');
    console.log("Starting styleIframeContent for URL:", iframeSrc);

    if (iframeSrc) {
        iframe.src = iframeSrc;
    }

    iframe.onload = function() {
        console.log("Iframe loaded, attempting to apply styles...");
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (!iframeDoc) {
                console.warn("Iframe document is not accessible.");
                return;
            }

            // Hide all nav elements
            const navElements = iframeDoc.getElementsByTagName('nav');
            for (let nav of navElements) {
                nav.style.display = 'none';
            }

            // Function to apply styles
            const applyStyles = () => {
                console.log('Applying styles to iframe content...');

                // Remove any existing injected styles to avoid duplicates
                const existingStyle = iframeDoc.querySelector('style[data-injected]');
                if (existingStyle) {
                    existingStyle.remove();
                }

                const style = iframeDoc.createElement('style');
                style.setAttribute('data-injected', 'true');
                style.textContent = `
                    :root {
                        --primary: #888;
                        --pending: #808080;
                        --success: #cde06b;
                        --warning: #ffa500;
                        --critical: #d41d28;
                        --unknown: #64748b;
                        --surface: #222;
                        --background: #2f2f2f;
                        --text: #f8f9fa;
                        --text-secondary: #ccc;
                        --border: #717171;
                        --radius: 16px;
                        --shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                        --shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.2);
                        --pending-bg: #373737;
                        --success-bg: #3b4032;
                        --warning-bg: #40382a;
                        --critical-bg: #3c2e30;
                        --unknown-bg: #353739;
                    }

                    body {
                        background: var(--background) !important;
                        color: var(--text) !important;
                        margin: 0;
                        padding: 0;
                        font-family: 'Calibri', sans-serif;
                        line-height: 1.6;
                        font-weight: 400;
                        overflow-x: hidden;
                    }

                    /* Rest of your styles */
                    .navbar {
                        background: var(--surface) !important;
                        border-bottom: 1px solid var(--border) !important;
                        box-shadow: var(--shadow) !important;
                    }

                    .navbar a {
                        color: var(--text) !important;
                    }

                    .navbar a:hover {
                        color: var(--text-secondary) !important;
                    }

                    /* ... rest of your styles ... */
                `;
                iframeDoc.head.appendChild(style);

                // Force apply styles to existing elements
                const logbookTable = iframeDoc.querySelector('#content_main table.table.table-striped.table-hover');
                if (logbookTable) {
                    console.log('Logbook table found, applying styles directly...');
                    logbookTable.style.background = '#2a2a2a';
                    logbookTable.style.border = '2px solid #888';
                    logbookTable.style.borderRadius = '8px';
                    logbookTable.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';

                    const thElements = logbookTable.querySelectorAll('th');
                    thElements.forEach(th => {
                        th.style.background = '#404040';
                        th.style.color = '#ccc';
                        th.style.borderBottom = '2px solid #888';
                        th.style.padding = '8px';
                    });

                    const tdElements = logbookTable.querySelectorAll('td');
                    tdElements.forEach(td => {
                        td.style.borderColor = '#555';
                        td.style.padding = '8px';
                        td.style.color = '#f8f9fa';
                        td.style.background = 'none';
                    });

                    const trElements = logbookTable.querySelectorAll('tr');
                    trElements.forEach((tr, index) => {
                        if (index === 0) return; // Skip header row
                        tr.style.background = index % 2 === 0 ? '#2a2a2a' : '#3a3a3a';
                    });
                } else {
                    console.log('Logbook table not found yet.');
                }

                // Debug: Log the body content
                console.log('Iframe body content after styling:', iframeDoc.body.innerHTML);
            };

            // Apply styles immediately after load
            applyStyles();

            // Enhanced polling mechanism
            let attempts = 0;
            const maxAttempts = 30;
            const interval = setInterval(() => {
                const logbookTable = iframeDoc.querySelector('#content_main table.table.table-striped.table-hover');
                if (logbookTable || attempts >= maxAttempts) {
                    if (logbookTable) {
                        console.log('Logbook table found after polling, applying styles...');
                        applyStyles();
                    } else {
                        console.log('Logbook table not found after maximum attempts.');
                    }
                    clearInterval(interval);
                }
                attempts++;
            }, 500);

            // Use MutationObserver to detect DOM changes
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.addedNodes.length || mutation.removedNodes.length) {
                        const logbookTable = iframeDoc.querySelector('#content_main table.table.table-striped.table-hover');
                        if (logbookTable) {
                            console.log('DOM change detected, reapplying styles...');
                            applyStyles();
                        }
                    }
                });
            });

            observer.observe(iframeDoc.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => observer.disconnect(), 30000);

        } catch (e) {
            console.warn('Cannot inject styles into iframe due to cross-origin restrictions:', e);
        }
    };
}