function fetchInfraBubbles() {
    const svg = d3.select("#map");
    const container = d3.select("#canvas-container");

    // Detect mobile
    function isMobile() {
        return window.matchMedia("(max-width: 768px)").matches;
    }

    // Dynamic dimensions
    function updateDimensions() {
        const width = container.node().getBoundingClientRect().width || 1200;
        const height = container.node().getBoundingClientRect().height || 900;
        svg.attr("width", width).attr("height", height);
        return { width, height };
    }

    let dimensions = updateDimensions();

    // Adjusted sizes based on mobile
    const baseBubbleRadius = isMobile() ? 30 : 70;

    // The API endpoint for infrastructure names
    const apiUrl = 'get_infra_name.php';

    // Fetch data
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            // Data is assumed to be an array like: [{"id":"1","name":"INFRA"}]
            const infraItems = data;
            const centerX = dimensions.width / 2;
            const centerY = dimensions.height / 2;

            // Map each infrastructure item to a bubble data object
            const bubbleData = infraItems.map(item => ({
                id: item.id,
                hostname: item.name,
                x: centerX + (Math.random() - 0.5) * dimensions.width * 0.5, // Random position within 50% of width
                y: centerY + (Math.random() - 0.5) * dimensions.height * 0.5, // Random position within 50% of height
                size: baseBubbleRadius,
                status: 'pending', // Default status, will be updated later
                problemHosts: 0 // Default count, will be updated later
            }));

            // Append a group to hold bubbles and text
            let g = svg.append("g");

            // Define zoom behavior with touch support
            const zoom = d3.zoom()
                .scaleExtent([0.1, 5])
                .on("zoom", (event) => {
                    g.attr("transform", event.transform);
                });
            
            // Apply zoom behavior without restricting touch events
            svg.call(zoom);
            
            // Enable canvas panning by adding touch listeners to the SVG
            svg.style("touch-action", "none");
            
            // Explicit touch event handling for mobile
            let touchStartX, touchStartY;
            let lastTouchDistance = 0;
            let currentTransform = d3.zoomIdentity;
            
            svg.on("touchstart", function(event) {
                // Store the current transform
                currentTransform = d3.zoomTransform(svg.node());
                
                // Get touch coordinates
                const touches = event.touches;
                if (touches.length === 1) {
                    touchStartX = touches[0].clientX;
                    touchStartY = touches[0].clientY;
                } else if (touches.length === 2) {
                    // For pinch-zoom, calculate initial distance
                    lastTouchDistance = Math.hypot(
                        touches[1].clientX - touches[0].clientX,
                        touches[1].clientY - touches[0].clientY
                    );
                }
                event.preventDefault();
            });
            
            svg.on("touchmove", function(event) {
                const touches = event.touches;
                
                if (touches.length === 1) {
                    // Single touch for panning
                    const dx = touches[0].clientX - touchStartX;
                    const dy = touches[0].clientY - touchStartY;
                    
                    // Apply translation based on touch movement
                    const newTransform = currentTransform.translate(dx / currentTransform.k, dy / currentTransform.k);
                    svg.call(zoom.transform, newTransform);
                } else if (touches.length === 2) {
                    // Two touches for pinch zoom
                    const currentDistance = Math.hypot(
                        touches[1].clientX - touches[0].clientX,
                        touches[1].clientY - touches[0].clientY
                    );
                    
                    if (lastTouchDistance > 0) {
                        // Calculate zoom factor
                        const scaleFactor = currentDistance / lastTouchDistance;
                        
                        // Get the midpoint of the two touches
                        const midX = (touches[0].clientX + touches[1].clientX) / 2;
                        const midY = (touches[0].clientY + touches[1].clientY) / 2;
                        
                        // Convert midpoint to SVG coordinates
                        const svgPoint = svg.node().createSVGPoint();
                        svgPoint.x = midX;
                        svgPoint.y = midY;
                        
                        // Apply zoom centered on the midpoint
                        const newK = currentTransform.k * scaleFactor;
                        const constrainedK = Math.max(0.1, Math.min(5, newK)); // Respect zoom limits
                        
                        // Create transform with new scale
                        const newTransform = currentTransform.scale(constrainedK / currentTransform.k);
                        svg.call(zoom.transform, newTransform);
                        
                        // Update for next move
                        currentTransform = newTransform;
                        lastTouchDistance = currentDistance;
                    }
                }
                event.preventDefault();
            });

            svg.on("touchend", function() {
                // Reset touch states when touches end
                lastTouchDistance = 0;
            });

            // Create simulation with forces
            const simulation = d3.forceSimulation(bubbleData)
                .force("charge", d3.forceManyBody().strength(isMobile() ? -10 : -30)) // Stronger repulsion
                .force("collision", d3.forceCollide().radius(d => d.size + 2).strength(0.8))
                .force("center", d3.forceCenter(centerX, centerY).strength(0.05)) // Gentle centering force
                .alphaDecay(0.02) // Slightly faster stabilization
                .on("tick", ticked);

            // Append circles for each bubble
            const bubbles = g.selectAll(".host-bubble")
                .data(bubbleData)
                .enter()
                .append("circle")
                .attr("class", d => `host-bubble ${d.status}`)
                .attr("r", d => d.size)
                .attr("cx", d => d.x)
                .attr("cy", d => d.y)
                .call(drag(simulation))
                .on("touchstart", function(event) {
                    // Prevent default only for drag operations
                    event.stopPropagation();
                })
                .on("mouseover", function() {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr("r", d => d.size * 1.2);
                })
                .on("mouseout", function() {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr("r", d => d.size);
                })
                .on("click", function(event, d) {
                    // Check if a detail card is already open for this bubble
                    if (currentOpenInfraCardBubbleId === d.id) {
                        // Navigate directly to the infrastructure view
                        const urlParams = new URLSearchParams(window.location.search);
                        const targetUrl = urlParams.has('subnet')
                            ? `subnets.php?infra=${encodeURIComponent(d.hostname)}&subnet=true`
                            : `hosts.php?subnet=all&subnetNickname=All Hosts&infra=${encodeURIComponent(d.hostname)}`;
                        window.location.href = targetUrl;
                    } else {
                        // Show detail card on click (this will update currentOpenInfraCardBubbleId)
                        showInfraDetailCard(event, d);
                    }
                });

            // Append text labels to the bubbles
            const labels = g.selectAll(".bubble-text")
                .data(bubbleData)
                .enter()
                .append("text")
                .attr("class", "bubble-text")
                .style("font-size", isMobile() ? "10px" : "12px")
                .attr("x", d => d.x)
                .attr("y", d => d.y + (isMobile() ? 3 : 5))
                .text(d => {
                    const maxLength = d.size * 1.6; // Adjust this factor based on your needs
                    if (d.hostname.length * 6 > maxLength) { // Approximate character width
                        return d.hostname.substring(0, Math.floor(maxLength / 6)) + "...";
                    }
                    return d.hostname;
                });

            // Update positions on each tick
            function ticked() {
                bubbles
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y);
                labels
                    .attr("x", d => d.x)
                    .attr("y", d => d.y + (isMobile() ? 3 : 5));
                    
                // Update badge positions
                updateBadgePositions(g, bubbleData);
            }

            // Drag behavior
            function drag(sim) {
                function dragstarted(event, d) {
                    if (!event.active) sim.alphaTarget(0.3).restart();
                    d.fx = d.x;
                    d.fy = d.y;
                }

                function dragged(event, d) {
                    d.fx = event.x;
                    d.fy = event.y;
                }

                function dragended(event, d) {
                    if (!event.active) sim.alphaTarget(0);
                    d.fx = null;
                    d.fy = null;
                }

                return d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended)
                    .touchable(true);
            }

            // Reset zoom on double-click
            svg.on("dblclick.zoom", null);
            svg.on("dblclick", () => {
                svg.transition()
                    .duration(750)
                    .call(zoom.transform, d3.zoomIdentity);
            });

            // Adjust simulation and SVG dimensions on window resize
            window.addEventListener("resize", () => {
                dimensions = updateDimensions();
                simulation.nodes(bubbleData).alpha(1).restart();
            });

            // Set up periodic status updates
            setInterval(() => {
                fetchInfraStatus(bubbleData).then(updatedBubbleData => {
                    bubbleData.forEach((bubble, index) => {
                        Object.assign(bubble, updatedBubbleData[index]);
                    });
                    
                    // Update bubble visuals
                    updateBubbleVisuals(bubbles, bubbleData);
                    
                    // Update badges
                    bubbleData.forEach(bubble => {
                        if (bubble.problemHosts > 0) {
                            createOrUpdateBadge(g, bubble);
                        }
                    });
                });
            }, 10000); // Update every 10 seconds

            // Fetch the status data for each infrastructure
            fetchInfraStatus(bubbleData).then(updatedBubbleData => {
                // Update bubbleData with status information
                bubbleData.forEach((bubble, index) => {
                    Object.assign(bubble, updatedBubbleData[index]);
                });
                
                // Update bubble visuals after status fetch
                updateBubbleVisuals(bubbles, bubbleData);
                
                // Create badges for bubbles with problems
                bubbleData.forEach(bubble => {
                    if (bubble.problemHosts > 0) {
                        createOrUpdateBadge(g, bubble);
                    }
                });
                
                // Start or restart simulation with updated data
                if (simulation) {
                    simulation.nodes(bubbleData).alpha(1).restart();
                }
            });

            // Animate bubbles continuously
            function animateBubbles() {
                bubbles.each(function(d) {
                    d3.select(this)
                        .transition()
                        .duration(2000 + Math.random() * 1000)
                        .ease(d3.easeSinInOut)
                        .attr("r", d => d.size * 1.1)
                        .transition()
                        .duration(2000 + Math.random() * 1000)
                        .ease(d3.easeSinInOut)
                        .attr("r", d => d.size)
                        .on("end", animateBubbles);
                });
            }
            animateBubbles();
        })
        .catch(error => console.error('Error fetching infra data:', error));
}
