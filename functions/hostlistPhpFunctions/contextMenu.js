/**
 * Context Menu functionality for Hostlist view
 * Handles right-click context menus for hosts and services
 */

/**
 * Initialize context menu functionality for host and service rows
 */
function initContextMenu() {
    // Add right-click event listeners to host and service rows
    document.addEventListener('contextmenu', async function(e) {
        const hostRow = e.target.closest('.hostlist-host-row');
        const serviceRow = e.target.closest('.hostlist-service-row');
        
        if (hostRow || serviceRow) {
            e.preventDefault();
            
            if (hostRow) {
                // Handle host right-click
                const hostname = hostRow.getAttribute('data-hostname');
                const ip = hostRow.getAttribute('data-ip');
                
                if (hostname && ip) {
                    // Set current host info for context menu
                    window.currentHostIP = ip;
                    window.currentHostname = hostname;
                    const content = await generateContextMenu(null, hostname, true);
                    showHostListContextMenu(content, e);
                }
            } else if (serviceRow) {
                // Handle service right-click
                const hostname = serviceRow.getAttribute('data-hostname');
                const ip = serviceRow.getAttribute('data-ip');
                const serviceName = serviceRow.getAttribute('data-service-name') || serviceRow.querySelector('.service-name')?.textContent.trim();
                
                if (hostname && ip && serviceName) {
                    // Set current host info for context menu
                    window.currentHostIP = ip;
                    window.currentHostname = hostname;
                    window.currentServiceName = serviceName;
                    
                    const encodedServiceName = serviceName.replace(/\s+/g, '+');
                    const content = await generateContextMenu(encodedServiceName, hostname, false);
                    showHostListContextMenu(content, e);
                }
            }
        }
    });
    
    // Close context menu when clicking elsewhere
    document.addEventListener('click', function() {
        const contextMenu = document.getElementById('custom-context-menu');
        if (contextMenu) {
            contextMenu.style.display = 'none';
        }
    });
}

/**
 * Show context menu for hostlist items
 * @param {string} content - HTML content for the menu
 * @param {Event} e - Mouse event
 */
function showHostListContextMenu(content, e) {
    const contextMenu = document.getElementById('custom-context-menu');
    if (!contextMenu) return;
    
    contextMenu.innerHTML = content;
    contextMenu.style.display = 'block';
    contextMenu.style.left = e.pageX + 'px';
    contextMenu.style.top = e.pageY + 'px';
    
    // Adjust position if menu goes off screen
    const rect = contextMenu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
        contextMenu.style.left = (e.pageX - rect.width) + 'px';
    }
    if (rect.bottom > window.innerHeight) {
        contextMenu.style.top = (e.pageY - rect.height) + 'px';
    }
}

/**
 * Load context menu scripts dynamically to avoid initialization errors
 */
function loadContextMenuScripts() {
    // Load openModals.js first
    const openModalsScript = document.createElement('script');
    openModalsScript.src = 'functions/hostApmFunctions/openModals.js';
    openModalsScript.onload = function() {
        // Then load contextMenu.js
        const contextMenuScript = document.createElement('script');
        contextMenuScript.src = 'functions/hostApmFunctions/contextMenu.js';
        contextMenuScript.onload = function() {
            // Override just the hostip parameter lookup for delete service
            setTimeout(() => {
                const originalCallDeleteService = window.callDeleteService;
                window.callDeleteService = function(ipAddress, serviceName) {
                    // Use our stored hostname instead of the URL parameter
                    const correctHostname = window.currentHostname || window.currentHostIP || ipAddress;
                    return originalCallDeleteService(correctHostname, serviceName);
                };
            }, 100);
        };
        document.head.appendChild(contextMenuScript);
    };
    document.head.appendChild(openModalsScript);
}

/**
 * Function to handle service deletion from context menu - REAL implementation
 */
function callDeleteService(ipAddress, serviceName) {
    // Use the current hostname/IP we stored when right-clicking
    const correctIP = window.currentHostIP || ipAddress;
    const actualServiceName = serviceName || window.currentServiceName;
    
    // Check if serviceName is an array for multiple services
    const isMultiple = Array.isArray(actualServiceName);
    const serviceCount = isMultiple ? actualServiceName.length : 1;
    const serviceText = isMultiple ? 
        (serviceCount > 1 ? `${serviceCount} selected services` : actualServiceName[0]) : 
        actualServiceName;
        
    if (!confirm(`Delete ${serviceText}?`)) {
        return;
    }
    
    const phpScriptUrl = 'delete_service.php';

    // Create a floating message element with consistent styling
    const floatingMessage = document.createElement('div');
    floatingMessage.id = 'service-delete-loading';
    floatingMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 8px;
        z-index: 10000;
        text-align: center;
    `;
    floatingMessage.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i>
        </div>
        <div>Deleting ${serviceText}...</div>
    `;

    document.body.appendChild(floatingMessage);

    const formData = new FormData();
    // Use IP address as required by delete_service.php
    formData.append('ip', correctIP);
    
    if (isMultiple) {
        // Add each service as array elements as expected by delete_service.php
        actualServiceName.forEach((service, index) => {
            formData.append(`servicetodelete[${index}]`, service);
        });
    } else {
        formData.append('servicetodelete', actualServiceName);
    }

    fetch(phpScriptUrl, {
        method: 'POST',
        body: formData,
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(data => {
            // Remove loading message
            const loadingElement = document.getElementById('service-delete-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            // Check if response starts with "Success:" as per delete_service.php
            if (typeof data === 'string' && data.trim().startsWith("Success:")) {
                // Show success message briefly then reload
                const successDiv = document.createElement('div');
                successDiv.style.cssText = floatingMessage.style.cssText;
                successDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-check-circle" style="font-size: 24px; color: #4caf50;"></i>
                    </div>
                    <div>${serviceText} deleted successfully!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Refreshing...</div>
                `;
                document.body.appendChild(successDiv);
                
                setTimeout(() => {
                    if (typeof populateHostListPage === 'function') {
                        Promise.all([
                            populateHostListPage(),
                            updateFeatureStatusIndicators(), // Consolidated update
                            successDiv.style.display = 'none'
                        ]);
                    }
                }, 3000);
            } else {
                // Show error message with server response
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = floatingMessage.style.cssText;
                errorDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #ff9800;"></i>
                    </div>
                    <div>Service deletion failed!</div>
                    <div style="font-size: 12px; margin-top: 5px;">${data.trim()}</div>
                `;
                document.body.appendChild(errorDiv);
                
                setTimeout(() => {
                    errorDiv.remove();
                }, 3000);
            }
        })
        .catch(error => {
            // Remove loading message
            const loadingElement = document.getElementById('service-delete-loading');
            if (loadingElement) {
                loadingElement.remove();
            }

            console.error('There was an error calling the PHP script:', error);
            
            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = floatingMessage.style.cssText;
            errorDiv.innerHTML = `
                <div style="margin-bottom: 10px;">
                    <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #f44336;"></i>
                </div>
                <div>An error occurred during service deletion</div>
                <div style="font-size: 12px; margin-top: 5px;">Please try again</div>
            `;
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 3000);
        });
}

/**
 * Function to handle host blacklisting from context menu - REAL implementation
 */
function blacklistHostApm(hostname) {
    // Close any open context menus
    const contextMenu = document.getElementById('custom-context-menu');
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
    
    // Close any open modals
    const modal = document.getElementById('service-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = "auto";
    }

    // Show a confirmation dialog to the user
    const isConfirmed = confirm("Are you sure you want to stop monitoring this host?");

    if (!isConfirmed) {
        return; // Exit the function if the user cancels
    }

    // Use the current hostname/IP we stored when right-clicking
    const ip = window.currentHostIP || hostname;
    const infra = new URLSearchParams(window.location.search).get('infra');

    if (!ip) {
        alert("Error: Could not determine host IP address.");
        return;
    }

    // Show loading message
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'blacklist-loading';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 8px;
        z-index: 10000;
        text-align: center;
    `;
    loadingDiv.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i>
        </div>
        <div>Blacklisting host...</div>
    `;
    document.body.appendChild(loadingDiv);

    // Send the request to blacklist the host
    const xhr = new XMLHttpRequest();
    const blacklistUrl = `blacklistHost.php?ip=${encodeURIComponent(ip)}${infra ? '&infra=' + encodeURIComponent(infra) : ''}`;
    
    xhr.open('GET', blacklistUrl, true);
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            // Remove loading message
            const loadingElement = document.getElementById('blacklist-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            if (xhr.status === 200) {
                // Show success message briefly then redirect
                const successDiv = document.createElement('div');
                successDiv.style.cssText = loadingDiv.style.cssText;
                successDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-check-circle" style="font-size: 24px; color: #4caf50;"></i>
                    </div>
                    <div>Host blacklisted successfully!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Refreshing...</div>
                `;
                document.body.appendChild(successDiv);
                
                // Refresh the page after 3 seconds
                setTimeout(() => {
                    if (typeof populateHostListPage === 'function') {
                        Promise.all([
                            populateHostListPage(),
                            updateFeatureStatusIndicators(), // Consolidated update
                            successDiv.style.display = 'none'
                        ]);
                    }
                }, 3000);
            } else {
                // Show error message
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = loadingDiv.style.cssText;
                errorDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #f44336;"></i>
                    </div>
                    <div>Error blacklisting host. Please try again.</div>
                `;
                document.body.appendChild(errorDiv);
                
                setTimeout(() => {
                    errorDiv.remove();
                }, 3000);
            }
        }
    };
    xhr.send();
} 