/**
 * Host List View functionality
 * Provides a list view of all hosts and their services
 */

// Status mappings from numeric codes to text representations
const statusMappingsText = {
    host: { 
        1: 'PENDING', 
        2: 'UP', 
        4: 'DOWN', 
        8: 'UNREACHABLE' 
    },
    service: { 
        1: 'PENDING', 
        2: 'OK', 
        4: 'WARNING', 
        8: 'UNKNOWN', 
        16: 'CRITICAL' 
    }
};

// Status mappings from numeric codes to CSS classes
const statusMappingsClass = {
    host: { 
        1: 'pending', 
        2: 'ok', 
        4: 'down', 
        8: 'unknown' 
    },
    service: { 
        1: 'pending', 
        2: 'ok', 
        4: 'warning', 
        8: 'unknown', 
        16: 'critical' 
    }
};

// Status mappings for API URLs
const statusMappingsAPI = {
    host: {
        'all': 'up+down+unreachable+pending',
        'ok': 'up',
        'down': 'down',
        'unknown': 'unreachable',
        'pending': 'pending'
    },
    // We'll use this to map individual statuses to API parameters
    service: {
        'ok': 'ok',
        'warning': 'warning',
        'critical': 'critical',
        'unknown': 'unknown',
        'pending': 'pending',
        'all': 'ok+warning+critical+unknown+pending'
    }
};

// Status counts cache (kept separate for UI updates)
const statusCounts = {
    hosts: {
        all: 0,
        ok: 0,
        down: 0,
        unknown: 0,
        pending: 0
    },
    services: {
        all: 0,
        ok: 0,
        warning: 0,
        critical: 0,
        unknown: 0,
        pending: 0
    }
};

/**
 * Fetch hostgroups list from Nagios API
 * @returns {Promise<Array>} - Array of hostgroup names
 */
async function fetchHostGroups() {
    try {
        const apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostgrouplist`;
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.result.type_code !== 0) {
            throw new Error(`API error: ${data.result.message}`);
        }
        
        return data.data.hostgrouplist || [];
    } catch (error) {
        console.error("Error fetching hostgroups:", error);
        return [];
    }
}

/**
 * Fetch host list data from Nagios API with status and hostgroup filtering
 * @param {string|string[]} statusFilter - The status filter(s) to apply ('all', 'ok', 'down', 'unknown', 'pending', or an array of these values)
 * @param {string} hostgroup - The hostgroup to filter by (optional)
 * @returns {Promise<Object>} - Host data
 */
async function fetchHostData(statusFilter = 'all', hostgroup = null) {
    // Convert single filter to array for consistent handling
    const filterArray = Array.isArray(statusFilter) ? statusFilter : [statusFilter];
    
    // Special case: if 'all' is included or empty array, just use 'all'
    const effectiveFilters = (filterArray.includes('all') || filterArray.length === 0) ? ['all'] : filterArray;

    try {
        let statusParam;
        if (effectiveFilters.includes('all')) {
            statusParam = statusMappingsAPI.host.all;
        } else {
            statusParam = effectiveFilters
                .map(filter => statusMappingsAPI.host[filter])
                .filter(Boolean)
                .join('+');
        }
        
        if (!statusParam) {
            statusParam = statusMappingsAPI.host.all; // Fallback if no valid filters
        }
        
        let apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=hostlist&details=true&hoststatus=${statusParam}`;
        
        // Add hostgroup parameter if specified
        if (hostgroup && hostgroup !== 'all') {
            apiUrl += `&hostgroup=${encodeURIComponent(hostgroup)}`;
        }
        
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.result.type_code !== 0) {
            throw new Error(`API error: ${data.result.message}`);
        }
        
        return data;
    } catch (error) {
        console.error("Error fetching host data:", error);
        throw error; // Rethrow to handle in the UI
    }
}

/**
 * Fetch service list data from Nagios API with status and hostgroup filtering
 * @param {string|string[]} statusFilters - The status filter(s) to apply ('all', 'ok', 'warning', 'critical', 'unknown', 'pending', or an array of these values)
 * @param {string} hostgroup - The hostgroup to filter by (optional)
 * @returns {Promise<Object>} - Service data
 */
async function fetchServiceData(statusFilters = 'all', hostgroup = null) {
    // Convert single filter to array for consistent handling
    const filterArray = Array.isArray(statusFilters) ? statusFilters : [statusFilters];
    
    // Special case: if 'all' is included, just use 'all'
    const effectiveFilters = filterArray.includes('all') ? ['all'] : filterArray;

    try {
        // Build the status parameter
        let statusParam;
        if (effectiveFilters.includes('all')) {
            statusParam = statusMappingsAPI.service.all;
        } else {
            statusParam = effectiveFilters
                .map(filter => statusMappingsAPI.service[filter])
                .filter(Boolean)
                .join('+');
        }
        
        if (!statusParam) {
            statusParam = statusMappingsAPI.service.all; // Fallback if no valid filters
        }
        
        let apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=servicelist&details=true&servicestatus=${statusParam}`;
        
        // Add hostgroup parameter if specified
        if (hostgroup && hostgroup !== 'all') {
            apiUrl += `&hostgroup=${encodeURIComponent(hostgroup)}`;
        }
        
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.result.type_code !== 0) {
            throw new Error(`API error: ${data.result.message}`);
        }
        
        return data;
    } catch (error) {
        console.error("Error fetching service data:", error);
        throw error; // Rethrow to handle in the UI
    }
}

/**
 * Fetch host object data from Nagios API to get IP addresses and action URLs
 * @param {string} hostgroup - The hostgroup to filter by (optional)
 * @returns {Promise<Object>} - Host object data with IP addresses and action URLs
 */
async function fetchHostObjectData(hostgroup = null) {
    try {
        let apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true`;
        
        // Add hostgroup parameter if specified
        if (hostgroup && hostgroup !== 'all') {
            apiUrl += `&hostgroup=${encodeURIComponent(hostgroup)}`;
        }
        
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.result.type_code !== 0) {
            throw new Error(`API error: ${data.result.message}`);
        }
        
        return data;
    } catch (error) {
        console.error("Error fetching host object data:", error);
        throw error;
    }
}

/**
 * Fetch service object data from Nagios API to get action URLs
 * @param {string} hostgroup - The hostgroup to filter by (optional)
 * @returns {Promise<Object>} - Service object data with action URLs
 */
async function fetchServiceObjectData(hostgroup = null) {
    try {
        let apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=servicelist&details=true`;
        
        // Add hostgroup parameter if specified
        if (hostgroup && hostgroup !== 'all') {
            apiUrl += `&hostgroup=${encodeURIComponent(hostgroup)}`;
        }
        
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.result.type_code !== 0) {
            throw new Error(`API error: ${data.result.message}`);
        }
        
        return data;
    } catch (error) {
        console.error("Error fetching service object data:", error);
        throw error;
    }
}

/**
 * Fetch host count data from Nagios API
 * @param {string} hostgroup - The hostgroup to filter by (optional)
 * @returns {Promise<Object>} - Host count data
 */
async function fetchHostCounts(hostgroup = null) {
    try {
        let apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=hostcount`;
        
        // Add hostgroup parameter if specified
        if (hostgroup && hostgroup !== 'all') {
            apiUrl += `&hostgroup=${encodeURIComponent(hostgroup)}`;
        }
        
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.result.type_code !== 0) {
            throw new Error(`API error: ${data.result.message}`);
        }
        
        return data.data.count;
    } catch (error) {
        console.error("Error fetching host counts:", error);
        throw error;
    }
}

/**
 * Fetch service count data from Nagios API
 * @param {string} hostgroup - The hostgroup to filter by (optional)
 * @returns {Promise<Object>} - Service count data
 */
async function fetchServiceCounts(hostgroup = null) {
    try {
        let apiUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi?query=servicecount`;
        
        // Add hostgroup parameter if specified
        if (hostgroup && hostgroup !== 'all') {
            apiUrl += `&hostgroup=${encodeURIComponent(hostgroup)}`;
        }
        
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.result.type_code !== 0) {
            throw new Error(`API error: ${data.result.message}`);
        }
        
        return data.data.count;
    } catch (error) {
        console.error("Error fetching service counts:", error);
        throw error;
    }
}

/**
 * Build a combined data structure with hosts and their services
 * @param {string|string[]} hostFilter - Host status filter(s)
 * @param {string|string[]} serviceFilters - Service status filter(s)
 * @param {string} hostgroup - The hostgroup to filter by (optional)
 * @returns {Promise<Array>} - Array of host objects with their services
 */
async function buildHostServiceData(hostFilter = 'all', serviceFilters = 'all', hostgroup = null) {
    try {
        // Show loading indicator
        const contentArea = document.getElementById('hostlist-content');
        if (contentArea) {
            contentArea.innerHTML = '<div class="hostlist-loading"><div class="spinner"></div><div>Loading hosts and services...</div></div>';
        }
        
        // Get host info from objectjson.cgi (for IPs), filtered hosts, filtered services, and service objects
        const [hostObjectData, hostData, serviceData, serviceObjectData] = await Promise.all([
            fetchHostObjectData(hostgroup), // This uses objectjson.cgi and has correct IP addresses and action URLs
            fetchHostData(hostFilter, hostgroup), // This uses statusjson.cgi with filtered host status
            fetchServiceData(serviceFilters, hostgroup), // This uses statusjson.cgi with filtered service status
            fetchServiceObjectData(hostgroup) // This gets service objects with action URLs
        ]).catch(error => {
            // Handle the error and display it in the UI
            if (contentArea) {
                contentArea.innerHTML = `<div class="hostlist-error">Error fetching data: ${error.message}</div>`;
            }
            throw error;
        });
        
        if (!hostObjectData || !hostObjectData.data || !hostObjectData.data.hostlist) {
            throw new Error("Invalid host object data received from API");
        }
        
        if (!hostData || !hostData.data || !hostData.data.hostlist) {
            throw new Error("Invalid host status data received from API");
        }
        
        if (!serviceData || !serviceData.data || !serviceData.data.servicelist) {
            throw new Error("Invalid service data received from API");
        }
        
        if (!serviceObjectData || !serviceObjectData.data || !serviceObjectData.data.servicelist) {
            throw new Error("Invalid service object data received from API");
        }
        
        // Create IP and action_url mapping from objectjson
        const hostInfoMap = {};
        for (const [hostName, hostObj] of Object.entries(hostObjectData.data.hostlist)) {
            hostInfoMap[hostName] = {
                address: hostObj.address || hostName,
                action_url: hostObj.action_url || ''
            };
        }
        
        // Create service action_url mapping
        const serviceInfoMap = {};
        for (const [hostName, services] of Object.entries(serviceObjectData.data.servicelist)) {
            serviceInfoMap[hostName] = {};
            for (const [serviceName, serviceObj] of Object.entries(services)) {
                serviceInfoMap[hostName][serviceName] = {
                    action_url: serviceObj.action_url || ''
                };
            }
        }
        
        // Process and combine the data
        const combinedData = [];
        
        // Get the list of hosts that have the filtered services
        const hostsWithFilteredServices = Object.keys(serviceData.data.servicelist);
        
        // Only include hosts that:
        // 1. Are in the filtered host list (from host API)
        // 2. Have the filtered services (from service API) OR service filter is 'all'
        for (const [hostName, hostInfo] of Object.entries(hostData.data.hostlist)) {
            // If service filter is not 'all', only include hosts that have matching services
            const isAllServiceFilter = serviceFilters === 'all' || 
                                      (Array.isArray(serviceFilters) && serviceFilters.includes('all'));
                                      
            if (!isAllServiceFilter && !hostsWithFilteredServices.includes(hostName)) {
                continue; // Skip this host - it doesn't have the filtered services
            }
            
            const hostObj = {
                name: hostName,
                display_name: hostName, // Use hostname, not the long display_name
                address: hostInfoMap[hostName]?.address || hostName, // Use IP from objectjson API
                action_url: hostInfoMap[hostName]?.action_url || '', // Add action_url from objectjson API
                status: hostInfo.status,
                status_text: statusMappingsText.host[hostInfo.status] || 'UNKNOWN',
                status_class: statusMappingsClass.host[hostInfo.status] || 'unknown',
                plugin_output: hostInfo.plugin_output || '',
                last_check: new Date(hostInfo.last_check * 1),
                duration: calculateDuration(hostInfo.last_state_change),
                attempt: `${hostInfo.current_attempt}/${hostInfo.max_attempts}`,
                // Add status information fields for icons
                checks_enabled: hostInfo.checks_enabled,
                flap_detection_enabled: hostInfo.flap_detection_enabled,
                notifications_enabled: hostInfo.notifications_enabled,
                accept_passive_checks: hostInfo.accept_passive_checks,
                event_handler_enabled: hostInfo.event_handler_enabled,
                scheduled_downtime_depth: hostInfo.scheduled_downtime_depth,
                obsess: hostInfo.obsess,
                problem_has_been_acknowledged: hostInfo.problem_has_been_acknowledged,
                is_flapping: hostInfo.is_flapping,
                services: []
            };
            
            combinedData.push(hostObj);
        }
        
        // Create a map for quick host lookup
        const hostMap = {};
        combinedData.forEach(host => {
            hostMap[host.name] = host;
        });
        
        // Add filtered services to their respective hosts
        for (const [hostName, services] of Object.entries(serviceData.data.servicelist)) {
            // Only add services to hosts that are in our final host list
            if (hostMap[hostName]) {
                // Add services for this host
                for (const [serviceName, serviceInfo] of Object.entries(services)) {
                    const serviceObj = {
                        name: serviceName,
                        action_url: serviceInfoMap[hostName]?.[serviceName]?.action_url || '', // Add action_url
                        status: serviceInfo.status,
                        status_text: statusMappingsText.service[serviceInfo.status] || 'UNKNOWN',
                        status_class: statusMappingsClass.service[serviceInfo.status] || 'unknown',
                        plugin_output: serviceInfo.plugin_output || '',
                        last_check: new Date(serviceInfo.last_check * 1),
                        duration: calculateDuration(serviceInfo.last_state_change),
                        attempt: `${serviceInfo.current_attempt}/${serviceInfo.max_attempts}`,
                        // Add status information fields for icons
                        checks_enabled: serviceInfo.checks_enabled,
                        flap_detection_enabled: serviceInfo.flap_detection_enabled,
                        notifications_enabled: serviceInfo.notifications_enabled,
                        accept_passive_checks: serviceInfo.accept_passive_checks,
                        event_handler_enabled: serviceInfo.event_handler_enabled,
                        scheduled_downtime_depth: serviceInfo.scheduled_downtime_depth,
                        obsess: serviceInfo.obsess,
                        problem_has_been_acknowledged: serviceInfo.problem_has_been_acknowledged,
                        is_flapping: serviceInfo.is_flapping
                    };
                    
                    hostMap[hostName].services.push(serviceObj);
                }
            }
        }
        
        // Sort the combined data
        combinedData.sort((a, b) => {
            // Sort by status severity first (DOWN > UNREACHABLE > PENDING > UP)
            if (a.status !== b.status) {
                return b.status - a.status;
            }
            // Then sort by name
            return a.name.localeCompare(b.name);
        });
        
        // For each host, sort services by status severity
        combinedData.forEach(host => {
            host.services.sort((a, b) => {
                if (a.status !== b.status) {
                    return b.status - a.status;
                }
                return a.name.localeCompare(b.name);
            });
        });
        
        // Fetch missing plugin_output from status.dat for hosts/services where it's empty
        const statusDatPromises = combinedData.map(async (host) => {
            const hostNeedsUpdate = !host.plugin_output;
            const servicesNeedUpdate = host.services.some(service => !service.plugin_output);

            if (!hostNeedsUpdate && !servicesNeedUpdate) {
                return; // No update needed for this host
            }

            try {
                const response = await fetch(`get_full_status.php?hostname=${encodeURIComponent(host.name)}`);
                if (!response.ok) {
                    console.error(`Failed to fetch status.dat for ${host.name}: ${response.statusText}`);
                    return;
                }
                
                const statusDatData = await response.json();
                if (statusDatData.error) {
                    // It's common for a host to not be in status.dat if it's pending, so this is a warn not an error
                    if (!statusDatData.error.includes('Host not found')) {
                        console.warn(`Could not process status.dat for ${host.name}: ${statusDatData.error}`);
                    }
                    return;
                }

                // Update host plugin_output if needed
                if (hostNeedsUpdate && statusDatData.hoststatus) {
                    host.plugin_output = statusDatData.hoststatus.plugin_output || '';
                }

                // Update services plugin_output if needed
                if (servicesNeedUpdate && statusDatData.servicestatus) {
                    const serviceStatusMap = statusDatData.servicestatus.reduce((acc, s) => {
                        acc[s.service_description] = s;
                        return acc;
                    }, {});

                    host.services.forEach(service => {
                        if (!service.plugin_output && serviceStatusMap[service.name]) {
                            const serviceDat = serviceStatusMap[service.name];
                            service.plugin_output = serviceDat.plugin_output || '';
                        }
                    });
                }
            } catch (error) {
                console.error(`Error processing status.dat info for ${host.name}:`, error);
            }
        });

        await Promise.all(statusDatPromises);
        
        return combinedData;
    } catch (error) {
        console.error("Error building host service data:", error);
        // Make sure error is shown in the UI
        const contentArea = document.getElementById('hostlist-content');
        if (contentArea) {
            contentArea.innerHTML = `<div class="hostlist-error">Error: ${error.message}</div>`;
        }
        return [];
    }
}

/**
 * Calculate the duration between a timestamp and now
 * @param {Number} timestamp - The timestamp in milliseconds
 * @returns {String} - Formatted duration string
 */
function calculateDuration(timestamp) {
    if (!timestamp) return 'N/A';
    
    const now = Date.now();
    const timeElapsed = now - timestamp * 1; // Convert to milliseconds
    
    // Convert to appropriate units
    const seconds = Math.floor(timeElapsed / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
        return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
        return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

/**
 * Format a date object for display
 * @param {Date} date - The date to format
 * @returns {String} - Formatted date string
 */
function formatDate(date) {
    if (!date || !(date instanceof Date) || isNaN(date)) {
        return 'N/A';
    }
    
    return date.toLocaleString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * Open a host or service in the modal iframe
 * @param {String} hostname - The hostname 
 * @param {String} ip - The IP address
 * @param {String} subnet - The subnet
 */
function openHostInModal(hostname, ip, subnet) {
    // Use the same modal opening approach as in fetchHostsBubbles.js
    const urlParams = new URLSearchParams(window.location.search);
    const infraParam = urlParams.get('infra');
    const url = `host.php?nickname=${encodeURIComponent(hostname)}&ip=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(infraParam)}&hostip=${encodeURIComponent(ip)}&subnet=${encodeURIComponent(subnet || 'External')}`;
    
    // Use the showModal function if it exists, otherwise fall back to window.location
    if (typeof showModal === 'function') {
        showModal(url);
    } else {
        // Fallback implementation of showModal
        const modal = document.getElementById('infoModal');
        const iframe = document.getElementById('modal-frame') || document.getElementById('iframeModal-frame');
        
        if (modal && iframe) {
            iframe.src = url;
            modal.style.display = 'block';
            modal.classList.add('show');
        } else {
            window.location.href = url;
        }
    }
}

/**
 * Calculate status counts using the dedicated count APIs
 * @param {string} hostgroup - The hostgroup to filter by (optional)
 * @returns {Promise<Object>} - Object containing separate status counts
 */
async function fetchStatusCounts(hostgroup = null) {
    try {
        const [hostCounts, serviceCounts] = await Promise.all([
            fetchHostCounts(hostgroup),
            fetchServiceCounts(hostgroup)
        ]);
        
        return {
            hosts: {
                all: hostCounts.up + hostCounts.down + hostCounts.unreachable + hostCounts.pending,
                ok: hostCounts.up,
                down: hostCounts.down,
                unknown: hostCounts.unreachable,
                pending: hostCounts.pending
            },
            services: {
                all: serviceCounts.ok + serviceCounts.warning + serviceCounts.critical + serviceCounts.unknown + serviceCounts.pending,
                ok: serviceCounts.ok,
                warning: serviceCounts.warning,
                critical: serviceCounts.critical,
                unknown: serviceCounts.unknown,
                pending: serviceCounts.pending
            }
        };
    } catch (error) {
        console.error("Error fetching status counts:", error);
        return {
            hosts: { all: 0, ok: 0, down: 0, unknown: 0, pending: 0 },
            services: { all: 0, ok: 0, warning: 0, critical: 0, unknown: 0, pending: 0 }
        };
    }
} 