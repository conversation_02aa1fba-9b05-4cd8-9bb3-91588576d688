/*
 * Export Hostlist table to CSV
 * Grabs the currently visible (filtered) rows of the hostlist table and downloads
 * them as a CSV file. Hidden / filtered-out rows are skipped.
 */
function exportHostListToCSV() {
    const table = document.querySelector('.hostlist-table');
    if (!table) {
        alert('No data table available to export.');
        return;
    }

    const csvRows = [];

    // Process header row separately to preserve column titles
    const headerCells = Array.from(table.querySelectorAll('thead th'));
    const headerValues = headerCells.map(th => sanitizeCSVCell(th.innerText));
    csvRows.push(headerValues.join(','));

    // Process body rows – include only those that are currently visible
    const bodyRows = table.querySelectorAll('tbody tr');
    bodyRows.forEach(row => {
        // Skip rows filtered out or not visible (offsetParent === null when display:none)
        if (row.classList.contains('filtered-out') || row.offsetParent === null) {
            return;
        }

        // Collect cell texts
        const cells = Array.from(row.cells);
        const values = cells.map(cell => sanitizeCSVCell(cell.innerText));
        csvRows.push(values.join(','));
    });

    if (csvRows.length <= 1) { // Only header present
        alert('No visible rows to export.');
        return;
    }

    const csvContent = '\uFEFF' + csvRows.join('\n'); // Prepend BOM for Excel compatibility
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `hostlist_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// Utility – escape/quote cell values if they contain special characters
function sanitizeCSVCell(text) {
    if (text === null || text === undefined) return '';
    let value = text.replace(/\n|\r/g, ' ').trim();
    value = value.replace(/"/g, '""'); // Escape quotes
    if (/[",]/.test(value)) {
        value = `"${value}"`;
    }
    return value;
}

// Attach click handler once DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const btn = document.getElementById('export-csv-btn');
    if (btn) {
        btn.addEventListener('click', exportHostListToCSV);
    }
}); 