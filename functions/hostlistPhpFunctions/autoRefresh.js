/**
 * Auto-refresh functionality for Hostlist view
 * Handles countdown timer and periodic data refresh
 */

/**
 * Initialize auto-refresh functionality
 */
function initAutoRefresh() {
    let countdownValue = 60;
    let isPaused = false; // Track whether auto-refresh is paused
    const countdownTimer = document.getElementById('countdown-timer');
    const refreshCountdown = document.getElementById('refresh-countdown');
    
    // Create pause/resume button and append it to the refresh countdown element
    const pauseBtn = document.createElement('i');
    pauseBtn.id = 'pause-refresh-btn';
    pauseBtn.className = 'fa fa-pause';
    pauseBtn.title = 'Pause auto-refresh';
    pauseBtn.style.cursor = 'pointer';
    pauseBtn.style.marginLeft = '6px';
    refreshCountdown.appendChild(pauseBtn);

    // Toggle pause/resume behaviour
    pauseBtn.addEventListener('click', function(event) {
        event.stopPropagation(); // Prevent triggering the manual refresh on parent element
        isPaused = !isPaused;

        if (isPaused) {
            this.classList.remove('fa-pause');
            this.classList.add('fa-play');
            this.title = 'Resume auto-refresh';
            countdownTimer.textContent = 'Paused';
            refreshCountdown.classList.add('paused');
        } else {
            this.classList.remove('fa-play');
            this.classList.add('fa-pause');
            this.title = 'Pause auto-refresh';
            countdownTimer.textContent = countdownValue + 's';
            refreshCountdown.classList.remove('paused');
        }
    });
    
    function updateCountdown() {
        if (isPaused) return; // Skip countdown when paused

        countdownValue--;
        countdownTimer.textContent = countdownValue + 's';
        
        if (countdownValue <= 0) {
            countdownValue = 60;
            refreshCountdown.classList.add('refreshing');
            Promise.all([
                populateHostListPage(),
                updateFeatureStatusIndicators() // Consolidated update
            ]).finally(() => {
                refreshCountdown.classList.remove('refreshing');
            });
        }
    }
    
    const countdownInterval = setInterval(updateCountdown, 1000);
    
    refreshCountdown.addEventListener('click', function() {
        countdownValue = 60;
        countdownTimer.textContent = countdownValue + 's'; // Always reset text, even if paused
        refreshCountdown.classList.add('refreshing');
        Promise.all([
            populateHostListPage(),
            updateFeatureStatusIndicators() // Consolidated update
        ]).finally(() => {
            refreshCountdown.classList.remove('refreshing');
        });
    });
} 