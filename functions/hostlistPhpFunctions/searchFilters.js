/**
 * Search and Filter functionality for Hostlist view
 * Provides filtering by search terms, status and hostgroups
 */

/**
 * Filter the host list based on search term only (status filtering is done at API level)
 */
function filterHostListBySearch() {
    const searchInput = document.getElementById('searchBar-list');
    const searchMode = document.getElementById('search-mode').value;
    const rawValue = searchInput.value.toLowerCase();

    // Determine exact-match search (quoted)
    const isExactMatch = ((rawValue.startsWith('"') && rawValue.endsWith('"')) || (rawValue.startsWith("'") && rawValue.endsWith("'"))) && rawValue.length > 2;
    const searchTerm = isExactMatch ? rawValue.substring(1, rawValue.length - 1).toLowerCase() : rawValue.trim();

    if (!window.fullHostListData) {
        console.warn('Host list data not loaded yet');
        return;
    }

    // If empty search just reset
    if (searchTerm === '') {
        window.filteredHostListData = window.fullHostListData;
        window.currentPage = 1;
        window.renderHostList();
        return;
    }

    const filteredHosts = [];

    window.fullHostListData.forEach(host => {
        const hostNameLower = host.display_name.toLowerCase();
        const hostIpLower = host.address.toLowerCase();
        const bubbleNicknameLower = (typeof bubbleHostnamesMap !== 'undefined' && bubbleHostnamesMap && bubbleHostnamesMap[host.address]) ? bubbleHostnamesMap[host.address].toLowerCase() : '';

        if (searchMode === 'host') {
            let hostMatches = false;
            if (isExactMatch) {
                hostMatches = hostNameLower === searchTerm || hostIpLower === searchTerm || bubbleNicknameLower === searchTerm;
            } else {
                hostMatches = hostNameLower.includes(searchTerm) || hostIpLower.includes(searchTerm) || bubbleNicknameLower.includes(searchTerm);
            }
            if (hostMatches) {
                filteredHosts.push({ ...host }); // include all services
            }
        } else { // service mode
            const matchedServices = host.services.filter(service => {
                const svcNameLower = service.name.toLowerCase();
                if (isExactMatch) {
                    return svcNameLower === searchTerm;
                }
                return svcNameLower.includes(searchTerm);
            });
            if (matchedServices.length > 0) {
                filteredHosts.push({ ...host, services: matchedServices });
            }
        }
    });

    window.filteredHostListData = filteredHosts;
    window.currentPage = 1;
    window.renderHostList();
}

/**
 * Update status filter buttons with counts
 * @param {Object} counts - Status counts object with separate host and service counts
 */
function updateStatusFilterCounts(counts) {
    // Update host filter buttons
    const hostFilterButtons = document.querySelectorAll('.hostlist-status-filter[data-type="host"]');
    hostFilterButtons.forEach(btn => {
        const status = btn.getAttribute('data-status');
        const count = counts.hosts[status] || 0;
        
        // Skip setting count on the "All" button
        if (status !== 'all') {
            btn.textContent = count;
        }
        
        // Add the count to title for all buttons
        const title = btn.getAttribute('title');
        const baseTitle = title.split(' (')[0]; // Remove existing count if any
        btn.setAttribute('title', `${baseTitle} (${count})`);
    });
    
    // Update service filter buttons
    const serviceFilterButtons = document.querySelectorAll('.hostlist-status-filter[data-type="service"]');
    serviceFilterButtons.forEach(btn => {
        const status = btn.getAttribute('data-status');
        const count = counts.services[status] || 0;
        
        // Skip setting count on the "All" button
        if (status !== 'all') {
            btn.textContent = count;
        }
        
        // Add the count to title for all buttons
        const title = btn.getAttribute('title');
        const baseTitle = title.split(' (')[0]; // Remove existing count if any
        btn.setAttribute('title', `${baseTitle} (${count})`);
    });
}

/**
 * Toggle visibility of service rows for a host
 * @param {String} hostId - The host ID/name
 */
function toggleServices(hostId) {
    const hostRow = document.querySelector(`.hostlist-host-row[data-host-id="${hostId}"]`);
    const serviceRows = document.querySelectorAll(`.hostlist-service-row[data-host-id="${hostId}"]`);
    const toggleIcon = hostRow.querySelector('.toggle-services i');
    
    // Toggle collapsed state on the host row
    hostRow.classList.toggle('collapsed');
    
    if (hostRow.classList.contains('collapsed')) {
        // Hide service rows
        serviceRows.forEach(row => {
            row.classList.add('collapsed');
        });
        // Change icon to indicate expansion is possible
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-right');
    } else {
        // Show service rows
        serviceRows.forEach(row => {
            row.classList.remove('collapsed');
        });
        // Change icon to indicate collapse is possible
        toggleIcon.classList.remove('fa-chevron-right');
        toggleIcon.classList.add('fa-chevron-down');
    }
} 