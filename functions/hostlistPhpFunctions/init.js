/**
 * Initialization and setup functionality for Hostlist view
 * Provides main entry points and setup for the list view
 */

/**
 * Map of IP address => Bubble View hostname (nickname)
 * Fetched once per page load from get_bubble_hostnames.php
 */
let bubbleHostnamesMap = null;

// Insert global pagination variables just after bubbleHostnamesMap declaration
const HOSTS_PER_PAGE = 50;
let fullHostListData = [];
let filteredHostListData = [];
let currentPage = 1;

// Expose for other modules
window.HOSTS_PER_PAGE = HOSTS_PER_PAGE;
window.fullHostListData = fullHostListData;
window.filteredHostListData = filteredHostListData;
window.currentPage = currentPage;

/**
 * Fetch Bubble View hostnames from backend database.
 * Returns a map keyed by IP where value is the Bubble View hostname.
 * The result is cached in bubbleHostnamesMap for subsequent calls.
 */
async function fetchBubbleHostnames() {
    if (bubbleHostnamesMap !== null) {
        // Already fetched (or attempted) – reuse cached value
        return bubbleHostnamesMap;
    }
    try {
        const response = await fetch('get_bubble_hostnames.php');
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        bubbleHostnamesMap = await response.json();
    } catch (error) {
        console.error('Error fetching Bubble View hostnames:', error);
        // Prevent further fetch attempts in case of repeated failures
        bubbleHostnamesMap = {};
    }
    return bubbleHostnamesMap;
}

/**
 * Initialize page-specific functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize host list data
    initHostList();
    
    // Set up event listeners
    setupEventListeners();
    
    // Initialize combined feature status indicators
    initFeatureStatusIndicators(); 
    
    // Setup popover handlers
    setupPopoverHandlers();
    
    // Initialize auto-refresh functionality
    initAutoRefresh();
    
    // Setup mobile header layout
    setupMobileHeaderLayout();
    
    // Initialize context menu functionality
    initContextMenu();
    
    // Setup modal event handlers
    setupModalEventHandlers();
    
    // Load context menu scripts after DOM is ready
    loadContextMenuScripts();
});

/**
 * Initialize the host list
 */
async function initHostList() {
    await populateHostGroups();
    await populateHostListPage();
}

/**
 * Populate hostgroups dropdown
 */
async function populateHostGroups() {
    try {
        const hostgroups = await fetchHostGroups();
        const hostgroupSelect = document.getElementById('hostgroup-filter');
        
        if (hostgroupSelect && hostgroups.length > 0) {
            // Clear existing options except "All Hostgroups"
            const allOption = hostgroupSelect.querySelector('option[value="all"]');
            hostgroupSelect.innerHTML = '';
            hostgroupSelect.appendChild(allOption);
            
            // Add hostgroup options
            hostgroups.forEach(hostgroup => {
                const option = document.createElement('option');
                option.value = hostgroup;
                option.textContent = hostgroup;
                hostgroupSelect.appendChild(option);
            });
            
            // Check if hostgroup parameter is in URL and set it as selected
            const urlParams = new URLSearchParams(window.location.search);
            const urlHostgroup = urlParams.get('hostgroup');
            if (urlHostgroup && hostgroups.includes(urlHostgroup)) {
                hostgroupSelect.value = urlHostgroup;
                console.log(`Auto-selected hostgroup from URL: ${urlHostgroup}`);
            }
        }
    } catch (error) {
        console.error('Error populating hostgroups:', error);
    }
}

/**
 * Populate the host list data on the page
 */
async function populateHostListPage() {
    const contentArea = document.getElementById('hostlist-content');
    
    try {
        // Get current filter selections for hosts - now supports multiple
        const activeHostFilters = document.querySelectorAll('.hostlist-status-filter[data-type="host"].active');
        const activeServiceFilters = document.querySelectorAll('.hostlist-status-filter[data-type="service"].active');
        
        // Get all active host filters or use 'all' if none are active
        const hostFilters = Array.from(activeHostFilters).map(btn => btn.getAttribute('data-status'));
        const hostFilterValue = hostFilters.length > 0 ? hostFilters : 'all';
        
        // Get all active service filters or use 'all' if none are active
        const serviceFilters = Array.from(activeServiceFilters).map(btn => btn.getAttribute('data-status'));
        const serviceFilterValue = serviceFilters.length > 0 ? serviceFilters : 'all';

        // Get selected hostgroup
        const hostgroupSelect = document.getElementById('hostgroup-filter');
        const selectedHostgroup = hostgroupSelect ? hostgroupSelect.value : 'all';

        // Ensure Bubble View nicknames are loaded before rendering
        await fetchBubbleHostnames();

        const data = await buildHostServiceData(hostFilterValue, serviceFilterValue, selectedHostgroup);

        // --- Pagination & Client-side Rendering Setup ---
        // Store full data for global use (search / pagination)
        window.fullHostListData = data;
        window.filteredHostListData = data;

        // Update status counts in filter buttons (same behaviour as before)
        const statusCountsData = await fetchStatusCounts(selectedHostgroup);
        updateStatusFilterCounts(statusCountsData);

        if (!data || data.length === 0) {
            contentArea.innerHTML = '<div class="hostlist-empty">No hosts found</div>';
            renderPaginationControls(0);
            return;
        }

        // Reset to first page whenever we pull fresh data
        currentPage = 1;

        // Render first page
        renderHostList();

        // Re-apply search term (if any) so that search persists across refreshes
        const searchInput = document.getElementById('searchBar-list');
        if (searchInput && searchInput.value.trim() !== '') {
            filterHostListBySearch();
        }

        // Skip legacy (full list) DOM rendering below – pagination handles this
        return;
    } catch (error) {
        console.error("Error populating host list:", error);
        contentArea.innerHTML = `<div class="hostlist-error">Error loading data: ${error.message}</div>`;
        throw error; // Re-throw to ensure promise rejection
    }
}

/**
 * Populate status icons for hosts and services in the hostlist table
 * @param {Array} data - Array of host objects with their services
 */
async function populateHostListStatusIcons(data) {
    // Icon mappings from hostApm.js
    const iconMappings = {
        host: [
            {
                condition: (item) => item.checks_enabled === false,
                iconClass: 'fa-times-circle',
                tooltip: 'Active checks are off'
            },
            {
                condition: (item) => item.flap_detection_enabled === false,
                iconClass: 'fa-flag',
                tooltip: 'Flap detection is off'
            },
            {
                condition: (item) => item.notifications_enabled === false,
                iconClass: 'fa-bell-slash',
                tooltip: 'Notifications are off'
            },
            {
                condition: (item) => item.accept_passive_checks === false,
                iconClass: 'fa-eye-slash',
                tooltip: 'Passive checks are off'
            },
            {
                condition: (item) => item.event_handler_enabled === false,
                iconClass: 'fa-toggle-off',
                tooltip: 'Event handler is off'
            },
            {
                condition: (item) => Number(item.scheduled_downtime_depth) !== 0,
                iconClass: 'fa-moon-o',
                tooltip: 'In scheduled downtime'
            },
            {
                condition: (item) => item.obsess === false,
                iconClass: 'fa-meh-o',
                tooltip: 'Obsessing is off'
            },
            {
                condition: (item) => item.problem_has_been_acknowledged === true,
                iconClass: 'fa-gavel',
                tooltip: 'Problem has been acknowledged'
            }
        ],
        service: [
            {
                condition: (item) => item.checks_enabled === false,
                iconClass: 'fa-times-circle',
                tooltip: 'Active checks are off'
            },
            {
                condition: (item) => item.flap_detection_enabled === false,
                iconClass: 'fa-flag',
                tooltip: 'Flap detection is off'
            },
            {
                condition: (item) => item.notifications_enabled === false,
                iconClass: 'fa-bell-slash',
                tooltip: 'Notifications are off'
            },
            {
                condition: (item) => item.accept_passive_checks === false,
                iconClass: 'fa-eye-slash',
                tooltip: 'Passive checks are off'
            },
            {
                condition: (item) => item.event_handler_enabled === false,
                iconClass: 'fa-toggle-off',
                tooltip: 'Event handler is off'
            },
            {
                condition: (item) => Number(item.scheduled_downtime_depth) !== 0,
                iconClass: 'fa-moon-o',
                tooltip: 'In scheduled downtime'
            },
            {
                condition: (item) => item.obsess === false,
                iconClass: 'fa-meh-o',
                tooltip: 'Obsessing is off'
            },
            {
                condition: (item) => item.problem_has_been_acknowledged === true,
                iconClass: 'fa-gavel',
                tooltip: 'Problem has been acknowledged'
            }
        ]
    };
    
    // Process each host
    data.forEach(host => {
        // Find host icon container
        const hostIconContainer = document.querySelector(`.host-status-icons[data-host-name="${host.name}"]`);
        if (hostIconContainer) {
            // Clear existing icons
            hostIconContainer.innerHTML = '';
            
            // Add host status icons
            iconMappings.host.forEach(mapping => {
                if (mapping.condition(host)) {
                    const icon = document.createElement('i');
                    icon.className = `fa ${mapping.iconClass} hostlist-status-icon`;
                    icon.title = mapping.tooltip;
                    hostIconContainer.appendChild(icon);
                }
            });
        }
        
        // Process services for this host
        host.services.forEach(service => {
            const serviceIconContainer = document.querySelector(`.service-status-icons[data-host-name="${host.name}"][data-service-name="${service.name}"]`);
            if (serviceIconContainer) {
                // Clear existing icons
                serviceIconContainer.innerHTML = '';
                
                // Add service status icons
                iconMappings.service.forEach(mapping => {
                    if (mapping.condition(service)) {
                        const icon = document.createElement('i');
                        icon.className = `fa ${mapping.iconClass} hostlist-status-icon`;
                        icon.title = mapping.tooltip;
                        serviceIconContainer.appendChild(icon);
                    }
                });
            }
        });
    });
}

/**
 * Set up event listeners for the page
 */
function setupEventListeners() {
    const searchInput = document.getElementById('searchBar-list');
    const searchModeSelect = document.getElementById('search-mode');
    const clearSearchBtn = document.querySelector('.hostlist-search-container .hostlist-clear-search');
    const hostFilters = document.querySelectorAll('.hostlist-status-filter[data-type="host"]');
    const serviceFilters = document.querySelectorAll('.hostlist-status-filter[data-type="service"]');
    const resetAllFilters = document.getElementById('resetAllFilters');
    const hostlistContent = document.getElementById('hostlist-content');
    const hostgroupSelect = document.getElementById('hostgroup-filter');
    
    // Search input
    searchInput.addEventListener('input', () => {
        filterHostListBySearch();
    });
    
    // Search mode select
    searchModeSelect.addEventListener('change', () => {
        // Update placeholder text based on mode
        const mode = searchModeSelect.value;
        if (mode === 'host') {
            searchInput.placeholder = "Search hosts...";
        } else if (mode === 'service') {
            searchInput.placeholder = "Search services...";
        }
        
        // Apply filtering with new mode
        filterHostListBySearch();
    });
    
    // Clear search
    clearSearchBtn.addEventListener('click', () => {
        searchInput.value = '';
        filterHostListBySearch();
    });
    
    // Reset all filters button
    resetAllFilters.addEventListener('click', async () => {
        // Clear all filters and refresh
        hostFilters.forEach(btn => btn.classList.remove('active'));
        serviceFilters.forEach(btn => btn.classList.remove('active'));
        
        // Reset hostgroup filter
        if (hostgroupSelect) {
            hostgroupSelect.value = 'all';
        }
        
        await populateHostListPage();
    });
    
    // Host status filters - now supports multi-selection
    hostFilters.forEach(btn => {
        btn.addEventListener('click', async () => {
            btn.classList.toggle('active');
            await populateHostListPage();
        });
    });
    
    // Service status filters - multi-selection already supported
    serviceFilters.forEach(btn => {
        btn.addEventListener('click', async (event) => {
            btn.classList.toggle('active');
            await populateHostListPage();
        });
    });
    
    // Hostgroup filter
    if (hostgroupSelect) {
        hostgroupSelect.addEventListener('change', async () => {
            await populateHostListPage();
        });
    }
}

// Helper: Render pagination controls (Prev / page numbers / Next)
function renderPaginationControls(totalPages) {
    let container = document.getElementById('hostlist-pagination');
    if (!container) {
        container = document.createElement('div');
        container.id = 'hostlist-pagination';
        container.className = 'hostlist-pagination';
        const parent = document.querySelector('.hostlist-container');
        if (parent) parent.appendChild(container);
    }

    if (totalPages <= 1) {
        container.style.display = 'none';
        return;
    }
    container.style.display = 'flex';

    let html = `<button class="page-btn prev-page" ${currentPage === 1 ? 'disabled' : ''}>Prev</button>`;

    for (let i = 1; i <= totalPages; i++) {
        // Show first, last, current, and two neighbours; collapse others with ellipsis
        if (i === 1 || i === totalPages || Math.abs(i - currentPage) <= 2) {
            html += `<button class="page-btn page-num ${i === currentPage ? 'active' : ''}" data-page="${i}">${i}</button>`;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            html += '<span class="page-ellipsis">…</span>';
        }
    }

    html += `<button class="page-btn next-page" ${currentPage === totalPages ? 'disabled' : ''}>Next</button>`;
    container.innerHTML = html;

    // Attach handlers
    container.querySelector('.prev-page')?.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderHostList();
        }
    });

    container.querySelector('.next-page')?.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            renderHostList();
        }
    });

    container.querySelectorAll('.page-num').forEach(btn => {
        btn.addEventListener('click', () => {
            const page = Number(btn.getAttribute('data-page'));
            currentPage = page;
            renderHostList();
        });
    });
}

// Helper: Build HTML table for a slice of host data
function generateTableHTML(hostArray) {
    // Build the table skeleton
    let tableHTML = `
        <table class="hostlist-table">
            <colgroup>
                <col class="col-host" style="width: 16%;">
                <col class="col-service" style="width: 16%;">
                <col class="col-status" style="width: 9%;">
                <col class="col-lastcheck" style="width: 15%;">
                <col class="col-duration" style="width: 8%;">
                <col class="col-attempt" style="width: 6%;">
                <col class="col-info" style="width: 30%;">
            </colgroup>
            <thead>
                <tr>
                    <th class="col-host">Host</th>
                    <th class="col-service">Service</th>
                    <th class="col-status">Status</th>
                    <th class="col-lastcheck">Last Check</th>
                    <th class="col-duration">Duration</th>
                    <th class="col-attempt">Attempt</th>
                    <th class="col-info">Status Information</th>
                </tr>
            </thead>
            <tbody>`;

    hostArray.forEach(host => {
        // Determine Bubble View nickname (if different)
        const bubbleName = bubbleHostnamesMap && bubbleHostnamesMap[host.address];
        const showBubbleName = bubbleName && bubbleName.trim() && bubbleName.toLowerCase() !== host.display_name.toLowerCase();

        tableHTML += `
            <tr class="hostlist-host-row expanded" data-host-id="${host.name}" data-hostname="${host.display_name}" data-ip="${host.address}" data-subnet="${host.subnet || 'External'}" data-status="${host.status_class}">
                <td class="col-host" title="${host.address} - ${host.status_text}" onclick="openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')">
                    ${host.action_url ? `<i class='fa fa-area-chart graph-icon' title='Performance data available' onclick="event.stopPropagation(); openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', true, 'performance')"></i>` : ''}
                    <span class="hostname ${host.status_class}">${host.display_name}</span>
                    ${showBubbleName ? `<span class="bubble-view-name" style="font-size:0.75em;color:#888;margin-left:4px;" title="Bubble View Name: ${bubbleName}">(${bubbleName})</span>` : ''}
                    <div class="hostlist-status-icons host-status-icons" data-host-name="${host.name}"></div>
                    ${host.services.length > 0 ? `<span class="toggle-services" onclick="event.stopPropagation(); toggleServices('${host.name}')"><i class="fa fa-chevron-down"></i></span>` : ''}
                </td>
                <td class="col-service" onclick="openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')"></td>
                <td class="col-status" onclick="openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')"></td>
                <td class="col-lastcheck" onclick="openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')">${formatDate(host.last_check)}</td>
                <td class="col-duration" onclick="openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')">${host.duration}</td>
                <td class="col-attempt" onclick="openHostInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}')">${host.attempt}</td>
                <td class="col-info">${host.plugin_output}</td>
            </tr>`;

        // Service rows
        host.services.forEach(service => {
            tableHTML += `
                <tr class="hostlist-service-row" data-host-id="${host.name}" data-hostname="${host.display_name}" data-ip="${host.address}" data-subnet="${host.subnet || 'External'}" data-status="${service.status_class}" data-service-name="${service.name}">
                    <td class="col-host" onclick="openServiceInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', '${service.name}')"></td>
                    <td class="col-service" onclick="openServiceInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', '${service.name}')">
                        ${service.action_url ? `<i class='fa fa-area-chart graph-icon' title='Performance data available' onclick="event.stopPropagation(); openServiceInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', '${service.name}', 'performance')"></i>` : ''}
                        <span class="service-name">${service.name}</span>
                        <div class="hostlist-status-icons service-status-icons" data-host-name="${host.name}" data-service-name="${service.name}"></div>
                    </td>
                    <td class="col-status" onclick="openServiceInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', '${service.name}')"><span class="hostlist-status ${service.status_class}">${service.status_text}</span></td>
                    <td class="col-lastcheck" onclick="openServiceInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', '${service.name}')">${formatDate(service.last_check)}</td>
                    <td class="col-duration" onclick="openServiceInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', '${service.name}')">${service.duration}</td>
                    <td class="col-attempt" onclick="openServiceInModal('${host.display_name}', '${host.address}', '${host.subnet || 'External'}', '${service.name}')">${service.attempt}</td>
                    <td class="col-info">${service.plugin_output}</td>
                </tr>`;
        });
    });

    tableHTML += `</tbody></table>`;
    return tableHTML;
}

// Main renderer used by pagination and search
function renderHostList() {
    const contentArea = document.getElementById('hostlist-content');
    if (!contentArea) return;

    if (!window.filteredHostListData || window.filteredHostListData.length === 0) {
        contentArea.innerHTML = '<div class="hostlist-empty">No hosts found</div>';
        renderPaginationControls(0);
        return;
    }

    const totalPages = Math.ceil(window.filteredHostListData.length / HOSTS_PER_PAGE);
    if (currentPage > totalPages) currentPage = totalPages;

    const startIdx = (currentPage - 1) * HOSTS_PER_PAGE;
    const pageSlice = window.filteredHostListData.slice(startIdx, startIdx + HOSTS_PER_PAGE);

    const tableHTML = generateTableHTML(pageSlice);
    contentArea.innerHTML = tableHTML;

    // Re-populate status icons for currently rendered slice
    populateHostListStatusIcons(pageSlice);

    // (Re)draw pagination controls
    renderPaginationControls(totalPages);
}

// Expose renderer for other modules (e.g., search)
window.renderHostList = renderHostList; 