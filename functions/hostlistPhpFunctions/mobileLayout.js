/**
 * Mobile Layout functionality for Hostlist view
 * Handles responsive layout adjustments for mobile devices
 */

/**
 * Setup mobile header layout - moves status filters and indicators below header on mobile
 */
function setupMobileHeaderLayout() {
    // Create mobile status container that will be placed below the header
    const mobileStatusContainer = document.createElement('div');
    mobileStatusContainer.id = 'mobile-status-container';
    mobileStatusContainer.className = 'mobile-status-container';
    
    // Check if we're on mobile
    let isMobile = window.innerWidth <= 768;
    
    // Function to handle the layout change
    function updateLayout() {
        const header = document.querySelector('header');
        const headerFirstDiv = header.querySelector('div:first-child');
        const filtersContainer = document.querySelector('.hostlist-status-filters-header');
        const indicatorsContainer = document.querySelector('.hostlist-status-indicators');
        const refreshCountdown = document.getElementById('refresh-countdown');
        
        if (isMobile) {
            // If mobile status container doesn't exist in DOM yet, add it after header
            if (!document.getElementById('mobile-status-container')) {
                document.body.insertBefore(mobileStatusContainer, document.querySelector('.hostlist-search-container'));
            }
            
            // Move indicators to mobile container first (so they appear above filters)
            if (indicatorsContainer && indicatorsContainer.parentElement !== mobileStatusContainer) {
                mobileStatusContainer.appendChild(indicatorsContainer);
            }
            
            // Move filters to mobile container if they're not already there
            if (filtersContainer && filtersContainer.parentElement !== mobileStatusContainer) {
                mobileStatusContainer.appendChild(filtersContainer);
            }
            
            // Keep refresh countdown next to breadcrumbs, don't move it
            
            // Make sure mobile container is visible
            mobileStatusContainer.style.display = 'flex';
        } else {
                                
            if (indicatorsContainer && indicatorsContainer.parentElement === mobileStatusContainer) {
                headerFirstDiv.appendChild(indicatorsContainer);
            }
            
            // On desktop, move everything back to header if needed
            if (filtersContainer && filtersContainer.parentElement === mobileStatusContainer) {
                headerFirstDiv.appendChild(filtersContainer);
            }
            
            // Hide mobile container
            mobileStatusContainer.style.display = 'none';
        }
    }
    
    // Initial layout setup
    updateLayout();
    
    // Update layout on resize
    window.addEventListener('resize', function() {
        const newIsMobile = window.innerWidth <= 768;
        
        // Only update if mobile state changed
        if (isMobile !== newIsMobile) {
            isMobile = newIsMobile;
            updateLayout();
        }
    });
} 