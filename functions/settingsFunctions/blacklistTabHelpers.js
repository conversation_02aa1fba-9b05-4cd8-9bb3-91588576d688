/**
 * Blacklist Tab Management Functions
 */
$(document).ready(function() {
    // Cache DOM elements
    const $blacklistTabLinks = $('.blacklist-tab-link');
    const $blacklistTabContents = $('.blacklist-tab-content');
    
    // Initialize blacklist tabs
    $blacklistTabLinks.on('click', function(e) {
        e.preventDefault();
        const target = $(this).data('target');
        
        // Activate clicked tab
        $blacklistTabLinks.removeClass('active');
        $(this).addClass('active');
        
        // Show target content
        $blacklistTabContents.hide();
        $('#' + target).show();
        
        // Load content based on the tab
        if (target === 'service-exclusions-tab') {
            // Load muted hosts when service exclusions tab is clicked
            setTimeout(function() {
                if ($('#muted-table-container').length) {
                    // Call the refreshMutedHosts function from blacklistManager.js
                    if (typeof refreshMutedHosts === 'function') {
                        refreshMutedHosts();
                    }
                }
            }, 200);
        }
    });
    
    // Initialize when the blacklist sub-tab is clicked
    $('.sub-tab-link[data-target="detection-blacklist"]').on('click', function() {
        // Ensure the first blacklist tab is active by default
        if (!$blacklistTabLinks.hasClass('active')) {
            $blacklistTabLinks.first().addClass('active');
            $('#blacklisted-hosts-tab').show();
            $('#service-exclusions-tab').hide();
        }
    });
}); 