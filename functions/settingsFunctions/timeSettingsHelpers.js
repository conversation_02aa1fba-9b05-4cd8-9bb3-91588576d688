/**
 * Time Settings Management for Settings Modal
 * Works with the original settings-time.php and settings-time-proc.php
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize time settings when the tab is clicked
    const timeTabLink = document.querySelector('.sub-tab-link[data-target="general-config-time"]');
    if (timeTabLink) {
        timeTabLink.addEventListener('click', initTimeSettings);
    }
    
    // Add CSS for button animations and status messages
    addTimeSettingsStyles();
});

/**
 * Add CSS styles for animations
 */
function addTimeSettingsStyles() {
    // Only add styles once
    if (document.getElementById('time-settings-styles')) {
        return;
    }
    
    const style = document.createElement('style');
    style.id = 'time-settings-styles';
    style.textContent = `
        /* Button animations */
        @keyframes button-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        @keyframes button-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .btn-pulse {
            animation: button-pulse 0.6s ease;
        }
        
        .btn-icon-spin .fa {
            animation: button-spin 0.8s linear infinite;
        }
        
        .btn-processing {
            opacity: 0.8;
            pointer-events: none;
        }
        
        /* Status message animations */
        #time-settings-status {
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        #time-settings-status.slide-in {
            max-height: 100px;
            opacity: 1;
            margin-top: 20px;
        }
        
        #time-settings-status.slide-out {
            max-height: 0;
            opacity: 0;
            margin-top: 0;
        }
    `;
    
    document.head.appendChild(style);
}

/**
 * Initialize time settings interface
 */
function initTimeSettings() {
    // Only initialize if not already done
    if (document.getElementById('time-settings-initialized')) {
        return;
    }
    
    // Mark as initialized
    const initialized = document.createElement('input');
    initialized.type = 'hidden';
    initialized.id = 'time-settings-initialized';
    document.getElementById('time-settings-form').appendChild(initialized);
    
    // Populate date/time dropdowns
    populateDateTimeDropdowns();
    
    // Setup event handlers
    setupTimeFormHandlers();
    
    // Load current settings
    loadTimeSettings();
    
    // Update current time display
    updateCurrentTimeDisplay();
    setInterval(updateCurrentTimeDisplay, 1000);
}

/**
 * Populate all date and time dropdown options
 */
function populateDateTimeDropdowns() {
    // Years (current year and 10 years into future)
    const yearSelect = document.getElementById('year');
    const currentYear = new Date().getFullYear();
    for (let i = currentYear; i <= currentYear + 10; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i;
        yearSelect.appendChild(option);
    }
    
    // Months (01-12)
    const monthSelect = document.getElementById('month');
    const monthNames = [
        'January', 'February', 'March', 'April', 
        'May', 'June', 'July', 'August',
        'September', 'October', 'November', 'December'
    ];
    
    for (let i = 1; i <= 12; i++) {
        const option = document.createElement('option');
        option.value = i.toString().padStart(2, '0');
        option.textContent = `${option.value} - ${monthNames[i-1]}`;
        monthSelect.appendChild(option);
    }
    
    // Days (01-31)
    const daySelect = document.getElementById('day');
    for (let i = 1; i <= 31; i++) {
        const option = document.createElement('option');
        option.value = i.toString().padStart(2, '0');
        option.textContent = option.value;
        daySelect.appendChild(option);
    }
    
    // Hours (00-23)
    const hourSelect = document.getElementById('hour');
    for (let i = 0; i <= 23; i++) {
        const option = document.createElement('option');
        option.value = i.toString().padStart(2, '0');
        option.textContent = option.value;
        hourSelect.appendChild(option);
    }
    
    // Minutes (00-59)
    const minSelect = document.getElementById('min');
    for (let i = 0; i <= 59; i++) {
        const option = document.createElement('option');
        option.value = i.toString().padStart(2, '0');
        option.textContent = option.value;
        minSelect.appendChild(option);
    }
}

/**
 * Setup event handlers for time settings form
 */
function setupTimeFormHandlers() {
    // Radio button change handler
    const radioButtons = document.querySelectorAll('input[name="time_method"]');
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            toggleTimeMethodDisplay(this.value);
        });
    });
    
    // Sync button handler
    document.getElementById('sync-time-now').addEventListener('click', function(e) {
        e.preventDefault();
        
        // Add animation to button
        animateButton(this, 'sync');
        
        // Perform the sync
        syncTimeNow();
    });
    
    // Form submission
    document.getElementById('time-settings-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Add animation to save button
        animateButton(document.getElementById('save-time-settings'), 'save');
        
        // Submit the form
        submitTimeSettings();
    });
}

/**
 * Animate a button during processing
 */
function animateButton(button, type) {
    // Remove any existing animation classes
    button.classList.remove('btn-pulse', 'btn-icon-spin', 'btn-processing');
    
    // Apply processing state and animation
    button.classList.add('btn-processing');
    
    // Apply appropriate animation based on button type
    if (type === 'sync') {
        // For sync button, spin the icon
        button.classList.add('btn-icon-spin');
        
        // Store original icon class
        const iconEl = button.querySelector('i');
        const originalIcon = iconEl.className;
        
        // Change to spinner icon
        iconEl.className = 'fa fa-refresh';
        
        // Set timeout to restore original state
        setTimeout(() => {
            button.classList.remove('btn-processing', 'btn-icon-spin');
            iconEl.className = originalIcon;
            
            // Add pulse animation after processing
            button.classList.add('btn-pulse');
            
            // Remove pulse animation after it completes
            setTimeout(() => {
                button.classList.remove('btn-pulse');
            }, 600);
        }, 2000);
    } else {
        // For save button, pulse effect
        const iconEl = button.querySelector('i');
        const originalIcon = iconEl.className;
        
        // Change to spinner icon
        iconEl.className = 'fa fa-circle-o-notch fa-spin';
        
        // Set timeout to restore original state
        setTimeout(() => {
            button.classList.remove('btn-processing');
            iconEl.className = originalIcon;
            
            // Add pulse animation after processing
            button.classList.add('btn-pulse');
            
            // Remove pulse animation after it completes
            setTimeout(() => {
                button.classList.remove('btn-pulse');
            }, 600);
        }, 1500);
    }
}

/**
 * Toggle display of time method sections based on selection
 */
function toggleTimeMethodDisplay(method) {
    const timeserverSettings = document.getElementById('timeserver-settings');
    const manualSettings = document.getElementById('manual-time-settings');
    const syncButton = document.getElementById('sync-time-now');
    
    if (method === 'auto') {
        timeserverSettings.style.display = 'block';
        manualSettings.style.display = 'none';
        syncButton.disabled = false;
    } else if (method === 'manual') {
        timeserverSettings.style.display = 'none';
        manualSettings.style.display = 'block';
        syncButton.disabled = true;
    }
}

/**
 * Update the current time display
 */
function updateCurrentTimeDisplay() {
    const now = new Date();
    const options = { 
        weekday: 'long',
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    };
    document.getElementById('current-date-time').textContent = now.toLocaleDateString('en-US', options);
}

/**
 * Load current time settings from settings-time.php
 */
function loadTimeSettings() {
    // Show loading state
    showTimeSettingsStatus('loading', 'Loading current time settings...');
    
    // Fetch current settings using Ajax from settings-time.php
    fetch('../../../settings-time.php')
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Extract time method
            let timeMethod = 'auto'; // Default
            
            // Try to extract from radio buttons
            const autoRadio = doc.querySelector('input[name="time_method"][value="auto"]');
            const manualRadio = doc.querySelector('input[name="time_method"][value="manual"]');
            
            if (autoRadio && autoRadio.checked) {
                timeMethod = 'auto';
            } else if (manualRadio && manualRadio.checked) {
                timeMethod = 'manual';
            }
            
            // Set radio button in our form
            document.getElementById('time_method_' + timeMethod).checked = true;
            toggleTimeMethodDisplay(timeMethod);
            
            // Extract timeserver values
            const ts1Input = doc.querySelector('input[name="ts1"]');
            const ts2Input = doc.querySelector('input[name="ts2"]');
            const ts3Input = doc.querySelector('input[name="ts3"]');
            const ts4Input = doc.querySelector('input[name="ts4"]');
            
            // Set timeserver values in our form and hidden old fields
            if (ts1Input) {
                const ts1Value = ts1Input.value || '';
                document.getElementById('ts1').value = ts1Value;
                document.getElementById('ts1_old').value = ts1Value;
            }
            
            if (ts2Input) {
                const ts2Value = ts2Input.value || '';
                document.getElementById('ts2').value = ts2Value;
                document.getElementById('ts2_old').value = ts2Value;
            }
            
            if (ts3Input) {
                const ts3Value = ts3Input.value || '';
                document.getElementById('ts3').value = ts3Value;
                document.getElementById('ts3_old').value = ts3Value;
            }
            
            if (ts4Input) {
                const ts4Value = ts4Input.value || '';
                document.getElementById('ts4').value = ts4Value;
                document.getElementById('ts4_old').value = ts4Value;
            }
            
            // Extract current date for setting defaults in manual mode
            const dateText = doc.querySelector('.badge')?.textContent || '';
            setCurrentDateTimeDefaults(dateText);
            
            // Clear status
            hideTimeSettingsStatus();
        })
        .catch(error => {
            console.error('Error loading time settings:', error);
            showTimeSettingsStatus('error', 'Failed to load current time settings. Please try again.');
        });
}

/**
 * Set current date/time as defaults for manual mode
 */
function setCurrentDateTimeDefaults(dateText) {
    // If we got a date string from the page, try to parse it
    // Otherwise use current browser time
    const now = new Date();
    
    // Set values in dropdowns
    document.getElementById('year').value = now.getFullYear().toString();
    document.getElementById('month').value = (now.getMonth() + 1).toString().padStart(2, '0');
    document.getElementById('day').value = now.getDate().toString().padStart(2, '0');
    document.getElementById('hour').value = now.getHours().toString().padStart(2, '0');
    document.getElementById('min').value = now.getMinutes().toString().padStart(2, '0');
}

/**
 * Submit time settings form
 */
function submitTimeSettings() {
    // Show saving status
    showTimeSettingsStatus('loading', 'Saving time settings...');
    
    // Get form data
    const form = document.getElementById('time-settings-form');
    const formData = new FormData(form);
    
    // Send form data to settings-time-proc.php
    fetch('../../../settings-time-proc.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        // Settings successfully saved
        showTimeSettingsStatus('success', 'Time settings successfully saved.');
        
        // Reload settings after a delay to update form with new values
        setTimeout(() => {
            loadTimeSettings();
        }, 2000);
    })
    .catch(error => {
        console.error('Error saving time settings:', error);
        showTimeSettingsStatus('error', 'Failed to save time settings. Please try again.');
    });
}

/**
 * Synchronize time now
 */
function syncTimeNow() {
    // Show syncing status
    showTimeSettingsStatus('loading', 'Synchronizing with time server...');
    
    // Extract the primary timeserver
    const ts1 = document.getElementById('ts1').value;
    
    // Create form data with sync parameters
    const formData = new FormData();
    formData.append('sync', true);
    formData.append('time_method', 'auto');
    formData.append('ts1', ts1);
    formData.append('ts1_old', document.getElementById('ts1_old').value);
    formData.append('ts2_old', document.getElementById('ts2_old').value);
    formData.append('ts3_old', document.getElementById('ts3_old').value);
    formData.append('ts4_old', document.getElementById('ts4_old').value);
    
    // Send sync request
    fetch('../../../settings-time-proc.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        // Synchronization completed
        showTimeSettingsStatus('success', 'Time synchronized successfully.');
        
        // Reload settings after a delay to update form with new values
        setTimeout(() => {
            loadTimeSettings();
        }, 2000);
    })
    .catch(error => {
        console.error('Error synchronizing time:', error);
        showTimeSettingsStatus('error', 'Failed to synchronize time. Please try again.');
    });
}

/**
 * Show status message in time settings
 */
function showTimeSettingsStatus(type, message) {
    // Use the status element inside the form
    const statusElement = document.getElementById('time-settings-status');
    
    if (!statusElement) {
        console.error('Status message element not found');
        return;
    }
    
    // Set the appropriate message class
    statusElement.className = 'message';
    
    if (type === 'loading') {
        statusElement.classList.add('message-info');
        message = `<i class="fa fa-spinner fa-spin"></i> ${message}`;
    } else if (type === 'success') {
        statusElement.classList.add('message-success');
        message = `<i class="fa fa-check-circle"></i> ${message}`;
    } else if (type === 'error') {
        statusElement.classList.add('message-error');
        message = `<i class="fa fa-exclamation-circle"></i> ${message}`;
    }
    
    // Update message content
    statusElement.innerHTML = message;
    
    // Show the element with animation
    statusElement.style.display = 'block';
    
    // Remove any existing animation classes
    statusElement.classList.remove('slide-in', 'slide-out');
    
    // Add the slide-in animation class
    setTimeout(() => {
        statusElement.classList.add('slide-in');
    }, 10);
    
    // For non-loading messages, auto-hide after delay
    if (type !== 'loading') {
        setTimeout(() => {
            hideTimeSettingsStatus();
        }, 4000);
    }
}

/**
 * Hide status message in time settings
 */
function hideTimeSettingsStatus() {
    const statusElement = document.getElementById('time-settings-status');
    if (statusElement) {
        // Add the slide-out animation class
        statusElement.classList.remove('slide-in');
        statusElement.classList.add('slide-out');
        
        // After animation completes, hide the element
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 300);
    }
} 