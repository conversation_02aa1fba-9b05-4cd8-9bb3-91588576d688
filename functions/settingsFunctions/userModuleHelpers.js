/**
 * User Module Management Functions
 */
$(document).ready(function() {
    // Cache DOM elements
    const $userModulesContainer = $('#user-modules-container');
    const $usersLoading = $('#users-loading');
    const $userModulesTableContainer = $('#user-modules-table-container');
    const $userEditFormContainer = $('#user-edit-form-container');
    const $userEditForm = $('#user-edit-form');
    const $moduleCheckboxesContainer = $('#module-checkboxes-container');
    const $editUserId = $('#edit-user-id');
    const $editUsername = $('#edit-username');
    const $editPassword = $('#edit-password');
    const $saveUserBtn = $('#save-user-btn');
    const $cancelEditBtn = $('#cancel-edit-btn');
    const $deleteUserBtn = $('#delete-user-btn');
    const $userEditStatus = $('#user-edit-status');
    const $authTabLinks = $('.auth-tab-link');
    const $authTabContents = $('.auth-tab-content');
    const $addUserBtn = $('#add-user-btn');
    
    // LDAP form elements
    const $ldapForm = $('#ldap-config-form');
    const $saveLdapBtn = $('#save-ldap-btn');
    const $ldapConfigStatus = $('#ldap-config-status');
    const $ldapServer = $('#ldap_server');
    const $ldapBaseDN = $('#ldap_basedn');
    const $ldapRootDN = $('#ldap_rootdn');
    const $ldapPassword = $('#ldap_password');
    
    // Define module colors for consistent display
    const moduleStatusColors = {
        assigned: {
            bg: 'rgba(46, 204, 113, 0.1)',
            text: '#2ecc71'
        },
        notAssigned: {
            bg: 'rgba(220, 53, 69, 0.1)',
            text: '#dc3545'
        }
    };
    
    // Module definitions (same as in userModulesHandler.php)
    const moduleNames = {
        1: 'BGA',
        2: 'APM',
        3: 'NDD',
        4: 'NPM',
        5: 'NTA',
        6: 'SPM',
        7: 'ELM',
        8: 'NCM',
        9: 'NSM',
        10: 'ALM'
    };
    
    // Initialize add user button
    $addUserBtn.on('click', function() {
        openAddUserForm();
    });
    
    // Initialize auth tabs
    $authTabLinks.on('click', function(e) {
        e.preventDefault();
        const target = $(this).data('target');
        
        // Activate clicked tab
        $authTabLinks.removeClass('active');
        $(this).addClass('active');
        
        // Show target content
        $authTabContents.hide();
        $('#' + target).show();
        
        // If users tab, load the data
        if (target === 'users-tab') {
            loadUserModules();
        }
    });
    
    // Initialize module table when the auth tab is clicked
    $('.sub-tab-link[data-target="general-config-auth"]').on('click', function() {
        // If the tab is already initialized, just return
        if ($authTabLinks.hasClass('active')) {
            return;
        }
        
        // Activate the first auth tab by default
        $authTabLinks.first().addClass('active');
        $('#auth-settings-tab').show();
        $('#users-tab').hide();
    });
    
    // Initialize when the Users tab is clicked directly from anywhere else
    $(document).on('click', '.auth-tab-link[data-target="users-tab"]', function() {
        loadUserModules();
    });
    
    // Initialize LDAP form handling
    initLdapFormHandling();
    
    // Initialize password toggle for user edit form
    $('#toggle-edit-password').on('click', function() {
        const $passwordField = $('#edit-password');
        const fieldType = $passwordField.attr('type');
        
        // Toggle password visibility
        if (fieldType === 'password') {
            $passwordField.attr('type', 'text');
            $(this).removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            $passwordField.attr('type', 'password');
            $(this).removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Initialize delete user button
    $deleteUserBtn.on('click', function() {
        handleDeleteUser();
    });
    
    /**
     * Load user modules data via AJAX
     */
    function loadUserModules() {
        $usersLoading.show();
        $userModulesTableContainer.hide();
        $userEditFormContainer.hide();
        
        $.ajax({
            url: 'src/authConfig/userModulesHandler.php',
            type: 'GET',
            data: { action: 'getUserModules' },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.users) {
                    renderUsersTable(response.users);
                    $addUserBtn.show(); // Show the add user button
                } else {
                    showError('Failed to load user module data.');
                }
            },
            error: function() {
                showError('Error connecting to the server.');
            },
            complete: function() {
                $usersLoading.hide();
            }
        });
    }
    
    /**
     * Render the users table with module information
     * 
     * @param {Array} users Array of user objects with module data
     */
    function renderUsersTable(users) {
        if (!users || users.length === 0) {
            $userModulesTableContainer.html('<p>No users found in the system.</p>').show();
            return;
        }
        
        // Get all module names for headers
        const moduleIds = Object.keys(users[0].modules).map(Number).sort((a, b) => a - b);
        
        // Create table HTML
        let tableHtml = `
            <div class="user-modules-table-wrapper">
                <table class="service-status user-modules-table">
                    <thead>
                        <tr>
                            <th style="min-width: 150px;">Username</th>
        `;
        
        // Add module columns
        moduleIds.forEach(moduleId => {
            const moduleName = users[0].modules[moduleId].name;
            tableHtml += `<th style="min-width: 50px; text-align: center;">${moduleName}</th>`;
        });
        
        tableHtml += `
                            <th style="min-width: 80px; text-align: center;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        // Add user rows
        users.forEach(user => {
            tableHtml += `
                <tr data-user-id="${user.user_id}" data-username="${user.username}">
                    <td>${user.username}</td>
            `;
            
            // Add module status for each user
            moduleIds.forEach(moduleId => {
                const module = user.modules[moduleId];
                const isAssigned = module.assigned;
                const statusClass = isAssigned ? 'assigned' : 'not-assigned';
                const icon = isAssigned ? 
                    '<i class="fa fa-check-circle"></i>' : 
                    '<i class="fa fa-times-circle"></i>';
                const colors = isAssigned ? moduleStatusColors.assigned : moduleStatusColors.notAssigned;
                
                tableHtml += `
                    <td style="text-align: center;">
                        <span class="module-status-icon ${statusClass}" 
                              style="background-color: ${colors.bg}; color: ${colors.text};" 
                              title="${isAssigned ? 'Assigned' : 'No Access'}">
                            ${icon}
                        </span>
                    </td>
                `;
            });
            
            // Add edit button
            tableHtml += `
                <td style="text-align: center;">
                    <button type="button" class="edit-user-btn btn-restart" style="padding: 8px; font-size: 1em; width: 36px; height: 36px; border-radius: 50%;" 
                            data-user-id="${user.user_id}" data-username="${user.username}" onclick="javascript:void(0);">
                        <i class="fa fa-edit"></i>
                    </button>
                </td>
            `;
            
            tableHtml += `</tr>`;
        });
        
        tableHtml += `
                    </tbody>
                </table>
            </div>
            <div style="margin-top: 15px; font-size: 0.9em; color: var(--text-secondary);">
                <p><i class="fa fa-info-circle"></i> This table shows which modules each user has access to. Click <i class="fa fa-edit"></i> to change password or module access.</p>
            </div>
        `;
        
        $userModulesTableContainer.html(tableHtml).show();
        
        // Attach event handlers to edit buttons using direct binding
        $userModulesTableContainer.find('.edit-user-btn').on('click', function() {
            const userId = $(this).data('user-id');
            const username = $(this).data('username');
            
            // Find the user in our data - convert both to strings to ensure matching types
            const userIdStr = String(userId);
            const user = users.find(u => String(u.user_id) === userIdStr);
            
            if (user) {
                openEditForm(user);
            } else {
                // As a fallback, create a user object with known data
                const fallbackUser = {
                    user_id: userId,
                    username: username,
                    modules: {}
                };
                
                // Add default modules (all unassigned)
                Object.keys(moduleNames).forEach(moduleId => {
                    fallbackUser.modules[moduleId] = {
                        id: parseInt(moduleId),
                        name: moduleNames[moduleId],
                        assigned: false
                    };
                });
                
                openEditForm(fallbackUser);
            }
        });
    }
    
    /**
     * Open the add user form
     */
    function openAddUserForm() {
        try {
            // Hide table, show form container
            $userModulesTableContainer.hide();
            $addUserBtn.hide(); // Hide the add user button
            
            // Create a new user form
            const formHtml = `
                <div id="user-add-form-container" style="margin-top: 20px;">
                    <h3><i class="fa fa-user-plus"></i> Add New User</h3>
                    <form id="user-add-form" action="../../../settings-auth-user-add-proc.php" method="post">
                        <div class="input-group" style="display: flex; flex-direction: column;">
                            <label for="new-username" style="margin-bottom: 5px;"><i class="fa fa-user"></i> Username:</label>
                            <input type="text" id="new-username" name="username" required>
                        </div>
                        
                        <div class="input-group" style="display: flex; flex-direction: column;">
                            <label for="new-password" style="margin-bottom: 5px;"><i class="fa fa-key"></i> Password:</label>
                            <div style="position: relative;">
                                <input type="password" id="new-password" name="password" required>
                                <span id="toggle-new-password" class="fa fa-eye"></span>
                            </div>
                            <small>Enter a password for this user.</small>
                        </div>
                        
                        <fieldset style="margin-top: 15px;">
                            <legend>Module Access</legend>
                            <div id="new-module-checkboxes-container" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;">
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="BGA" checked> BGA
                                </label>
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="APM" checked> APM
                                </label>
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="NDD" checked> NDD
                                </label>
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="NPM" checked> NPM
                                </label>
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="NTA" checked> NTA
                                </label>
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="SPM" checked> SPM
                                </label>
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="ELM" checked> ELM
                                </label>
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="NCM" checked> NCM
                                </label>
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="NSM" checked> NSM
                                </label>
                                <label class="checkbox-label" style="margin-bottom: 10px;">
                                    <input type="checkbox" name="ALM" checked> ALM
                                </label>
                            </div>
                        </fieldset>
                        
                        <div style="margin-top: 20px;">
                            <button type="submit" class="btn-restart" id="create-user-btn">
                                <i class="fa fa-save"></i>&nbsp;Create User
                            </button>
                            <button type="button" class="btn-stop" id="cancel-add-btn" style="margin-left: 10px;">
                                <i class="fa fa-times"></i>&nbsp;Cancel
                            </button>
                        </div>
                    </form>
                    <div id="user-add-status" class="message" style="margin-top: 15px; display: none;"></div>
                </div>
            `;
            
            $userModulesTableContainer.after(formHtml);
            
            // Setup event handlers for the new form
            $('#cancel-add-btn').on('click', function() {
                $('#user-add-form-container').remove();
                $userModulesTableContainer.show();
                $addUserBtn.show(); // Show the add user button again
            });
            
            // Add password toggle functionality
            $('#toggle-new-password').on('click', function() {
                const $passwordField = $('#new-password');
                const fieldType = $passwordField.attr('type');
                
                // Toggle password visibility
                if (fieldType === 'password') {
                    $passwordField.attr('type', 'text');
                    $(this).removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    $passwordField.attr('type', 'password');
                    $(this).removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });
            
            // Handle form submission
            $('#user-add-form').on('submit', function(e) {
                e.preventDefault();
                
                const $form = $(this);
                const $submitBtn = $('#create-user-btn');
                const $statusDiv = $('#user-add-status');
                
                // Basic validation
                const username = $('#new-username').val().trim();
                const password = $('#new-password').val().trim();
                
                if (!username) {
                    showAddFormError('Username is required');
                    return;
                }
                
                if (!password) {
                    showAddFormError('Password is required');
                    return;
                }
                
                // Disable form during submission
                $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Creating...');
                
                // Submit form data to the processor
                $.ajax({
                    url: '../../../settings-auth-user-add-proc.php',
                    type: 'POST',
                    data: $form.serialize(),
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function(response, textStatus, xhr) {
                        // Check for error messages in the response
                        if (response.includes("The user already exists.") || 
                            response.includes("user_exists") ||
                            response.includes("bereits existiert") ||
                            response.includes("already exists")) {
                            showAddFormError('User already exists');
                            $submitBtn.prop('disabled', false).html('<i class="fa fa-save"></i>&nbsp;Create User');
                            return;
                        }
                        
                        // HTTP 200 should be treated as success if no error message detected
                        if (xhr.status === 200) {
                            showAddFormSuccess('User created successfully');
                            // Reload user list after short delay
                            setTimeout(function() {
                                $('#user-add-form-container').remove();
                                loadUserModules();
                            }, 2000);
                            return;
                        }
                        
                        // Check for success in redirect URL or response content
                        if (xhr.getResponseHeader('Location') && 
                            xhr.getResponseHeader('Location').indexOf('status=success') > -1) {
                            showAddFormSuccess('User created successfully');
                            // Reload user list after short delay
                            setTimeout(function() {
                                $('#user-add-form-container').remove();
                                loadUserModules();
                            }, 2000);
                        } else if (response.indexOf('status=success') > -1) {
                            showAddFormSuccess('User created successfully');
                            // Reload user list after short delay
                            setTimeout(function() {
                                $('#user-add-form-container').remove();
                                loadUserModules();
                            }, 2000);
                        } else {
                            // Try to extract error message
                            let errorMsg = 'Failed to create user';
                            const errorMatch = response.match(/alert-danger[^>]*>(.*?)<\/div>/s);
                            if (errorMatch && errorMatch[1]) {
                                errorMsg = $(errorMatch[1]).text() || errorMsg;
                            }
                            showAddFormError(errorMsg);
                            $submitBtn.prop('disabled', false).html('<i class="fa fa-save"></i>&nbsp;Create User');
                        }
                    },
                    error: function(xhr, status, error) {
                        // Since redirects can end up here, check if it was actually a successful redirect
                        if (xhr.status === 302 || status === 'parsererror') {
                            // This could still be a successful creation that resulted in a redirect
                            showAddFormSuccess('User created successfully');
                            // Reload user list after short delay
                            setTimeout(function() {
                                $('#user-add-form-container').remove();
                                loadUserModules();
                            }, 2000);
                        } else {
                            showAddFormError('Server error occurred. Please try again.');
                            $submitBtn.prop('disabled', false).html('<i class="fa fa-save"></i>&nbsp;Create User');
                        }
                    }
                });
            });
            
        } catch (error) {
            showError('Error opening add user form: ' + error.message);
        }
    }
    
    /**
     * Show form success message for add user form
     * 
     * @param {string} message Success message
     */
    function showAddFormSuccess(message) {
        $('#user-add-status').hide().removeClass('message-success message-error message-info')
            .addClass('message-success')
            .html(`<i class="fa fa-check-circle"></i>&nbsp;${message}`)
            .fadeIn();
    }
    
    /**
     * Show form error message for add user form
     * 
     * @param {string} message Error message
     */
    function showAddFormError(message) {
        $('#user-add-status').hide().removeClass('message-success message-error message-info')
            .addClass('message-error')
            .html(`<i class="fa fa-exclamation-triangle"></i>&nbsp;${message}`)
            .fadeIn();
    }
    
    /**
     * Open the edit form for a user
     * 
     * @param {Object} user User object with module data
     */
    function openEditForm(user) {
        try {
            // Set form values
            $editUserId.val(user.user_id);
            $editUsername.val(user.username);
            $editPassword.val(user.password || '');
            
            // Generate module checkboxes
            generateModuleCheckboxes(user.modules);
            
            // Show form, hide table
            $userModulesTableContainer.hide();
            $userEditFormContainer.show();
            $userEditStatus.hide();
            $addUserBtn.hide(); // Hide the add user button

            // Show/hide delete button based on username
            if (user.username === 'admin') {
                $deleteUserBtn.hide();
            } else {
                $deleteUserBtn.show();
            }
            
            // Ensure buttons are enabled initially
            toggleFormLoading(false);
            
        } catch (error) {
            showError('Error opening edit form: ' + error.message);
        }
    }
    
    /**
     * Generate module checkboxes for the edit form
     * 
     * @param {Object} modules Module assignment data
     */
    function generateModuleCheckboxes(modules) {
        $moduleCheckboxesContainer.empty();
        
        Object.keys(modules).forEach(moduleId => {
            const module = modules[moduleId];
            const isAssigned = module.assigned;
            
            const checkboxHtml = `
                <label class="checkbox-label" style="margin-bottom: 10px;">
                    <input type="checkbox" name="${module.name}" id="module-${moduleId}" 
                           ${isAssigned ? 'checked' : ''}>
                    ${module.name}
                </label>
            `;
            
            $moduleCheckboxesContainer.append(checkboxHtml);
        });
    }
    
    /**
     * Handle form submission
     */
    $userEditForm.on('submit', function(e) {
        e.preventDefault();
        $userEditStatus.hide();
        
        // Validate form
        if (!$editPassword.val().trim()) {
            showFormError('Password is required');
            return;
        }
        
        // Disable form and show loading
        toggleFormLoading(true);
        
        // Create form data object for better control
        const formData = new FormData(this);
        
        // Make sure checkbox states are properly included
        // If not checked, they won't be included in the form data, so we need to handle this explicitly
        Object.keys(moduleNames).forEach(moduleId => {
            const moduleName = moduleNames[moduleId];
            const checkbox = document.getElementById(`module-${moduleId}`);
            
            // If the checkbox is checked, make sure it's included in the form data
            if (checkbox && checkbox.checked) {
                formData.append(moduleName, 'on');
            }
        });
        
        // Convert FormData to object for jQuery AJAX
        const formObject = {};
        formData.forEach((value, key) => {
            formObject[key] = value;
        });
        
        // Send AJAX request
        $.ajax({
            url: 'src/authConfig/userModulesHandler.php',
            type: 'POST',
            data: formObject,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showFormSuccess(response.message || 'User updated successfully');
                    // Reload user data after a short delay
                    setTimeout(loadUserModules, 2000);
                } else {
                    showFormError(response.message || 'Failed to update user');
                }
            },
            error: function(xhr, status, error) {
                showFormError('Server error occurred. Please try again.');
            },
            complete: function() {
                toggleFormLoading(false);
            }
        });
    });
    
    /**
     * Cancel edit and return to user list
     */
    $cancelEditBtn.on('click', function() {
        $userEditFormContainer.hide();
        $userModulesTableContainer.show();
        $addUserBtn.show(); // Show the add user button again
        $deleteUserBtn.hide(); // Hide delete button when cancelling
    });
    
    /**
     * Toggle form loading state
     * 
     * @param {boolean} isLoading Whether the form is in loading state
     */
    function toggleFormLoading(isLoading) {
        if (isLoading) {
            $saveUserBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Saving...');
            $cancelEditBtn.prop('disabled', true);
            $deleteUserBtn.prop('disabled', true); // Disable delete button during save
        } else {
            $saveUserBtn.prop('disabled', false).html('<i class="fa fa-save"></i>&nbsp;Save User');
            $cancelEditBtn.prop('disabled', false);
            // Only enable delete button if user is not admin
            if ($editUsername.val() !== 'admin') {
                $deleteUserBtn.prop('disabled', false);
            }
        }
    }
    
    /**
     * Show form success message
     * 
     * @param {string} message Success message
     */
    function showFormSuccess(message) {
        $userEditStatus.hide().removeClass('message-success message-error message-info')
            .addClass('message-success')
            .html(`<i class="fa fa-check-circle"></i>&nbsp;${message}`)
            .fadeIn();
    }
    
    /**
     * Show form error message
     * 
     * @param {string} message Error message
     */
    function showFormError(message) {
        $userEditStatus.hide().removeClass('message-success message-error message-info')
            .addClass('message-error')
            .html(`<i class="fa fa-exclamation-triangle"></i>&nbsp;${message}`)
            .fadeIn();
    }
    
    /**
     * Show general error message
     * 
     * @param {string} message Error message to display
     */
    function showError(message) {
        $userModulesTableContainer.html(`
            <div class="message message-error">
                <i class="fa fa-exclamation-triangle"></i>&nbsp;${message}
            </div>
        `).show();
    }
    
    /**
     * Initialize LDAP form handling
     */
    function initLdapFormHandling() {
        // Load LDAP settings when the LDAP tab is clicked
        $authTabLinks.filter('[data-target="ldap-tab"]').on('click', function() {
            // Only load settings if the form wrapper is visible (i.e., LDAP is enabled)
            if ($('#ldap-form-wrapper').is(':visible')) {
                loadLdapSettings();
            }
        });
        
        // Handle save button click
        $saveLdapBtn.on('click', function() {
            saveLdapSettings();
        });
        
        // Initialize password toggle
        $('#toggle-ldap-password').on('click', function() {
            const $passwordField = $('#ldap_password');
            const fieldType = $passwordField.attr('type');
            
            // Toggle password visibility
            if (fieldType === 'password') {
                $passwordField.attr('type', 'text');
                $(this).removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                $passwordField.attr('type', 'password');
                $(this).removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });

        // === LDAP Browse Panel Support ===
        initLdapBrowsePanel();
    }
    
    /**
     * Load current LDAP settings from the server
     */
    function loadLdapSettings() {
        // Show loading status
        $ldapConfigStatus.hide().removeClass('message-success message-error message-info')
            .addClass('message-info')
            .html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Loading LDAP settings...')
            .show();
        
        $.ajax({
            url: 'src/authConfig/ldapSettingsHandler.php',
            type: 'GET',
            data: { action: 'getLdapSettings' },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Populate form fields with current settings
                    $ldapServer.val(response.settings.server || '');
                    $ldapBaseDN.val(response.settings.basedn || '');
                    $ldapRootDN.val(response.settings.rootdn || '');
                    $ldapPassword.val(response.settings.password || '');
                    $ldapConfigStatus.hide();
                } else {
                    $ldapConfigStatus.hide().removeClass('message-success message-error message-info')
                        .addClass('message-error')
                        .html('<i class="fa fa-exclamation-triangle"></i>&nbsp;' + (response.message || 'Error loading LDAP settings'));
                    console.error('Error loading LDAP settings:', response.message);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $ldapConfigStatus.hide().removeClass('message-success message-error message-info')
                    .addClass('message-error')
                    .html('<i class="fa fa-exclamation-triangle"></i>&nbsp;Failed to load LDAP settings');
                console.error('Error loading LDAP settings:', textStatus, errorThrown);
            }
        });
    }
    
    /**
     * Save LDAP settings to the server
     */
    function saveLdapSettings() {
        // Show loading state
        $saveLdapBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Saving...');
        $ldapConfigStatus.hide();
        
        // Validate form
        const server = $ldapServer.val().trim();
        const basedn = $ldapBaseDN.val().trim();
        const rootdn = $ldapRootDN.val().trim();
        const password = $ldapPassword.val();
        
        if (!server || !basedn || !rootdn || !password) {
            $ldapConfigStatus.hide().removeClass('message-success message-error message-info')
                .addClass('message-error')
                .html('<i class="fa fa-exclamation-triangle"></i>&nbsp;All fields are required!')
                .show();
            $saveLdapBtn.prop('disabled', false).html('<i class="fa fa-save"></i>&nbsp;Save LDAP Settings');
            return;
        }
        
        // Create an iframe to handle the form submission and redirect
        const formData = new FormData();
        formData.append('ldap_server', server);
        formData.append('ldap_basedn', basedn);
        formData.append('ldap_rootdn', rootdn);
        formData.append('ldap_password', password);
        
        // Create a hidden iframe for form submission
        const iframeId = 'ldap-submit-frame';
        
        // Remove any existing iframe with the same ID
        $('#' + iframeId).remove();
        
        // Create and append the iframe
        const $iframe = $('<iframe>', {
            id: iframeId,
            name: iframeId,
            style: 'display:none'
        }).appendTo('body');
        
        // Create the form
        const $form = $('<form>', {
            action: '../../../settings-auth-ldap-proc.php',
            method: 'post',
            target: iframeId
        }).appendTo('body');
        
        // Add form fields
        $.each({
            'ldap_server': server,
            'ldap_basedn': basedn,
            'ldap_rootdn': rootdn,
            'ldap_password': password
        }, function(name, value) {
            $('<input>').attr({
                type: 'hidden',
                name: name,
                value: value
            }).appendTo($form);
        });
        
        // Set up event handler for iframe load
        $iframe.on('load', function() {
            try {
                const content = $iframe.contents().find('body').html();
                
                // Check the URL of the loaded page in the iframe
                const iframeUrl = $iframe.contents().get(0).location.href;
                
                if (iframeUrl.includes('status=success')) {
                    $ldapConfigStatus.hide().removeClass('message-success message-error message-info')
                        .addClass('message-success')
                        .html('<i class="fa fa-check-circle"></i>&nbsp;LDAP settings saved successfully!')
                        .show();
                } else if (iframeUrl.includes('status=error')) {
                    let errorMsg = 'Error saving LDAP settings';
                    // Try to extract more specific error message if possible
                    const $errorContent = $iframe.contents().find('.alert-danger');
                    if ($errorContent.length > 0) {
                        errorMsg = $errorContent.text().trim();
                        // Clean up the error message by removing button text
                        errorMsg = errorMsg.replace(/×/g, '').trim();
                    }
                    
                    $ldapConfigStatus.hide().removeClass('message-success message-error message-info')
                        .addClass('message-error')
                        .html('<i class="fa fa-exclamation-triangle"></i>&nbsp;' + errorMsg)
                        .show();
                }
            } catch (e) {
                // Access denied error can happen due to same-origin policy if redirected to another domain
                // In this case, we'll assume success since we can't inspect the content
                $ldapConfigStatus.hide().removeClass('message-success message-error message-info')
                    .addClass('message-success')
                    .html('<i class="fa fa-check-circle"></i>&nbsp;LDAP settings submitted successfully!')
                    .show();
            }
            
            // Clean up
            setTimeout(function() {
                $form.remove();
                $iframe.remove();
            }, 100);
            
            $saveLdapBtn.prop('disabled', false).html('<i class="fa fa-save"></i>&nbsp;Save LDAP Settings');
        });
        
        // Submit the form
        $form.submit();
    }

    /**
     * Handle Delete User button click
     */
    function handleDeleteUser() {
        const userId = $editUserId.val();
        const username = $editUsername.val();
        
        if (!userId || !username) {
            showFormError('Could not get user information for deletion.');
            return;
        }
        
        if (username === 'admin') {
            showFormError('The admin user cannot be deleted.');
            return;
        }
        
        // Confirmation dialog
        if (!confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
            return;
        }
        
        // Disable buttons and show status
        $saveUserBtn.prop('disabled', true);
        $cancelEditBtn.prop('disabled', true);
        $deleteUserBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Deleting...');
        $userEditStatus.hide().removeClass('message-success message-error message-info')
            .addClass('message-info')
            .html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Deleting user...')
            .show();
            
        // Send AJAX GET request to the delete script
        $.ajax({
            url: '../../settings-auth-user-delete-proc.php?user_id=' + userId,
            type: 'GET',
            success: function(response, textStatus, xhr) {
                // Because the PHP script uses header redirects, a successful AJAX call might still end up
                // here or in the error block depending on browser/server behavior.
                // We'll assume success if the request completes without a definite error status.
                // A 302 redirect to the success page is the expected outcome.
                handleDeleteSuccess(username);
            },
            error: function(xhr, status, error) {
                // Check if it's a redirect that error handler caught (can happen)
                if (xhr.status === 302) {
                    handleDeleteSuccess(username);
                } else {
                    console.error('Delete request failed:', status, error, xhr.responseText);
                    handleDeleteError(username, xhr.responseText || 'Server error occurred. Check logs.');
                }
            }
        });
    }
    
    /**
     * Handle successful user deletion
     * @param {string} username 
     */
    function handleDeleteSuccess(username) {
        showFormSuccess(`User "${username}" deleted successfully.`);
        
        // Reset delete button state
        $deleteUserBtn.prop('disabled', false).html('<i class="fa fa-trash"></i>&nbsp;Delete User');
        
        // Wait a moment, then return to the user list
        setTimeout(function() {
            $userEditFormContainer.hide();
            $userModulesTableContainer.show();
            $addUserBtn.show(); 
            $deleteUserBtn.hide(); // Ensure delete button is hidden
            loadUserModules(); // Reload the user list
        }, 2000); 
    }

    /**
     * Handle failed user deletion
     * @param {string} username 
     * @param {string} errorMessage 
     */
    function handleDeleteError(username, errorMessage) {
        showFormError(`Failed to delete user "${username}". ${errorMessage}`);
        // Re-enable buttons on error
        $saveUserBtn.prop('disabled', false);
        $cancelEditBtn.prop('disabled', false);
        // Only re-enable delete if not admin
        if ($editUsername.val() !== 'admin') {
            $deleteUserBtn.prop('disabled', false).html('<i class="fa fa-trash"></i>&nbsp;Delete User');
        } else {
            $deleteUserBtn.hide();
        }
    }

    /**
     * LDAP Browse panel behaviour (select multiple branches)
     */
    function initLdapBrowsePanel() {
        const $browseBtn = $('#browse-ldap-btn');
        const $panel     = $('#ldap-browse-panel');
        const $closeBtn  = $('#close-ldap-browse');
        const $applyBtn  = $('#ldap-browse-apply');
        const $loadBtn   = $('#ldap-browse-load');
        const $tree      = $('#ldap-browse-tree');
        const $baseInput = $('#ldap-browse-basedn');

        // Fetch and render tree starting from base DN
        function fetchTree(startDn) {
            const server   = $ldapServer.val().trim();
            const rootdn   = $ldapRootDN.val().trim();
            const password = $ldapPassword.val();

            if (!server || !rootdn) {
                alert('Please fill in the Server and Root DN fields first.');
                return;
            }

            $.ajax({
                url: '/ldap_browse.php',
                type: 'POST',
                dataType: 'json',
                data: {
                    server: server,
                    rootdn: rootdn,
                    password: password,
                    basedn: startDn
                },
                success: function(resp) {
                    if (resp.error) {
                        alert(resp.error);
                        return;
                    }
                    renderTree(resp);
                },
                error: function(xhr) {
                    alert('Unable to browse directory: ' + (xhr.responseText || xhr.statusText));
                }
            });
        }

        // Render function similar to legacy implementation
        function renderTree(data) {
            const existingDnsRaw = $ldapBaseDN.val();
            let existing = existingDnsRaw.split(/[\r\n]+/).map(l => l.trim()).filter(Boolean);
            if (existing.length === 1) {
                // Try splitting by spaces (legacy format)
                existing = existingDnsRaw.split(/\s+(?=[A-Za-z]+=)/).map(l => l.trim()).filter(Boolean);
            }
            const selectedSet = new Set(existing);

            function buildNode(node, level) {
                const dn = node.li_attr['data-dn'];
                const label = dn.split(',')[0] || dn;

                const $details = $('<details>').css('margin-left', level * 15 + 'px');
                if (level === 0) {
                    $details.attr('open', true);
                }

                const $summary = $('<summary>');

                // arrow indicator (unicode triangles)
                if (node.children && node.children.length) {
                    const $arrow = $('<span>').text($details.prop('open') ? '▼ ' : '▶ ').css('cursor', 'pointer');
                    $details.on('toggle', function() {
                        $arrow.text($details.prop('open') ? '▼ ' : '▶ ');
                    });
                    $summary.append($arrow);
                } else {
                    $summary.append(document.createTextNode('• '));
                }

                const $cb = $('<input>', { type: 'checkbox', value: dn, style: 'margin-right:5px;' });
                if (selectedSet.has(dn)) {
                    $cb.prop('checked', true);
                }
                $summary.append($cb).append(document.createTextNode(label));
                $details.append($summary);

                if (node.children && node.children.length) {
                    node.children.forEach(child => {
                        $details.append(buildNode(child, level + 1));
                    });
                }
                return $details;
            }

            $tree.empty();
            data.forEach(root => {
                $tree.append(buildNode(root, 0));
            });
        }

        // Browse button
        $browseBtn.on('click', function() {
            const firstDn = ($ldapBaseDN.val().trim().split(/[\r\n]+/)[0]) || '';
            $baseInput.val(firstDn);
            fetchTree(firstDn);
            $panel.show();
        });

        // Close button
        $closeBtn.on('click', function() {
            $panel.hide();
        });

        // Load button
        $loadBtn.on('click', function() {
            const dn = $baseInput.val().trim();
            if (!dn) {
                alert('Please enter a Base DN');
                return;
            }
            fetchTree(dn);
        });

        // Apply button
        $applyBtn.on('click', function() {
            const checked = $tree.find('input[type="checkbox"]:checked').map(function() { return this.value; }).get();
            $ldapBaseDN.val(checked.join('\n'));
            $panel.hide();
        });
    }
}); 