// Authentication Config Form Submission Handler
$(document).ready(function() {
    const $form = $('#auth-config-form');
    const $statusDiv = $('#auth-config-status');
    const $saveButton = $('#save-auth-btn');
    const $authTypeRadios = $form.find('input[name="auth_type"]');
    const $ldapTabContent = $('#ldap-tab');
    const $ldapEnableMessage = $('#ldap-enable-message');
    const $ldapFormWrapper = $('#ldap-form-wrapper');

    // Hide the save button as changes will be saved automatically
    $saveButton.hide();

    // Function to update the view based on selected auth type
    function updateAuthSettingsView() {
        const selectedAuthType = $form.find('input[name="auth_type"]:checked').val();
        
        if (selectedAuthType === 'local') {
            $ldapEnableMessage.show();
            $ldapFormWrapper.hide();
        } else { // ldap is selected
            $ldapEnableMessage.hide();
            $ldapFormWrapper.show();
            // Check if LDAP tab is currently active, if so, trigger loading settings
            // Note: loadLdapSettings is defined in userModuleHelpers.js
            // We rely on the click handler there to load settings when the tab is switched to.
        }
    }

    function saveAuthSettings() {
        $statusDiv.hide().removeClass('message-success message-error message-info');
        $saveButton.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Saving...');

        // Get the selected auth type
        const authType = $form.find('input[name="auth_type"]:checked').val();
        
        if (!authType) {
            $statusDiv.hide().removeClass('message-success message-error message-info')
                      .addClass('message-error')
                      .html('<i class="fa fa-exclamation-triangle"></i>&nbsp;Error: Please select an authentication method.')
                      .fadeIn();
            $saveButton.prop('disabled', false).html('<i class="fa fa-save"></i>&nbsp;Save Authentication Settings');
            return;
        }

        $.ajax({
            url: 'src/authConfig/authConfigHandler.php',
            type: 'POST',
            data: { auth_type: authType },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $statusDiv.hide().removeClass('message-success message-error message-info')
                              .addClass('message-success')
                              .html('<i class="fa fa-check-circle"></i>&nbsp;' + response.message)
                              .fadeIn();
                    // Ensure LDAP visibility is correct after saving
                    updateAuthSettingsView(); 
                } else {
                    $statusDiv.hide().removeClass('message-success message-error message-info')
                              .addClass('message-error')
                              .html('<i class="fa fa-exclamation-triangle"></i>&nbsp;Error: ' + 
                                   (response.message || 'Unknown error occurred.'))
                              .fadeIn();
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $statusDiv.hide().removeClass('message-success message-error message-info')
                          .addClass('message-error')
                          .html('<i class="fa fa-times-circle"></i>&nbsp;Request failed: ' + textStatus)
                          .fadeIn();
                console.error('Auth settings update error:', errorThrown);
            },
            complete: function() {
                $saveButton.prop('disabled', false).html('<i class="fa fa-save"></i>&nbsp;Save Authentication Settings');
                // Don't auto-hide this message as it's important for the user to see
            }
        });
    }

    // Add change listener for auth type radio buttons
    $authTypeRadios.on('change', function() {
        updateAuthSettingsView();
        saveAuthSettings(); // Save settings immediately on change
    });

    // Add click handler for the inline link in the LDAP enable message
    $ldapEnableMessage.on('click', '.auth-tab-link-inline', function(e) {
        e.preventDefault();
        const targetTab = $(this).data('target');
        $('.auth-tab-link[data-target="' + targetTab + '"]').trigger('click');
    });

    // Fetch current settings when tab is activated
    $('.sub-tab-link[data-target="general-config-auth"]').on('click', function() {
        $.ajax({
            url: 'src/authConfig/authConfigHandler.php',
            type: 'GET',
            data: { action: 'getAuthType' },
            dataType: 'json',
            success: function(response) {
                if (response.auth_type) {
                    // Update radio button selection
                    $form.find(`input[name="auth_type"][value="${response.auth_type}"]`).prop('checked', true);
                    // Set initial visibility based on fetched type
                    updateAuthSettingsView(); 
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Error fetching auth settings:', errorThrown);
            }
        });
    });
}); 