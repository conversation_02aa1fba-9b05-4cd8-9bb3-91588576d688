document.addEventListener('DOMContentLoaded', initializeServiceActions);

function initializeServiceActions() {
    // Initialize all action forms
    document.querySelectorAll('.module-action-form, .service-action-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            if (this.classList.contains('module-action-form')) {
                handleModuleAction(this);
            } else {
                handleServiceAction(this);
            }
        });
    });
    
    // Initialize module status indicators for all modules
    document.querySelectorAll('.module-section').forEach(moduleSection => {
        updateModuleStatusIndicator(moduleSection);
    });
}

async function handleModuleAction(form) {
    const formData = new FormData(form);
    const action = formData.get('action');
    const moduleName = formData.get('module');
    const submitButton = form.querySelector('button[type="submit"]');
    const moduleSection = form.closest('.module-section');
    const messageContainer = findOrCreateMessageContainer();
    let originalButtonHTML = ''; // Store original button content

    // UI Feedback - Disable and show loading
    if (submitButton) {
        originalButtonHTML = submitButton.innerHTML; // Store original HTML before changing
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Processing...'; // Generic loading text
    }

    try {
        // Execute module action
        const response = await fetch(`src/settingsphp/systemServices.php?action=${encodeURIComponent(action)}&module=${encodeURIComponent(moduleName)}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        // Check if response is JSON before parsing
        const contentType = response.headers.get("content-type");
        if (!response.ok || !contentType || !contentType.includes("application/json")) {
             const errorText = await response.text();
             // Try to extract a meaningful part of the error if it's HTML
             let detail = errorText.substring(0, 200);
             const bodyMatch = errorText.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
             if (bodyMatch && bodyMatch[1].trim()) {
                 detail = bodyMatch[1].trim().substring(0, 200) + '...';
             }
             throw new Error(`Server error ${response.status}: Expected JSON, got ${contentType || 'unknown'}. Response: ${detail}`);
        }
        const data = await response.json();

        // Show action results
        showActionMessage(messageContainer, data.success, data.message, data.details);

        // Wait briefly then refresh status
        await new Promise(resolve => setTimeout(resolve, 500));
        await refreshModuleStatus(moduleSection);

    } catch (error) {
        console.error('Module action failed:', error);
        showErrorMessage(messageContainer, `Failed to ${action.replace('_module', '')} ${moduleName}`, error);
        // Attempt to refresh status even on error to show current state
        if (moduleSection) {
             await refreshModuleStatus(moduleSection);
        }
    } finally {
        // Restore UI
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.innerHTML = originalButtonHTML; // Restore original HTML
        }
    }
}

async function handleServiceAction(form) {
    const formData = new FormData(form);
    const action = formData.get('action');
    const serviceName = formData.get('service');
    const submitButton = form.querySelector('button[type="submit"]');
    const serviceRow = form.closest('tr[data-service]');
    const moduleSection = serviceRow.closest('.module-section');
    const messageContainer = findOrCreateMessageContainer();
    let originalButtonHTML = ''; // Store original button content

    // UI Feedback
    if (submitButton) {
        originalButtonHTML = submitButton.innerHTML; // Store original HTML before changing
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Processing...'; // Generic loading text
    }

    try {
        // Execute service action
        const response = await fetch(`src/settingsphp/systemServices.php?action=${encodeURIComponent(action)}&service=${encodeURIComponent(serviceName)}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

         // Check if response is JSON before parsing
        const contentType = response.headers.get("content-type");
        if (!response.ok || !contentType || !contentType.includes("application/json")) {
             const errorText = await response.text();
             let detail = errorText.substring(0, 200);
             const bodyMatch = errorText.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
             if (bodyMatch && bodyMatch[1].trim()) {
                 detail = bodyMatch[1].trim().substring(0, 200) + '...';
             }
             throw new Error(`Server error ${response.status}: Expected JSON, got ${contentType || 'unknown'}. Response: ${detail}`);
        }
        const data = await response.json();

        // Show action result
        showActionMessage(messageContainer, data.success, data.message);

        // Wait briefly then refresh status
        await new Promise(resolve => setTimeout(resolve, 500));
        await refreshServiceStatus(serviceRow);
        
        // Also update module status after service action
        if (moduleSection) {
            updateModuleStatusIndicator(moduleSection);
        }

    } catch (error) {
        console.error('Service action failed:', error);
        showErrorMessage(messageContainer, `Failed to ${action} ${serviceName}`, error);
         // Attempt to refresh status even on error to show current state
        if (serviceRow) {
             await refreshServiceStatus(serviceRow);
             
             // Also update module status after error
             const moduleSection = serviceRow.closest('.module-section');
             if (moduleSection) {
                 updateModuleStatusIndicator(moduleSection);
             }
        }
    } finally {
        // Restore UI
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.innerHTML = originalButtonHTML; // Restore original HTML
        }
    }
}

async function refreshModuleStatus(moduleSection) {
    if (!moduleSection) return; // Guard against null section
    const serviceRows = moduleSection.querySelectorAll('tbody tr[data-service]');
    // Use Promise.all for potentially faster parallel refreshes within a module
    await Promise.all(Array.from(serviceRows).map(row => refreshServiceStatus(row)));
    
    // Update the module status indicator after refreshing all services
    updateModuleStatusIndicator(moduleSection);
}

function updateModuleStatusIndicator(moduleSection) {
    if (!moduleSection) return;
    
    const serviceRows = moduleSection.querySelectorAll('tbody tr[data-service]');
    const moduleHeader = moduleSection.querySelector('.module-header');
    
    // Remove existing status indicator if present
    const existingIndicator = moduleHeader.querySelector('.module-status-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }
    
    // Calculate overall status
    let runningCount = 0;
    let totalCount = serviceRows.length;
    let hasFailedServices = false;
    
    serviceRows.forEach(row => {
        const statusSpan = row.querySelector('.status-indicator');
        if (statusSpan) {
            if (statusSpan.classList.contains('status-running')) {
                runningCount++;
            } else if (statusSpan.classList.contains('status-failed')) {
                hasFailedServices = true;
            }
        }
    });
    
    // Create the status indicator
    const statusIndicator = document.createElement('span');
    statusIndicator.classList.add('module-status-indicator');
    
    // Set appropriate status class and text
    if (totalCount === 0) {
        statusIndicator.classList.add('status-unknown');
        statusIndicator.textContent = 'No services';
    } else if (hasFailedServices) {
        statusIndicator.classList.add('status-failed');
        statusIndicator.textContent = `${runningCount}/${totalCount} Running (Issues)`;
    } else if (runningCount === 0) {
        statusIndicator.classList.add('status-stopped');
        statusIndicator.textContent = 'All stopped';
    } else if (runningCount === totalCount) {
        statusIndicator.classList.add('status-running');
        statusIndicator.textContent = 'All running';
    } else {
        statusIndicator.classList.add('status-other');
        statusIndicator.textContent = `${runningCount}/${totalCount} Running`;
    }
    
    // Insert the status indicator in the module header
    moduleHeader.querySelector('.module-name').appendChild(statusIndicator);
}

async function refreshServiceStatus(serviceRow) {
    if (!serviceRow) return; // Guard against null row
    const serviceName = serviceRow.dataset.service;
    const statusCell = serviceRow.querySelector('td:nth-child(2)');
    const statusSpan = statusCell?.querySelector('.status-indicator');
    const actionButtons = serviceRow.querySelector('.action-buttons');

    if (!serviceName || !statusSpan || !actionButtons) {
        console.warn('Could not find required elements for status refresh in row:', serviceRow);
        return; // Skip if elements are missing
    }

    // Show loading state only if not already loading
    let wasLoading = statusSpan.querySelector('.fa-spinner');
    if (!wasLoading) {
        statusSpan.dataset.originalContent = statusSpan.innerHTML; // Store current content
        statusSpan.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
    }

    try {
        const response = await fetch(`src/settingsphp/systemServices.php?action=get_status&service=${encodeURIComponent(serviceName)}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        if (!response.ok) {
             const errorText = await response.text();
             throw new Error(`Status check HTTP error ${response.status}: ${errorText.substring(0,100)}`);
        }
        const contentType = response.headers.get("content-type");
         if (!contentType || !contentType.includes("application/json")) {
             const errorText = await response.text();
             let detail = errorText.substring(0, 200);
             const bodyMatch = errorText.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
             if (bodyMatch && bodyMatch[1].trim()) {
                 detail = bodyMatch[1].trim().substring(0, 200) + '...';
             }
             throw new Error(`Status check error: Expected JSON, got ${contentType || 'unknown'}. Response: ${detail}`);
         }
        const status = await response.json();

        // Update status display
        statusSpan.className = `${status.class} status-indicator`;
        statusSpan.textContent = status.text;

        // Update details if any
        const detailsElement = statusCell.querySelector('small');
        if (detailsElement) detailsElement.remove();

        if (status.details?.trim()) {
            const details = document.createElement('small');
            // IMPORTANT: Assume details from PHP *might* contain HTML. Sanitize if necessary,
            // or ensure PHP escapes it properly before sending. Here we trust PHP output.
            details.innerHTML = `<br><i>${status.details}</i>`;
            statusCell.appendChild(details);
        }

        // Update action buttons
        updateActionButtons(actionButtons, status.text === 'Running');

    } catch (error) {
        console.error(`Status check failed for ${serviceName}:`, error);
        statusSpan.className = 'status-unknown status-indicator';
        // Restore original content or show error text
        statusSpan.innerHTML = statusSpan.dataset.originalContent || 'Error';
        showErrorMessage(findOrCreateMessageContainer(), `Failed to get status for ${serviceName}`, error);
    } finally {
         // Clear dataset attribute if it exists
         delete statusSpan.dataset.originalContent;
    }
}

function updateActionButtons(container, isRunning) {
    // Remove reference to start button since we're not using it anymore
    const stopBtn = container.querySelector('.btn-stop');
    if (stopBtn) stopBtn.disabled = !isRunning;
}

function showActionMessage(container, isSuccess, message, details) {
    if (!container) return;

    container.className = `message ${isSuccess ? 'message-success' : 'message-error'}`;

    let messageHTML = escapeHTML(message);
    if (details?.length) {
        messageHTML += '<ul style="margin-top:10px; list-style: disc; padding-left: 20px;">'; // Added list style
        details.forEach(d => {
            // Ensure detail message is also escaped
            messageHTML += `<li style="color:${d.success ? 'var(--success)' : 'var(--critical)'}; margin-bottom: 3px;">${escapeHTML(d.message)}</li>`;
        });
        messageHTML += '</ul>';
    }

    container.innerHTML = messageHTML;
    container.style.display = 'block';
}

function showErrorMessage(container, context, error) {
    if (!container) return;

    container.className = 'message message-error';
    // Ensure error message itself is escaped
    container.innerHTML = `
        <strong>${escapeHTML(context)}</strong><br>
        <small>${escapeHTML(error.message || 'Unknown error')}</small>
    `;
    container.style.display = 'block';
}

function findOrCreateMessageContainer() {
    // First try to find existing message container in active tab
    let container = document.querySelector('.tab-content-pane.active .message'); // More specific selector if possible

    // If not found, try to find global message container by ID
    if (!container) {
        container = document.querySelector('#service-status-messages'); // Use a specific ID
    }

    // If still not found, create a new one relative to the main content area
    if (!container) {
        // Find the container holding the modules/table
        const serviceArea = document.querySelector('.service-modules-container');
        if (serviceArea) {
            container = document.createElement('div');
            container.id = 'service-status-messages'; // Give it the ID
            container.className = 'message'; // Basic class
            container.style.display = 'none'; // Start hidden
            // Insert it before the service area
            serviceArea.parentNode.insertBefore(container, serviceArea);
        } else {
            // Fallback: find the main heading if serviceArea isn't found
             const heading = document.querySelector('.tab-content-pane.active h2');
             if (heading) {
                 container = document.createElement('div');
                 container.id = 'service-status-messages';
                 container.className = 'message';
                 container.style.display = 'none';
                 heading.parentNode.insertBefore(container, heading.nextSibling);
             }
        }
    }

    return container;
}

// Simple HTML escaping function
function escapeHTML(str) {
    if (typeof str !== 'string') return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}