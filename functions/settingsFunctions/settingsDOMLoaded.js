document.addEventListener('DOMContentLoaded', () => {
    const modal = document.getElementById('settingsModal');
    const openBtn = document.getElementById('openSettingsBtn');
    const closeBtn = document.getElementById('closeModalBtn');

    // Make blacklistChanged flag globally accessible
    window.blacklistChanged = false;

    const topTabLinks = document.querySelectorAll('.top-tab-link');
    const subTabContainers = document.querySelectorAll('.sub-tabs-container');
    const subTabLinks = document.querySelectorAll('.sub-tab-link'); // Selects only actual links
    const contentPanes = document.querySelectorAll('.tab-content-pane');
    const accordionHeadings = document.querySelectorAll('.sub-tab-heading'); // Select all accordion headings

    // --- Accordion Logic ---
    accordionHeadings.forEach(heading => {
        // Set initial state (optional: default to collapsed or open)
        // To start open (remove .collapsed), do nothing here.
        // To start collapsed, uncomment the block below:
        /*
        heading.classList.add('collapsed'); // Mark heading as collapsed
        let nextItem = heading.nextElementSibling;
        while (nextItem && nextItem.classList.contains('sub-tab-item')) {
            nextItem.classList.add('collapsed'); // Collapse items below it
            nextItem = nextItem.nextElementSibling;
        }
        */

        heading.addEventListener('click', () => {
            // Don't run accordion logic on smaller screens where headings are hidden
            if (window.innerWidth <= 768) {
                return;
            }

            // Toggle the 'collapsed' class on the heading itself (for icon rotation)
            heading.classList.toggle('collapsed');

            // Find and toggle the 'collapsed' class on sibling sub-tab items
            let sibling = heading.nextElementSibling;
            while (sibling && sibling.classList.contains('sub-tab-item')) {
                 // Only toggle items that are direct siblings under this heading
                sibling.classList.toggle('collapsed');
                sibling = sibling.nextElementSibling; // Move to the next sibling
            }
        });
    });

     // Function to handle accordion state based on screen size changes
     function handleAccordionStateForResize() {
        const isSmallScreen = window.innerWidth <= 768;
        accordionHeadings.forEach(heading => {
            let nextItem = heading.nextElementSibling;
            while (nextItem && nextItem.classList.contains('sub-tab-item')) {
                if (isSmallScreen) {
                     // Ensure items are visible on small screens
                    nextItem.classList.remove('collapsed');
                } else {
                     // Restore collapsed state based on heading class on larger screens
                     if (heading.classList.contains('collapsed')) {
                         nextItem.classList.add('collapsed');
                     } else {
                         nextItem.classList.remove('collapsed');
                     }
                }
                nextItem = nextItem.nextElementSibling;
            }
        });
    }

    // Add resize listener to manage accordion state
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(handleAccordionStateForResize, 150); // Debounce resize event
    });

     // Initial check in case the page loads on a small screen
     handleAccordionStateForResize();


    // --- Modal Open/Close ---
    openBtn.addEventListener('click', () => {
        modal.classList.add('show');
        // Reset blacklist changed flag when opening modal
        window.blacklistChanged = false;
        // Ensure the currently marked active tab is correctly displayed on open
        let activeTopTab = document.querySelector('.top-tab-link.active');
        if (!activeTopTab && topTabLinks.length > 0) {
            // If somehow no tab is active, default to the first one
            activeTopTab = topTabLinks[0];
            activeTopTab.classList.add('active');
        }
         // Ensure accordion state is correct for current screen size when opening
         handleAccordionStateForResize();
         activateTopTab(activeTopTab); // Activate its content
    });

    closeBtn.addEventListener('click', () => {
        modal.classList.remove('show');
        // Refresh page if blacklist was changed
        if (window.blacklistChanged) {
            setTimeout(() => {
                window.location.reload();
            }, 100);
        }
    });

    // Close modal if clicking outside the content area
    modal.addEventListener('click', (event) => {
        if (event.target === modal) {
            modal.classList.remove('show');
            // Refresh page if blacklist was changed
            if (window.blacklistChanged) {
                setTimeout(() => {
                    window.location.reload();
                }, 100);
            }
        }
    });

    // --- Top Tab Switching ---
    topTabLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            activateTopTab(link);
        });
    });

    function activateTopTab(activeLink) {
        if (!activeLink) return;

        const targetId = activeLink.getAttribute('data-target');

        // Deactivate all top tabs and sub-tab containers
        topTabLinks.forEach(l => l.classList.remove('active'));
        subTabContainers.forEach(c => c.classList.remove('active'));

        // Activate the clicked top tab and corresponding sub-tab container
        activeLink.classList.add('active');
        const activeSubTabContainer = document.getElementById(`sub-tabs-${targetId}`);

        if (activeSubTabContainer) {
            activeSubTabContainer.classList.add('active');

            // Check if there's already an active sub-tab in this container
            let activeSubTab = activeSubTabContainer.querySelector('.sub-tab-link.active');

            // If no sub-tab is active, activate the first one *that is visible*
            if (!activeSubTab) {
                 // Find the first non-collapsed item's link
                 const firstVisibleItem = activeSubTabContainer.querySelector('.sub-tab-item:not(.collapsed)');
                 const firstSubTab = firstVisibleItem ? firstVisibleItem.querySelector('.sub-tab-link') : null;

                 if (firstSubTab) {
                    // Deactivate all sub-tabs in this group first (important if switching back)
                    activeSubTabContainer.querySelectorAll('.sub-tab-link').forEach(l => l.classList.remove('active'));
                    activeSubTab = firstSubTab; // Set the first one as the one to activate
                    activeSubTab.classList.add('active'); // Mark it active immediately for the next step
                 } else {
                     // Maybe all items are collapsed? Try the very first link regardless.
                      const firstEverSubTab = activeSubTabContainer.querySelector('.sub-tab-link');
                      if(firstEverSubTab) {
                         activeSubTabContainer.querySelectorAll('.sub-tab-link').forEach(l => l.classList.remove('active'));
                         activeSubTab = firstEverSubTab;
                         activeSubTab.classList.add('active');
                      }
                 }
            }

            // Activate the content pane for the determined active sub-tab
            if (activeSubTab) {
                 activateSubTabContent(activeSubTab);
            } else {
                 // If no sub-tabs exist at all, hide all content panes
                 contentPanes.forEach(pane => pane.classList.remove('active'));
            }

        } else {
             // If no matching sub-tab container, hide all content panes
             contentPanes.forEach(pane => pane.classList.remove('active'));
        }
    }


    // --- Sub Tab Switching ---
    subTabLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
           activateSubTab(link);
        });
    });

     function activateSubTab(activeLink) {
        if (!activeLink || !activeLink.classList.contains('sub-tab-link')) return; // Ensure it's a clickable link

        const parentContainer = activeLink.closest('.sub-tabs-container');

        // Deactivate other sub-tabs *only within the same group*
        if (parentContainer) {
            parentContainer.querySelectorAll('.sub-tab-link').forEach(l => {
                if (l !== activeLink) {
                    l.classList.remove('active');
                }
            });
        }
         // Add active class to the clicked link
         activeLink.classList.add('active');

         // Activate the corresponding content pane
         activateSubTabContent(activeLink);
     }

     // Helper function to activate content based on sub-tab link
     function activateSubTabContent(activeSubTabLink) {
         const targetId = activeSubTabLink.getAttribute('data-target');

         // Deactivate all content panes first
         contentPanes.forEach(pane => pane.classList.remove('active'));

         // Activate the target content pane
         const activePane = document.getElementById(targetId);
         if (activePane) {
             activePane.classList.add('active');
             // Optional: Scroll content area to top
             const contentArea = activePane.closest('.modal-content-area');
             if (contentArea) {
                contentArea.scrollTop = 0;
             }
         }
     }

    // --- Initial State ---
    // Activate the first top tab and its corresponding first sub-tab's content on load
     const initialTopTab = document.querySelector('.top-tab-link.active');
     if (initialTopTab) {
        activateTopTab(initialTopTab); // This will handle activating the first sub-tab's content
     } else if (topTabLinks.length > 0) {
        // Fallback if no tab is marked active initially
        topTabLinks[0].classList.add('active');
        activateTopTab(topTabLinks[0]);
     }

     // Make sure initial accordion state is correct
     handleAccordionStateForResize();
 
     // --- Theme Selection Logic ---
     const themeSelector = document.getElementById('theme-selector');
     const themeStatusDiv = document.getElementById('theme-update-status');
 
     if (themeSelector && themeStatusDiv) {
         themeSelector.addEventListener('change', async () => {
             const selectedTheme = themeSelector.value;
 
             try {
                 const formData = new FormData();
                 formData.append('theme', selectedTheme);
 
                 const response = await fetch('update_theme.php', {
                     method: 'POST',
                     body: formData
                 });
 
                 if (!response.ok) {
                     throw new Error(`HTTP error! status: ${response.status}`);
                 }
 
                 const result = await response.json();
 
                 if (result.success) {
                     window.location.reload();
                 } else {
                     themeStatusDiv.textContent = `Error: ${result.message || 'Failed to update theme.'}`;
                     themeStatusDiv.className = 'message error';
                 }
 
             } catch (error) {
                 console.error('Error updating theme:', error);
                 themeStatusDiv.textContent = `Error: Could not update theme. ${error.message}`;
                 themeStatusDiv.className = 'message error';
             }
         });
     }
// --- Hostname Configuration Form Submission ---
    // NOTE: Hostname form submission is now handled in configurationHelpers.js
    // No need to set up event listeners here anymore
}); // End DOMContentLoaded