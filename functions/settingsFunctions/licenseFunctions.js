document.addEventListener('DOMContentLoaded', () => {
    const renewButton = document.getElementById('renew-license-btn');
    const licenseFormDiv = document.getElementById('renew-license-form');
    const licenseForm = document.getElementById('license-form-actual');
    const cancelButton = document.getElementById('cancel-renew-btn');
    const licenseInput = document.getElementById('license-key-input');
    const statusMessageDiv = document.getElementById('license-status-message');
    const submitButton = licenseForm ? licenseForm.querySelector('button[type="submit"]') : null;
    const submitButtonOriginalHTML = submitButton ? submitButton.innerHTML : '<i class="fa fa-check" style="margin-right: 6px;"></i>Submit Key';

    // Check if all elements exist before adding listeners
    if (!renewButton || !licenseFormDiv || !licenseForm || !cancelButton || !licenseInput || !statusMessageDiv || !submitButton) {
        console.warn('License renewal elements not found. License functionality might be incomplete.');
        return; // Exit if essential elements are missing
    }

    // Create a hidden iframe for form submission
    let formFrame = document.createElement('iframe');
    formFrame.name = 'license_submit_frame';
    formFrame.style.display = 'none';
    document.body.appendChild(formFrame);
    
    // Set the form to target our hidden iframe
    licenseForm.target = 'license_submit_frame';
    
    // Show the form when the "Renew" button is clicked
    renewButton.addEventListener('click', () => {
        licenseFormDiv.style.display = 'block';
        statusMessageDiv.style.display = 'none'; // Hide previous messages
        statusMessageDiv.textContent = '';
        statusMessageDiv.className = 'message'; // Reset classes
        licenseInput.value = ''; // Clear input field
        licenseInput.focus();
    });

    // Hide the form when the "Cancel" button is clicked
    cancelButton.addEventListener('click', () => {
        licenseFormDiv.style.display = 'none';
        statusMessageDiv.style.display = 'none';
    });

    // Handle form submission
    licenseForm.addEventListener('submit', (event) => {
        // Check for terms agreement first (if the checkbox exists)
        const agreeCheckbox = document.getElementById('agree-terms');
        if (agreeCheckbox && !agreeCheckbox.checked) {
            event.preventDefault();
            alert('You must agree to the terms and conditions to proceed.');
            return;
        }
        
        // Don't prevent default - let it submit to the iframe
        const licenseKey = licenseInput.value.trim();
        
        if (!licenseKey) {
            event.preventDefault();
            showLicenseMessage('Please enter a license key.', false);
            return;
        }

        // Show submitting state
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fa fa-spinner fa-spin" style="margin-right: 6px;"></i>Submitting...';
        statusMessageDiv.style.display = 'none';
        
        // Listen for the iframe to load (submission complete)
        formFrame.onload = function() {
            try {
                // Check the iframe URL after submission
                const frameLocation = formFrame.contentWindow.location.href;
                const frameDocument = formFrame.contentDocument || formFrame.contentWindow.document;
                
                // Reset button state
                submitButton.disabled = false;
                submitButton.innerHTML = submitButtonOriginalHTML;
                
                // Check if the URL contains an error indicator
                if (frameLocation.includes('lic.php?status=error')) {
                    // Error case
                    showLicenseMessage('Invalid license key format or activation failed. Please check your key and try again.', false);
                } else if (frameLocation.includes('syscheck.php')) {
                    // Success case
                    showLicenseMessage('License activated successfully! Redirecting...', true);
                    setTimeout(() => {
                        window.location.href = frameLocation;
                    }, 1500);
                } else {
                    // Try to get any error message from the page
                    try {
                        const errorElements = frameDocument.querySelectorAll('.error, .alert, .message-error');
                        if (errorElements.length > 0) {
                            showLicenseMessage('Error: ' + errorElements[0].textContent.trim(), false);
                        } else {
                            // Default error if no error found
                            showLicenseMessage('The license is not valid. Please enter a valid license key.', false);
                        }
                    } catch (e) {
                        // If we can't access the iframe content due to same-origin policy
                        showLicenseMessage('The license is not valid. Please enter a valid license key.', false);
                    }
                }
            } catch (e) {
                // This can happen due to cross-origin restrictions if the response page 
                // is from a different origin
                console.error('Error checking iframe result:', e);
                
                // Reset form state
                submitButton.disabled = false;
                submitButton.innerHTML = submitButtonOriginalHTML;
                
                // We can't detect the result, so we'll assume success but won't redirect
                showLicenseMessage('License request processed. Please check if it was successful.', true);
            }
        };
        
        // Fallback in case the iframe onload event doesn't fire
        setTimeout(() => {
            if (submitButton.disabled) {
                submitButton.disabled = false;
                submitButton.innerHTML = submitButtonOriginalHTML;
                showLicenseMessage('Request timed out. Please try again.', false);
            }
        }, 30000); // 30 second timeout
    });

    // Helper function to display status messages
    function showLicenseMessage(message, isSuccess) {
        statusMessageDiv.textContent = message;
        statusMessageDiv.className = `message ${isSuccess ? 'message-success' : 'message-error'}`;
        statusMessageDiv.style.display = 'block';
    }
});