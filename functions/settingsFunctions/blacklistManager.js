// Global variables for easy access
let removeSelectedBtn;
let selectedCountSpan;
let hostCheckboxes;
let subnetCheckboxes;

// Initialize when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're on the blacklist manager page
    if (document.querySelector('.blacklist-manager')) {
        // Try to find the elements
        removeSelectedBtn = document.getElementById('remove-selected-btn');
        selectedCountSpan = document.getElementById('selected-count');
        hostCheckboxes = document.querySelectorAll('.select-host');
        subnetCheckboxes = document.querySelectorAll('.select-subnet');
        
        // Force update selection count
        const selectedHosts = document.querySelectorAll('.select-host:checked').length;
        
        if (removeSelectedBtn) {
            // Add click handler for bulk removal
            removeSelectedBtn.addEventListener('click', removeSelected);
            
            // Force update button state
            removeSelectedBtn.disabled = selectedHosts === 0;
            
            // Add a click event specifically to fix any issues
            setTimeout(function() {
                if (selectedHosts > 0 && removeSelectedBtn.disabled) {
                    removeSelectedBtn.disabled = false;
                }
            }, 500);
        }
        
        // Initial update of UI
        updateSelection();
    }

    // Initialize blacklist tab switching
    const blacklistTabLinks = document.querySelectorAll('.blacklist-tab-link');
    const blacklistTabContents = document.querySelectorAll('.blacklist-tab-content');
    
    blacklistTabLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('data-target');
            
            // Activate clicked tab
            blacklistTabLinks.forEach(function(tabLink) {
                tabLink.classList.remove('active');
            });
            this.classList.add('active');
            
            // Show target content
            blacklistTabContents.forEach(function(content) {
                content.style.display = 'none';
            });
            const targetContent = document.getElementById(target);
            if (targetContent) {
                targetContent.style.display = 'block';
            }
            
            // Load content based on the tab
            if (target === 'service-exclusions-tab') {
                // Load muted hosts when service exclusions tab is clicked
                setTimeout(function() {
                    if (document.getElementById('muted-table-container')) {
                        refreshMutedHosts();
                    }
                }, 200);
            }
        });
    });


    
    // Initialize refresh buttons for blacklist and muted hosts
    const refreshBlacklistBtn = document.getElementById('refresh-blacklist-btn');
    if (refreshBlacklistBtn) {
        refreshBlacklistBtn.addEventListener('click', function() {
            refreshBlacklist();
        });
    }
    
    const refreshMutedBtn = document.getElementById('refresh-muted-btn');
    if (refreshMutedBtn) {
        refreshMutedBtn.addEventListener('click', function() {
            refreshMutedHosts();
        });
    }
});

// Function called directly from onclick on select-all checkbox
window.toggleAll = function(checkbox) {
    // Update all host checkboxes
    document.querySelectorAll('.select-host').forEach(hostCheckbox => {
        hostCheckbox.checked = checkbox.checked;
    });
    
    // Update all subnet checkboxes
    document.querySelectorAll('.select-subnet').forEach(subnetCheckbox => {
        subnetCheckbox.checked = checkbox.checked;
        subnetCheckbox.indeterminate = false;
    });
    
    // Update selection count
    updateSelection();
};

// Function called directly from onclick on subnet checkbox
window.toggleSubnet = function(checkbox, subnet) {
    // If checkbox is a string or not provided, find the checkbox element
    if (!checkbox || typeof checkbox === 'string') {
        checkbox = document.querySelector(`.select-subnet[data-subnet="${subnet}"]`);
        
        if (!checkbox) {
            return;
        }
        
        // Toggle the checkbox state if it was found through lookup
        checkbox.checked = !checkbox.checked;
    }
    
    // Simple and direct approach - force all host checkboxes to match the subnet checkbox
    const checkState = checkbox.checked;
    
    // Update all host checkboxes in this subnet
    const hostCheckboxes = document.querySelectorAll(`.select-host[data-subnet="${subnet}"]`);
    
    hostCheckboxes.forEach(hostCheckbox => {
        hostCheckbox.checked = checkState;
    });
    
    // Update selection count
    updateSelection();
    
    // Update select-all checkbox
    updateSelectAllCheckbox();
    
    // Return true for the function to complete successfully
    return true;
};

// Function called directly from onclick on host checkboxes
window.updateSelection = function() {
    // Count selected hosts
    const selectedCount = document.querySelectorAll('.select-host:checked').length;
    
    // Find elements again in case they weren't available earlier
    if (!selectedCountSpan) {
        selectedCountSpan = document.getElementById('selected-count');
    }
    
    if (!removeSelectedBtn) {
        removeSelectedBtn = document.getElementById('remove-selected-btn');
    }
    
    // Update UI elements if they exist
    if (selectedCountSpan) {
        selectedCountSpan.textContent = selectedCount;
    }
    
    if (removeSelectedBtn) {
        removeSelectedBtn.disabled = selectedCount === 0;
    }
    
    // Update subnet checkboxes
    document.querySelectorAll('.select-subnet').forEach(subnetCheckbox => {
        const subnet = subnetCheckbox.getAttribute('data-subnet');
        const subnetHosts = document.querySelectorAll(`.select-host[data-subnet="${subnet}"]`);
        const checkedHosts = document.querySelectorAll(`.select-host[data-subnet="${subnet}"]:checked`);
        
        if (checkedHosts.length === 0) {
            subnetCheckbox.checked = false;
            subnetCheckbox.indeterminate = false;
        } else if (checkedHosts.length === subnetHosts.length) {
            subnetCheckbox.checked = true;
            subnetCheckbox.indeterminate = false;
        } else {
            subnetCheckbox.checked = false;
            subnetCheckbox.indeterminate = true;
        }
    });
    
    // Update select-all checkbox
    updateSelectAllCheckbox();
};

// Helper function to update select-all checkbox state
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('select-all-blacklist');
    if (!selectAllCheckbox) return;
    
    const totalHosts = document.querySelectorAll('.select-host').length;
    const checkedHosts = document.querySelectorAll('.select-host:checked').length;
    
    if (checkedHosts === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedHosts === totalHosts) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// Function to handle bulk removal
function removeSelected() {
    // Find button again in case it wasn't available earlier
    if (!removeSelectedBtn) {
        removeSelectedBtn = document.getElementById('remove-selected-btn');
    }
    
    // Get all selected hosts
    const selectedCheckboxes = document.querySelectorAll('.select-host:checked');
    
    if (selectedCheckboxes.length === 0) {
        alert('No hosts selected for removal.');
        
        // Force enable the button for future selections
        if (removeSelectedBtn) {
            removeSelectedBtn.disabled = true;
        }
        
        return;
    }
    
    // Build array of hosts to remove
    const hostsToRemove = [];
    selectedCheckboxes.forEach(checkbox => {
        hostsToRemove.push({
            ip: checkbox.getAttribute('data-ip'),
            infra: checkbox.getAttribute('data-infra')
        });
    });
    
    // Confirm and process
    if (confirm(`Are you sure you want to remove ${hostsToRemove.length} host${hostsToRemove.length > 1 ? 's' : ''} from the blacklist?`)) {
        // Create XMLHttpRequest for more robust handling
        const xhr = new XMLHttpRequest();
        
        // Define what happens on successful data submission
        xhr.addEventListener("load", function(event) {
            try {
                const data = JSON.parse(xhr.responseText);
                
                if (data.success) {
                    // Remove rows for selected hosts
                    selectedCheckboxes.forEach(checkbox => {
                        removeHostRow(checkbox);
                    });
                    // Set blacklist changed flag
                    window.blacklistChanged = true;
                } else {
                    alert('Error: ' + (data.message || 'Unknown error'));
                }
            } catch (e) {
                alert('Error: Server returned an invalid response. Check the console for details.');
            }
        });
        
        // Define what happens in case of error
        xhr.addEventListener("error", function(event) {
            alert('Error: Request failed. Check your network connection.');
        });
        
        // Use direct path to the PHP handler
        xhr.open("POST", "src/settingsphp/blacklistManager.php", true);
        
        // Set appropriate headers for AJAX request
        xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
        
        // Prepare the data 
        const payload = 'action=remove_multiple_blacklist&hosts=' + encodeURIComponent(JSON.stringify(hostsToRemove));
        
        // Send the request
        xhr.send(payload);
    }
}

// Function to refresh the blacklist
function refreshBlacklist() {
    const container = document.getElementById('blacklist-table-container');
    if (!container) return;
    
    // Show loading indicator
    container.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Loading blacklist...</div>';
    
    // Create XMLHttpRequest
    const xhr = new XMLHttpRequest();
    
    // Define what happens on successful data submission
    xhr.addEventListener("load", function(event) {
        if (xhr.status === 200) {
            container.innerHTML = xhr.responseText;
            
            // Reinitialize checkboxes
            hostCheckboxes = document.querySelectorAll('.select-host');
            subnetCheckboxes = document.querySelectorAll('.select-subnet');
            
            // Update selection state
            updateSelection();
        } else {
            container.innerHTML = '<div class="message message-error"><i class="fa fa-exclamation-circle"></i> Error loading blacklist. Please try again.</div>';
        }
    });
    
    // Define what happens in case of error
    xhr.addEventListener("error", function(event) {
        container.innerHTML = '<div class="message message-error"><i class="fa fa-exclamation-circle"></i> Network error. Please check your connection and try again.</div>';
    });
    
    // Use blacklistManager.php to get blacklist content
    xhr.open("GET", "src/settingsphp/blacklistManager.php", true);
    
    // Set headers for AJAX request
    xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
    
    // Send the request
    xhr.send();
}



// Function to handle single blacklist host removal
function removeFromBlacklist(ip, infra) {
    if (confirm(`Are you sure you want to remove ${ip} from the blacklist?`)) {
        // Create XMLHttpRequest
        const xhr = new XMLHttpRequest();
        
        // Define what happens on successful data submission
        xhr.addEventListener("load", function(event) {
            try {
                const data = JSON.parse(xhr.responseText);
                
                if (data.success) {
                    // Find and remove the row
                    const row = document.querySelector(`tr[data-ip="${ip}"][data-infra="${infra}"]`);
                    if (row) {
                        // Visual transition
                        row.style.backgroundColor = '#d1e7dd';
                        setTimeout(() => {
                            row.style.transition = 'opacity 0.5s ease';
                            row.style.opacity = '0';
                            
                            setTimeout(() => {
                                // Get the parent container
                                const parent = row.closest('.module-section');
                                // Remove the row
                                row.remove();
                                
                                // If this was in a subnet group, update the count
                                if (parent) {
                                    // Update subnet host count
                                    const remainingRows = parent.querySelectorAll('tbody tr').length;
                                    const countSpan = parent.querySelector('.module-status-indicator');
                                    if (countSpan) {
                                        countSpan.textContent = remainingRows + (remainingRows === 1 ? ' host' : ' hosts');
                                    }
                                    
                                    // Hide empty subnet
                                    if (remainingRows === 0) {
                                        parent.removeAttribute('open');
                                        setTimeout(() => {
                                            parent.style.display = 'none';
                                            
                                            // Check if all subnets are now empty
                                            const visibleSubnets = Array.from(document.querySelectorAll('.module-section'))
                                                .filter(s => s.style.display !== 'none' && s.querySelectorAll('tbody tr').length > 0);
                                            
                                            if (visibleSubnets.length === 0) {
                                                // Replace with empty message
                                                document.querySelector('.service-modules-container').innerHTML = 
                                                    '<div class="message message-info">No hosts found in the blacklist.</div>';
                                                document.querySelector('.blacklist-actions').style.display = 'none';
                                            }
                                        }, 300);
                                    }
                                }
                                
                                // Update selection count
                                updateSelection();
                            }, 500);
                        }, 100);
                    }
                } else {
                    alert('Error: ' + (data.message || 'Unknown error'));
                }
            } catch (e) {
                alert('Error: Server returned an invalid response. Check the console for details.');
            }
        });
        
        // Define what happens in case of error
        xhr.addEventListener("error", function(event) {
            alert('Error: Request failed. Check your network connection.');
        });
        
        // Use direct path to the PHP handler
        xhr.open("POST", "src/settingsphp/blacklistManager.php", true);
        
        // Set appropriate headers for AJAX request
        xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
        
        // Prepare the data 
        const payload = `action=remove_blacklist&ip=${encodeURIComponent(ip)}&infra=${encodeURIComponent(infra)}`;
        
        // Send the request
        xhr.send(payload);
    }
}

// Function to handle complete host deletion from blacklist
function deleteCompletelyFromBlacklist(ip, infra) {
    if (confirm(`Are you sure you want to completely delete ${ip} from the database? You will have to rescan it if you want to add it back.\n\nThis option should only be used if the host is stuck, bugged, or cannot be added properly.`)) {
        // Create XMLHttpRequest
        const xhr = new XMLHttpRequest();
        
        // Define what happens on successful data submission
        xhr.addEventListener("load", function(event) {
            try {
                const data = JSON.parse(xhr.responseText);
                
                if (data.success) {
                    // Find and remove the row
                    const row = document.querySelector(`tr[data-ip="${ip}"][data-infra="${infra}"]`);
                    if (row) {
                        // Visual transition
                        row.style.backgroundColor = '#f8d7da';
                        setTimeout(() => {
                            row.style.transition = 'opacity 0.5s ease';
                            row.style.opacity = '0';
                            
                            setTimeout(() => {
                                // Get the parent container
                                const parent = row.closest('.module-section');
                                // Remove the row
                                row.remove();
                                
                                // If this was in a subnet group, update the count
                                if (parent) {
                                    // Update subnet host count
                                    const remainingRows = parent.querySelectorAll('tbody tr').length;
                                    const countSpan = parent.querySelector('.module-status-indicator');
                                    if (countSpan) {
                                        countSpan.textContent = remainingRows + (remainingRows === 1 ? ' host' : ' hosts');
                                    }
                                    
                                    // Hide empty subnet
                                    if (remainingRows === 0) {
                                        parent.removeAttribute('open');
                                        setTimeout(() => {
                                            parent.style.display = 'none';
                                            
                                            // Check if all subnets are now empty
                                            const visibleSubnets = Array.from(document.querySelectorAll('.module-section'))
                                                .filter(s => s.style.display !== 'none' && s.querySelectorAll('tbody tr').length > 0);
                                            
                                            if (visibleSubnets.length === 0) {
                                                // Replace with empty message
                                                document.querySelector('.service-modules-container').innerHTML = 
                                                    '<div class="message message-info">No hosts found in the blacklist.</div>';
                                                document.querySelector('.blacklist-actions').style.display = 'none';
                                            }
                                        }, 300);
                                    }
                                }
                                
                                // Update selection count
                                updateSelection();
                            }, 500);
                        }, 100);
                    }
                } else {
                    alert('Error: ' + (data.message || 'Unknown error'));
                }
            } catch (e) {
                alert('Error: Server returned an invalid response. Check the console for details.');
            }
        });
        
        // Define what happens in case of error
        xhr.addEventListener("error", function(event) {
            alert('Error: Request failed. Check your network connection.');
        });
        
        // Use direct path to the PHP handler
        xhr.open("POST", "src/settingsphp/blacklistManager.php", true);
        
        // Set appropriate headers for AJAX request
        xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
        
        // Prepare the data 
        const payload = `action=delete_completely&ip=${encodeURIComponent(ip)}&infra=${encodeURIComponent(infra)}`;
        
        // Send the request
        xhr.send(payload);
    }
}

// Function to handle single muted host removal (unmuting)
function unmuteSingle(ip, infra) {
    if (confirm(`Are you sure you want to remove ${ip} from service discovery exclusions?`)) {
        // Create XMLHttpRequest
        const xhr = new XMLHttpRequest();
        
        // Define what happens on successful data submission
        xhr.addEventListener("load", function(event) {
            try {
                const data = JSON.parse(xhr.responseText);
                
                if (data.success) {
                    // Find and remove the row
                    const row = document.querySelector(`.muted-host-row[data-ip="${ip}"][data-infra="${infra}"]`);
                    if (row) {
                        // Visual transition
                        row.style.backgroundColor = '#d1e7dd';
                        setTimeout(() => {
                            row.style.transition = 'opacity 0.5s ease';
                            row.style.opacity = '0';
                            
                            setTimeout(() => {
                                // Get the parent container
                                const parent = row.closest('.module-section');
                                // Remove the row
                                row.remove();
                                
                                // If this was in a subnet group, update the count
                                if (parent) {
                                    // Update subnet host count
                                    const remainingRows = parent.querySelectorAll('tbody tr').length;
                                    const countSpan = parent.querySelector('.module-status-indicator');
                                    if (countSpan) {
                                        countSpan.textContent = remainingRows + (remainingRows === 1 ? ' host' : ' hosts');
                                    }
                                    
                                    // Hide empty subnet
                                    if (remainingRows === 0) {
                                        parent.removeAttribute('open');
                                        setTimeout(() => {
                                            parent.style.display = 'none';
                                            
                                            // Check if all subnets are now empty
                                            const visibleSubnets = Array.from(document.querySelectorAll('#muted-table-container .module-section'))
                                                .filter(s => s.style.display !== 'none' && s.querySelectorAll('tbody tr').length > 0);
                                            
                                            if (visibleSubnets.length === 0) {
                                                // Replace with empty message
                                                document.getElementById('muted-table-container').innerHTML = 
                                                    '<div class="message message-info">No hosts found in service discovery exclusions.</div>';
                                            }
                                        }, 300);
                                    }
                                }
                            }, 500);
                        }, 100);
                    }
                } else {
                    alert('Error: ' + (data.message || 'Unknown error'));
                }
            } catch (e) {
                alert('Error: Server returned an invalid response. Check the console for details.');
            }
        });
        
        // Define what happens in case of error
        xhr.addEventListener("error", function(event) {
            alert('Error: Request failed. Check your network connection.');
        });
        
        // Use direct path to the PHP handler
        xhr.open("POST", "src/settingsphp/mutedHostsManager.php", true);
        
        // Set appropriate headers for AJAX request
        xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
        
        // Prepare the data 
        const payload = `action=unmute_host&ip=${encodeURIComponent(ip)}&infra=${encodeURIComponent(infra)}`;
        
        // Send the request
        xhr.send(payload);
    }
}

// Function to refresh the muted hosts list
function refreshMutedHosts() {
    const container = document.getElementById('muted-table-container');
    if (!container) return;
    
    // Show loading indicator
    container.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin"></i> Loading service discovery exclusions...</div>';
    
    // Create XMLHttpRequest
    const xhr = new XMLHttpRequest();
    
    // Define what happens on successful data submission
    xhr.addEventListener("load", function(event) {
        if (xhr.status === 200) {
            container.innerHTML = xhr.responseText;
            
            // Re-attach event handlers for unmute buttons
            document.querySelectorAll('.btn-unmute-host').forEach(button => {
                button.addEventListener('click', function() {
                    unmuteSingle(this.getAttribute('data-ip'), this.getAttribute('data-infra'));
                });
            });
        } else {
            container.innerHTML = '<div class="message message-error"><i class="fa fa-exclamation-circle"></i> Error loading service discovery exclusions. Please try again.</div>';
        }
    });
    
    // Define what happens in case of error
    xhr.addEventListener("error", function(event) {
        container.innerHTML = '<div class="message message-error"><i class="fa fa-exclamation-circle"></i> Network error. Please check your connection and try again.</div>';
    });
    
    // Use direct path to the PHP handler to get muted hosts
    xhr.open("GET", "src/settingsphp/getMutedHosts.php", true);
    
    // Set headers for AJAX request
    xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
    
    // Send the request
    xhr.send();
}

// Helper function to remove a host row with animation
function removeHostRow(checkbox) {
    const row = checkbox.closest('tr');
    const subnet = row.closest('.module-section');
    
    // Visual transition
    row.style.backgroundColor = '#f8d7da';
    setTimeout(() => {
        row.style.transition = 'opacity 0.5s ease';
        row.style.opacity = '0';
        
        setTimeout(() => {
            // Remove the row
            row.remove();
            
            // Update subnet host count
            const remainingRows = subnet.querySelectorAll('tbody tr').length;
            const countSpan = subnet.querySelector('.module-status-indicator');
            countSpan.textContent = remainingRows + (remainingRows === 1 ? ' host' : ' hosts');
            
            // Hide empty subnet
            if (remainingRows === 0) {
                subnet.removeAttribute('open');
                setTimeout(() => {
                    subnet.style.display = 'none';
                    
                    // Check if all subnets are now empty
                    const visibleSubnets = Array.from(document.querySelectorAll('.module-section'))
                        .filter(s => s.style.display !== 'none' && s.querySelectorAll('tbody tr').length > 0);
                    
                    if (visibleSubnets.length === 0) {
                        // Replace with empty message
                        document.querySelector('.service-modules-container').innerHTML = 
                            '<div class="message message-info">No hosts found in the blacklist.</div>';
                        document.querySelector('.blacklist-actions').style.display = 'none';
                    }
                }, 300);
            }
            
            // Update selection count
            updateSelection();
        }, 500);
    }, 100);
}

// Make functions available globally
window.removeFromBlacklist = removeFromBlacklist;
window.deleteCompletelyFromBlacklist = deleteCompletelyFromBlacklist;

