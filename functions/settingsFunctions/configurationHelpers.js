// Mail Config Form Submission Handler
$(document).ready(function() {
    const $form = $('#mail-config-form');
    const $statusDiv = $('#mail-config-status');
    const $testButton = $('#test-mail-btn');
    const $saveButton = $('#save-mail-config-btn'); // Get the new save button
    let saveTimeout;

    function saveMailSettings() {
        // Show loading indicator
        $statusDiv.hide().removeClass('message-success message-error message-info')
                .addClass('message-info').html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Saving mail settings...').fadeIn();

        $.ajax({
            url: 'src/mailConfig/mailConfigHandler.php',
            type: 'POST',
            data: $form.serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $statusDiv.hide().removeClass('message-success message-error message-info')
                        .addClass('message-success').html('<i class="fa fa-check-circle"></i>&nbsp;' + response.message).fadeIn();
                } else {
                    $statusDiv.hide().removeClass('message-success message-error message-info')
                        .addClass('message-error').html('<i class="fa fa-exclamation-triangle"></i>&nbsp;Error: ' + (response.message || 'Unknown error occurred.')).fadeIn();
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $statusDiv.hide().removeClass('message-success message-error message-info')
                    .addClass('message-error').html('<i class="fa fa-times-circle"></i>&nbsp;Request failed. Check console or server logs. Status: ' + textStatus).fadeIn();
            },
            complete: function() {
                // Keep spinner for a moment if success/error message is very fast
                // then fade out the status message after a delay
                setTimeout(function() { $statusDiv.fadeOut(); }, 5000);
            }
        });
    }

    // Auto-save on input change
    $form.find('input').on('input', function() {
        clearTimeout(saveTimeout);
        $statusDiv.hide().removeClass('message-success message-error message-info') // Clear previous status
                .addClass('message-info').html('<i class="fa fa-info-circle"></i>&nbsp;Changes detected. Auto-saving soon...').fadeIn(); // Indicate pending auto-save
        setTimeout(function() { $statusDiv.fadeOut(); }, 2000); // Hide "Changes detected" message

        saveTimeout = setTimeout(saveMailSettings, 3000); // 3 seconds debounce for auto-save
    });

    // Manual save button
    $saveButton.on('click', function() {
        clearTimeout(saveTimeout); // Cancel any pending auto-save
        saveMailSettings(); // Trigger save immediately
    });

    $testButton.on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const originalButtonText = $button.html();
        const $logOutputDiv = $('#mail-test-log-output'); // Select log div
        const $logPre = $logOutputDiv.find('pre'); // Select pre tag inside log div

        $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Sending...');
        $statusDiv.hide().removeClass('message-success message-error message-info');
        $logOutputDiv.hide(); // Hide log output initially
        $logPre.text(''); // Clear previous logs

        $.ajax({
            url: 'src/mailConfig/mailConfigTest.php',
            type: 'POST',
            data: { action: 'testEmail' },
            dataType: 'json',
            success: function(response) {
                // Display success/error message
                if (response.success) {
                    $statusDiv.hide().removeClass('message-success message-error message-info')
                        .addClass('message-success').html('<i class="fa fa-check-circle"></i>&nbsp;' + response.message).fadeIn();
                } else {
                    $statusDiv.hide().removeClass('message-success message-error message-info')
                        .addClass('message-error').html('<i class="fa fa-exclamation-triangle"></i>&nbsp;Error: ' + (response.message || 'Unknown error occurred.')).fadeIn();
                }

                // Display logs if available
                if (response.log_output && response.log_output.trim() !== '') {
                    $logPre.text(response.log_output); // Use .text() to prevent HTML injection
                    $logOutputDiv.show();
                } else {
                    // Optionally show a message if logs are empty but expected
                    // $logPre.text('-- No relevant log entries found --');
                    // $logOutputDiv.show();
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $statusDiv.hide().removeClass('message-success message-error message-info')
                    .addClass('message-error').html('<i class="fa fa-times-circle"></i>&nbsp;Request failed. Check console or server logs. Status: ' + textStatus).fadeIn();
            },
            complete: function() {
                $button.prop('disabled', false).html(originalButtonText);
                // Keep status message visible longer
                setTimeout(function() { $statusDiv.fadeOut(); }, 10000);
            }
        });
    });
});

// Hostname Configuration Manager - Auto-Save after typing
$(document).ready(function() {
    const $hostnameForm = $('#hostname-config-form');
    const $hostnameInput = $('#local_hostname');
    const $statusDiv = $('#hostname-config-status');
    let hostnameTimeout;

    // Function to save hostname automatically
    function saveHostname() {
        const hostname = $hostnameInput.val().trim();

        if (!hostname) {
            $statusDiv.hide().removeClass('message-success message-error message-info')
                    .addClass('message-error').html('<i class="fa fa-exclamation-triangle"></i>&nbsp;Hostname cannot be empty.').fadeIn();
            setTimeout(function() { $statusDiv.fadeOut(); }, 5000);
            return;
        }

        // Show saving indicator
        $statusDiv.hide().removeClass('message-success message-error message-info')
                .addClass('message-info').html('<i class="fa fa-spinner fa-spin"></i>&nbsp;Saving...').fadeIn();

        // Use fetch to handle the POST request and follow the redirect
        fetch('../../../settings-host-proc.php', { // Path relative to settingsModal.php
            method: 'POST',
            body: new FormData($hostnameForm[0]) // Use FormData with the native form element
        })
        .then(response => {
            // Check the final URL after potential redirects
            const finalUrl = new URL(response.url);
            const status = finalUrl.searchParams.get('status');

            $statusDiv.hide().removeClass('message-success message-error message-info'); // Clear previous state

            if (status === 'success') {
                $statusDiv.addClass('message-success').html('<i class="fa fa-check-circle"></i>&nbsp;Hostname updated successfully.').fadeIn();
            } else if (status === 'error') {
                // The PHP script redirects on error, but doesn't seem to pass the specific message via URL.
                // We'll show a generic error message here.
                const errorMessage = 'An error occurred while updating the hostname. Please check system logs.';
                $statusDiv.addClass('message-error').html('<i class="fa fa-exclamation-triangle"></i>&nbsp;Error: ' + errorMessage).fadeIn();
                console.error('Hostname update failed. Check server logs or settings-host-proc.php session errors.');
            } else {
                // If the response wasn't a redirect or didn't have the expected status param
                $statusDiv.addClass('message-error').html('<i class="fa fa-exclamation-triangle"></i>&nbsp;Received an unexpected response from the server.').fadeIn();
                console.error('Unexpected response URL:', response.url);
            }
        })
        .catch(error => {
            // Handle network errors or issues with the fetch request itself
            $statusDiv.hide().removeClass('message-success message-error message-info')
                    .addClass('message-error').html('<i class="fa fa-times-circle"></i>&nbsp;Failed to send request. Check network connection or server status.').fadeIn();
            console.error('Error saving hostname:', error);
        })
        .finally(() => {
            // Fade out the status message after a delay
            setTimeout(function() { $statusDiv.fadeOut(); }, 5000);
        });
    }

    // Set up input event to trigger auto-save with debounce
    $hostnameInput.on('input', function() {
        clearTimeout(hostnameTimeout);
        hostnameTimeout = setTimeout(saveHostname, 1000); // 1 second debounce
    });

    // Handle the submit event for the form (in case Enter key is pressed)
    $hostnameForm.on('submit', function(e) {
        e.preventDefault();
        clearTimeout(hostnameTimeout);
        saveHostname();
    });
});