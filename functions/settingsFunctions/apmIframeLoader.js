document.addEventListener('DOMContentLoaded', function() {
    // Add iframe styles to document (removed loading spinner styles)
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
        .iframe-container {
            position: relative;
            min-height: 500px;
            width: 100%;
            overflow: hidden;
        }
        .apm-iframe {
            position: relative;
            z-index: 1;
            width: 100%;
            border: none !important;
            border-radius: 0 !important;
            background: #fff;
            overflow: hidden;
        }
        .apm-refresh-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: inherit;
            font-size: inherit;
            padding: 0 4px;
        }
    `;
    document.head.appendChild(styleSheet);

    // Cache the style rules we inject into every iframe document so we can
    // clone and prepend them instantly on subsequent navigations (avoids FOUC)
    let _apmCachedStyleElement = null;

    /* ============================================================
       THEME DETECTION  +  THEME-SPECIFIC STYLE HELPERS (shared with
       openModals.js but duplicated here to avoid cross-file deps)
       ============================================================ */
    function detectCurrentTheme () {
        try {
            // 1) Inspect <link rel="stylesheet"> paths for /styles/<theme>/…
            const themeLink = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
                .find(l => l.href && l.href.includes('/styles/'));
            if (themeLink) {
                const m = themeLink.href.match(/\/styles\/([^\/]+)/);
                if (m && m[1]) return m[1];
            }
            // 2) Fall-back to body class markers
            if (document.body.classList.contains('light-theme')) return 'light-theme';
            if (document.body.classList.contains('dark-theme'))  return 'dark-theme';
        } catch (e) {
            console.error('Theme detection failed:', e);
        }
        return 'dark-theme';
    }

    function getLightThemeStyles () {
        // Light theme already matches the default baseStyle injected above,
        // so we do not need to add any extra overrides.
        return ``;
    }

    function getDarkThemeStyles () {
        /* Override only the parts that differ between the existing (light) baseStyle
           and the desired dark appearance so that we stay self-contained. */
        return `
            /* === Dark-theme overrides for default buttons === */
            input[type="button"],
            input[type="submit"],
            button {
                background: #444 !important;
                background-color: #444 !important;
                color: #f8f9fa !important;
                border: 1px solid #888 !important;
            }
            input[type="button"]:hover,
            input[type="submit"]:hover,
            button:hover {
                background-color: #555 !important;
                border: 1px solid #888 !important;
            }

            /* === Dark theme background and text for body & main content area === */
            body,
            #content_main {
                background-color: #222 !important; /* matches --background */
                color: #f8f9fa !important;            /* matches --text */
            }

            /* === Table styling for dark theme === */
            .table-striped table {
                background-color: #222222 !important;  /* matches --surface */
                color: #f8f9fa !important;
                border-color: #717171 !important;      /* matches --border */
                border-collapse: collapse !important;
            }
            .table-striped th,
            .table-striped td {
                background-color: transparent !important; /* inherit table bg by default */
                color: #f8f9fa !important;
                border-color: #717171 !important;
            }
            .table-striped tr:nth-child(even) {
                background-color: #373737 !important;  /* matches --pending-bg for striping */
            }
            .table-striped tr:hover {
                background-color: #404040 !important;  /* matches --warning-bg for hover */
            }
        `;
    }

    // Improved function to adjust iframe dimensions with consistent scaling
    function adjustIframeDimensions(iframe) {
        try {
            const doc = iframe.contentDocument || iframe.contentWindow.document;
            if (!doc || !doc.body) return;

            // Reset any previous transformations
            doc.body.style.transform = 'none';
            doc.body.style.transformOrigin = 'top left';
            doc.body.style.width = '100%';
            doc.body.style.overflow = 'hidden';
            
            // Force a reflow to get accurate dimensions
            doc.body.offsetHeight;
            
            // Get dimensions after reset
            const contentWidth = Math.max(doc.documentElement.scrollWidth, doc.body.scrollWidth);
            const contentHeight = Math.max(doc.documentElement.scrollHeight, doc.body.scrollHeight);
            const frameWidth = iframe.clientWidth;

            // Apply a universal 86.5% scale to all iframe content
            const scale = 0.865;
            doc.body.style.transform = `scale(${scale})`;
            doc.body.style.transformOrigin = 'top left';
            doc.body.style.width = `${100 / scale}%`;
            iframe.style.height = Math.max(500, contentHeight * scale) + 'px';

            // Ensure no scrollbars on the iframe document
            doc.documentElement.style.overflow = 'hidden';
            doc.body.style.overflow = 'hidden';
            
        } catch (e) {
            console.error('Error adjusting iframe dimensions:', e);
            // Fallback height if scaling fails
            if (iframe) {
                iframe.style.height = '500px';
            }
        }
    }

    // Improved function to inject styles into iframe with better content detection
    function injectIframeStyles(iframe) {
        return new Promise((resolve, reject) => {
            try {
                const doc = iframe.contentDocument || iframe.contentWindow.document;
                if (!doc || !doc.head) {
                    reject(new Error('Cannot access iframe document'));
                    return;
                }

                // Check if we already injected styles to avoid duplicates
                if (doc.querySelector('#apm-injected-styles')) {
                    resolve();
                    return;
                }

                let styleToInject;

                // If we've already created the canonical style element once, clone it.
                if (_apmCachedStyleElement) {
                    styleToInject = _apmCachedStyleElement.cloneNode(true);
                } else {
                    // First-time creation of the canonical style element.
                    const baseStyle = document.createElement('style');
                    baseStyle.id = 'apm-injected-styles';
                    baseStyle.textContent = `
                        td[width="150"][align="center"][valign="top"] { display: none !important; }
                        nav, nav.navbar, .navbar { display: none !important; }
                        body { 
                            min-height: 100vh !important; 
                            overflow: hidden !important;
                            margin: 0 !important;
                            padding: 0 !important;
                        }
                        html { 
                            overflow: hidden !important; 
                        }
                        * {
                            box-sizing: border-box !important;
                        }
                        .fa.fa-lightbulb-o.fa-lg {
                            display: none !important;
                        }
                        /* === Standardise default buttons inside iframe to match Light Theme === */
                        input[type="button"],
                        input[type="submit"],
                        button {
                            background: #f1f5f9 !important;
                            background-color: #f1f5f9 !important; /* same as .btn-stop /.btn-restart */
                            color:  #111827 !important; /* fall back to dark text if CSS vars unavailable */
                            padding: 6px 10px !important;
                            min-width: 32px !important;
                            height: 32px !important;
                            border: 1px solid #cbd5e1 !important; /* matches --border */
                            border-radius: 4px !important;
                            cursor: pointer !important;
                            font-size: 13px !important;
                            line-height: 1 !important;
                            display: inline-flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                        }
                        input[type="button"]:hover,
                        input[type="submit"]:hover,
                        button:hover {
                            background-color: #e2e8f0 !important; /* hover state from .btn-stop:hover */
                            border: 1px solid #cbd5e1 !important;
                            border-radius: 4px !important;
                        }
                        /* Disabled state */
                        input[type="button"][disabled],
                        input[type="submit"][disabled],
                        button[disabled] {
                            opacity: 0.5 !important;
                            cursor: not-allowed !important;
                        }
                    `;
                    // Store a reference so we can clone on the next navigation
                    _apmCachedStyleElement = baseStyle;
                    styleToInject = baseStyle.cloneNode(true);
                }

                // Prepend so rules apply as early as possible in the new document
                doc.head.prepend(styleToInject);

                /* ===============================
                   THEME-SPECIFIC STYLE INJECTION
                   =============================== */
                try {
                    const theme = detectCurrentTheme();
                    if (!doc.querySelector('#apm-theme-styles')) {
                        const themeStyle = document.createElement('style');
                        themeStyle.id = 'apm-theme-styles';
                        themeStyle.textContent = theme === 'light-theme' ? getLightThemeStyles() : getDarkThemeStyles();
                        doc.head.appendChild(themeStyle);
                    }
                } catch (e) {
                    console.error('Failed to inject theme styles into iframe:', e);
                }

                // -------------------------------------------------------------
                // Detect form submissions triggered via Enter key (or programmatic
                // submit calls) so we can react similarly to click-based searches.
                // -------------------------------------------------------------
                doc.querySelectorAll('form').forEach(formEl => {
                    if (!formEl._apmSubmitListenerAdded) {
                        formEl.addEventListener('submit', () => {
                            // Mark that we want to reset scroll once the next significant DOM change finishes
                            iframe._apmResetModalScroll = true;
                            // Immediately scroll modal content to top so user is ready for new content
                            const modalContentArea = document.querySelector('.modal-content-area');
                            if (modalContentArea) {
                                modalContentArea.scrollTop = 0;
                            }
                            // Hide iframe to avoid FOUC during the upcoming update
                            iframe.style.visibility = 'hidden';

                            // Schedule style reinjection and dimension adjustment
                            setTimeout(async () => {
                                try {
                                    await injectIframeStyles(iframe);
                                    adjustIframeDimensions(iframe);
                                } catch (e) {
                                    console.error('Error handling form-submit navigation:', e);
                                }
                            }, 50);
                        });
                        formEl._apmSubmitListenerAdded = true;
                    }
                });

                // Handle login form to show loading during authentication
                const loginForm = doc.querySelector('form[name="frmPassword"], form.form-signin');
                if (loginForm && !loginForm._apmModified) {
                    // Get the original intended URL from the container
                    const container = iframe.closest('.iframe-container');
                    const originalUrl = container?.parentElement?.dataset?.url || iframe.src;
                    
                    // Add event listener to form submission
                    loginForm.addEventListener('submit', function(e) {
                        // After a short delay (to allow form submission), reload with original URL
                        setTimeout(() => {
                            iframe.src = originalUrl;
                        }, 1000); // 1 second delay to allow login to process
                    });
                    
                    loginForm._apmModified = true;
                }

                // Set up mutation observer to detect significant content changes only
                if (!iframe._apmMutationObserver) {
                    const observer = new MutationObserver((mutations) => {
                        let significantChange = false;
                        mutations.forEach((mutation) => {
                            // Only trigger for significant DOM changes (new forms, tables, major content)
                            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                                for (let node of mutation.addedNodes) {
                                    if (node.nodeType === Node.ELEMENT_NODE) {
                                        // Only consider truly significant changes
                                        const isSignificant = 
                                            node.tagName === 'FORM' ||
                                            node.tagName === 'TABLE' ||
                                            node.tagName === 'TBODY' ||
                                            node.tagName === 'TR' ||
                                            (node.tagName === 'DIV' && node.children.length > 5) ||
                                            node.classList.contains('content') ||
                                            node.classList.contains('main') ||
                                            (node.tagName === 'TD' && node.classList.contains('sitenumber'));
                                        
                                        if (isSignificant) {
                                            significantChange = true;
                                            break;
                                        }
                                    }
                                }
                            }
                        });
                        
                        if (significantChange) {
                            setTimeout(async () => {
                                try {
                                    await injectIframeStyles(iframe);
                                    adjustIframeDimensions(iframe);
                                    // Reset scroll only if navigation flagged it.
                                    if (iframe._apmResetModalScroll) {
                                        const modalContentArea = document.querySelector('.modal-content-area');
                                        if (modalContentArea) {
                                            modalContentArea.scrollTop = 0;
                                        }
                                        iframe._apmResetModalScroll = false;
                                    }
                                } catch (e) {
                                    console.error('Error handling content change:', e);
                                }
                            }, 50);
                        }
                    });
                    
                    // Observe the full subtree so we catch deeper DOM mutations (e.g., table row updates
                    // when search is submitted via the Enter key or reset via the "x" icon).
                    observer.observe(doc.body, {
                        childList: true,
                        subtree: true, // Watch entire subtree for significant updates
                        attributes: false
                    });
                    iframe._apmMutationObserver = observer;
                }
                
                // Enhanced click event listener for better navigation detection
                if (!doc._apmClickListenerAdded) {
                    doc.addEventListener('click', function(e) {
                        const clickable = e.target.closest('td.sitenumber, [onclick], a, button, input[type="submit"]');
                        if (clickable) {
                            const onclickValue = clickable.getAttribute('onclick') || '';
                            const href = clickable.getAttribute('href') || '';
                            
                            const isSiteNumber = clickable.matches && clickable.matches('td.sitenumber');
                            const triggersNavigation = 
                                onclickValue.includes("actionPic(") ||
                                onclickValue.includes('LockButton()') ||
                                onclickValue.includes('addDataset()') ||
                                onclickValue.includes('abort()') ||
                                onclickValue.includes('del(') ||
                                onclickValue.includes('submit') ||
                                onclickValue.includes('window.location') ||
                                onclickValue.includes('document.location') ||
                                (href !== '' && !href.startsWith('#')) ||
                                (clickable.tagName === 'BUTTON' && clickable.type === 'submit') ||
                                (clickable.tagName === 'INPUT' && clickable.type === 'submit') ||
                                isSiteNumber;

                            if (triggersNavigation) {
                                // Mark that we want to reset scroll once the next significant DOM change finishes
                                iframe._apmResetModalScroll = true;
                                // Immediately scroll modal content to top so user is ready for new content
                                const modalContentArea = document.querySelector('.modal-content-area');
                                if (modalContentArea) {
                                    modalContentArea.scrollTop = 0;
                                }
                                // Hide iframe immediately to avoid FOUC on the upcoming navigation
                                iframe.style.visibility = 'hidden';
                                // Set up a more comprehensive monitoring system
                                let hasContentChanged = false;
                                const checkForChanges = () => {
                                    if (!hasContentChanged) {
                                        setTimeout(async () => {
                                            try {
                                                await injectIframeStyles(iframe);
                                                adjustIframeDimensions(iframe);
                                                hasContentChanged = true;
                                            } catch (e) {
                                                console.error('Error handling click navigation:', e);
                                                hasContentChanged = true;
                                            }
                                        }, 50);
                                    }
                                };
                                
                                // Check multiple times to catch different loading patterns
                                setTimeout(checkForChanges, 200);
                                setTimeout(checkForChanges, 800);
                            }
                        }
                    });
                    doc._apmClickListenerAdded = true;
                }
                
                // Wait for styles to be applied then adjust dimensions
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                        adjustIframeDimensions(iframe);
                        // Final scroll reset if flag still set (ensures top after first render)
                        if (iframe._apmResetModalScroll) {
                            const modalContentArea = document.querySelector('.modal-content-area');
                            if (modalContentArea) {
                                modalContentArea.scrollTop = 0;
                            }
                            iframe._apmResetModalScroll = false;
                        }
                        // Show iframe now that styles and sizing are ready
                        iframe.style.visibility = 'visible';
                        resolve();
                    });
                });
            } catch (e) {
                console.error('Failed to inject styles into iframe:', e);
                reject(e);
            }
        });
    }

    // Enhanced function to check for authentication redirects and content readiness
    function checkIframeContent(iframe) {
        try {
            const doc = iframe.contentDocument || iframe.contentWindow.document;
            if (!doc || !doc.body) return { isReady: false, needsAuth: false };
            
            const bodyText = doc.body.textContent || '';
            const bodyHTML = doc.body.innerHTML || '';
            
            // More specific login form detection
            const hasLoginForm = doc.querySelector('form[name="frmPassword"], form.form-signin, input[name="tfUsername"], input[name="tfPassword"]');
            const hasAuthenticationHeading = bodyText.includes('Authentication') || bodyHTML.includes('form-signin');
            const isLoginPage = hasLoginForm || hasAuthenticationHeading;
            
            // Check for actual content (not just login page)
            const hasContent = bodyText.trim().length > 100;
            const hasMainContent = doc.querySelector('table:not(.header), form:not([name="frmPassword"]):not(.form-signin), .content, #main, .main');
            const hasSignificantContent = hasContent && bodyText.trim().length > 500; // More substantial content
            
            // Check if page is completely blank/empty
            const isEmpty = bodyText.trim().length < 20;
            
            return {
                isReady: hasSignificantContent && hasMainContent && !isLoginPage,
                needsAuth: isLoginPage,
                isEmpty: isEmpty,
                isLoginPage: isLoginPage
            };
        } catch (e) {
            console.error('Error checking iframe content:', e);
            return { isReady: false, needsAuth: false, isEmpty: true, isLoginPage: false };
        }
    }

    // Function to load iframe content with improved authentication handling
    function loadIframeContent(container) {
        if (!container || container.querySelector('iframe')) return;
        
        const url = container.dataset.url;
        if (!url) return;

        // Create iframe container
        const iframeContainer = document.createElement('div');
        iframeContainer.className = 'iframe-container';
        
        const iframe = document.createElement('iframe');
        iframe.className = 'apm-iframe';
        iframe.src = url;
        iframe.frameBorder = "0";
        iframe.style.width = "100%";
        iframe.style.height = "500px";
        // Keep iframe hidden until our custom styles are injected and applied to avoid FOUC
        iframe.style.visibility = 'hidden';
        
        let authRetryCount = 0;
        const maxAuthRetries = 5;
        let lastContentCheck = '';
        
        // Enhanced iframe load handler
        iframe.addEventListener('load', async function() {
            try {
                // Wait a moment for content to fully load
                await new Promise(resolve => setTimeout(resolve, 50));
                
                // Check content status
                const contentStatus = checkIframeContent(iframe);
                const currentContent = iframe.contentDocument?.body?.textContent || '';
                const currentUrl = iframe.contentDocument?.location?.href || '';
                
                // If we have a login page, inject styles and show it
                if (contentStatus.isLoginPage) {
                    await injectIframeStyles(iframe);
                    adjustIframeDimensions(iframe);
                    iframe.style.visibility = 'visible';
                    return;
                }
                
                // If content is empty or we still need auth, retry
                if ((contentStatus.isEmpty || currentContent === lastContentCheck) && authRetryCount < maxAuthRetries) {
                    authRetryCount++;
                    lastContentCheck = currentContent;
                    setTimeout(() => {
                        iframe.src = iframe.src;
                    }, 1000);
                    return;
                }
                
                // If we don't have ready content after retries, show what we have
                if (!contentStatus.isReady && authRetryCount >= maxAuthRetries) {
                    await injectIframeStyles(iframe);
                    adjustIframeDimensions(iframe);
                    iframe.style.visibility = 'visible';
                    return;
                }
                
                // Content appears to be ready
                if (contentStatus.isReady) {
                    await injectIframeStyles(iframe);
                    
                    // Set up enhanced request interception for AJAX and fetch
                    try {
                        const iframeWindow = iframe.contentWindow;
                        
                        // Intercept XMLHttpRequest
                        if (!iframeWindow._apmXHRIntercepted) {
                            const originalXHR = iframeWindow.XMLHttpRequest;
                            iframeWindow.XMLHttpRequest = function() {
                                const xhr = new originalXHR();
                                const originalSend = xhr.send;
                                
                                xhr.send = function() {
                                    const handleResponse = async () => {
                                        try {
                                            // Wait a bit for DOM to update
                                            setTimeout(async () => {
                                                await injectIframeStyles(iframe);
                                                adjustIframeDimensions(iframe);
                                            }, 50);
                                        } catch (e) {
                                            console.error('Error handling AJAX response:', e);
                                        }
                                    };
                                    
                                    xhr.addEventListener('load', handleResponse);
                                    
                                    return originalSend.apply(this, arguments);
                                };
                                
                                return xhr;
                            };
                            iframeWindow._apmXHRIntercepted = true;
                        }
                        
                        // Intercept fetch
                        if (!iframeWindow._apmFetchIntercepted) {
                            const originalFetch = iframeWindow.fetch;
                            iframeWindow.fetch = function() {
                                return originalFetch.apply(this, arguments)
                                    .then(async response => {
                                        setTimeout(async () => {
                                            try {
                                                await injectIframeStyles(iframe);
                                                adjustIframeDimensions(iframe);
                                            } catch (e) {
                                                console.error('Error handling fetch response:', e);
                                            }
                                        }, 50);
                                        return response;
                                    })
                                    .catch(error => {
                                        throw error;
                                    });
                            };
                            iframeWindow._apmFetchIntercepted = true;
                        }
                    } catch (e) {
                        console.error('Error setting up request interception:', e);
                    }

                    // Final check and cleanup
                    setTimeout(async () => {
                        try {
                            await injectIframeStyles(iframe);
                            adjustIframeDimensions(iframe);
                            // Final scroll reset if flag still set (ensures top after first render)
                            if (iframe._apmResetModalScroll) {
                                const modalContentArea = document.querySelector('.modal-content-area');
                                if (modalContentArea) {
                                    modalContentArea.scrollTop = 0;
                                }
                                iframe._apmResetModalScroll = false;
                            }
                            iframe.style.visibility = 'visible';
                        } catch (e) {
                            console.error('Error in final content preparation:', e);
                        }
                    }, 50);
                } else {
                    // Show whatever content we have
                    await injectIframeStyles(iframe);
                    adjustIframeDimensions(iframe);
                    iframe.style.visibility = 'visible';
                }
                
            } catch (e) {
                console.error('Error in iframe load handler:', e);
            }
        });
        
        // Handle iframe errors
        iframe.addEventListener('error', function() {
            console.error('Iframe failed to load');
        });
        
        iframeContainer.appendChild(iframe);
        container.appendChild(iframeContainer);
    }

    // Add click event listeners to APM tab links
    document.querySelectorAll('.top-tab-link[data-target="apm"]').forEach(link => {
        link.addEventListener('click', function() {
            // Find the visible APM tab content and load its iframe
            setTimeout(() => {
                const visiblePane = document.querySelector('.tab-content-pane.active');
                if (visiblePane) {
                    const container = visiblePane.querySelector('.iframe-container');
                    if (container) {
                        loadIframeContent(container);
                    }
                }
            }, 50);
        });
    });

    // Add click event listeners to APM sub-tab links
    document.querySelectorAll('.sub-tab-link').forEach(link => {
        link.addEventListener('click', function() {
            const targetId = this.dataset.target;
            if (targetId && targetId.startsWith('apm-')) {
                const targetPane = document.getElementById(targetId);
                if (targetPane) {
                    const container = targetPane.querySelector('.iframe-container');
                    if (container) {
                        loadIframeContent(container);
                    }
                }
            }
        });
    });

    // Add click event listeners to APM refresh buttons
    document.querySelectorAll('.apm-refresh-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent any unintended bubbling
            const paneId = this.dataset.paneId;
            if (!paneId) return;
            const pane = document.getElementById(paneId);
            if (!pane) return;

            const container = pane.querySelector('.iframe-container');
            if (!container) return;

            const originalUrl = container.dataset.url;
            if (!originalUrl) return;

            const iframe = container.querySelector('iframe.apm-iframe');

            if (iframe) {
                // Reload iframe to its original URL
                iframe.src = originalUrl;

                // Ensure visibility and reset scroll positioning
                iframe.style.visibility = 'hidden'; // Hide to avoid FOUC during reload
                iframe._apmResetModalScroll = true;

                setTimeout(() => {
                    // Failsafe to ensure iframe becomes visible after reload
                    iframe.style.visibility = 'visible';
                }, 500);
            } else {
                // Iframe hasn't been initialized yet – load fresh content
                loadIframeContent(container);
            }
        });
    });
});