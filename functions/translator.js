class Translator {
    constructor(dictionaries, selectedLang, defaultLang = 'en') {
        this.dictionaries = dictionaries;
        this.selectedLang = selectedLang;
        this.defaultLang = defaultLang;
    }

    translatePage(context = document) {
        if (!this.dictionaries[this.selectedLang]) return;
    
        const dict = this.dictionaries[this.selectedLang];
    
        function translateTextNodes(element) {
            element.childNodes.forEach(node => {
                if (node.nodeType === Node.TEXT_NODE) {
                    const originalText = node.nodeValue;
                    const trimmedText = originalText.trim();
                    
                    if (trimmedText && dict[trimmedText]) {
                        // Preserve leading and trailing whitespace
                        const leadingWhitespace = originalText.match(/^\s*/)[0];
                        const trailingWhitespace = originalText.match(/\s*$/)[0];
                        node.nodeValue = leadingWhitespace + dict[trimmedText] + trailingWhitespace;
                    }
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                    // Translate placeholder attribute
                    if (node.hasAttribute('placeholder')) {
                        const placeholderText = node.getAttribute('placeholder').trim();
                        if (dict[placeholderText]) {
                            node.setAttribute('placeholder', dict[placeholderText]);
                        }
                    }
    
                    // Translate title attribute
                    if (node.hasAttribute('title')) {
                        const titleText = node.getAttribute('title').trim();
                        if (dict[titleText]) {
                            node.setAttribute('title', dict[titleText]);
                        }
                    }
    
                    translateTextNodes(node); // Recursive translation
                }
            });
        }
    
        translateTextNodes(context.body || context);        
    }  

    translateIframe(iframe) {
        try {
            if (iframe.contentDocument) {
                this.translatePage(iframe.contentDocument);
            }
        } catch (e) {
            console.warn('Cannot access iframe content due to cross-origin restrictions:', e);
        }
    }

    setupLanguageDropdown() {
        const select = document.getElementById('langSelect');
        select.addEventListener('change', async (event) => {
            const newLang = event.target.value;
            this.selectedLang = newLang;

            try {
                const response = await fetch('update_lang.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `lang=${encodeURIComponent(newLang)}`
                });
                const result = await response.text();
                console.log("Response: ", result);
                if (response.ok) {
                    window.location.reload();
                } else {
                    console.error('Update failed:', result);
                }
            } catch (error) {
                console.error('Fetch error:', error);
            }
        });
    }

    init() {
        this.translatePage();
        this.setupLanguageDropdown();
    }
}