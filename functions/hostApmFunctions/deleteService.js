function callDeleteService(ipAddress, serviceName) {
    // Check if serviceName is an array for multiple services
    const isMultiple = Array.isArray(serviceName);
    const serviceCount = isMultiple ? serviceName.length : 1;
    const serviceText = isMultiple ? 
        (serviceCount > 1 ? `${serviceCount} selected services` : serviceName[0]) : 
        serviceName;
        
    if (!confirm(`Delete ${serviceText}?`)) {
        return;
    }
    const phpScriptUrl = 'delete_service.php';

    // Create a floating message element with consistent styling
    const floatingMessage = document.createElement('div');
    floatingMessage.id = 'service-delete-loading';
    floatingMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 8px;
        z-index: 10000;
        text-align: center;
    `;
    floatingMessage.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i>
        </div>
        <div>Deleting ${serviceText}...</div>
    `;

    document.body.appendChild(floatingMessage);

    const formData = new FormData();
    formData.append('ip', ipAddress);
    
    if (isMultiple) {
        // Add each service as a separate form field
        serviceName.forEach((service, index) => {
            formData.append(`servicetodelete[${index}]`, service);
        });
    } else {
        formData.append('servicetodelete', serviceName);
    }

    fetch(phpScriptUrl, {
        method: 'POST',
        body: formData,
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(data => {
            // Remove loading message
            const loadingElement = document.getElementById('service-delete-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            if (typeof data === 'string' && data.includes("Success")) {
                // Show success message briefly then reload
                const successDiv = document.createElement('div');
                successDiv.style.cssText = floatingMessage.style.cssText;
                successDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-check-circle" style="font-size: 24px; color: #4caf50;"></i>
                    </div>
                    <div>${serviceText} deleted successfully!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Refreshing...</div>
                `;
                document.body.appendChild(successDiv);
                
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            } else {
                // Show error message
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = floatingMessage.style.cssText;
                errorDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #ff9800;"></i>
                    </div>
                    <div>Service deletion failed!</div>
                    <div style="font-size: 12px; margin-top: 5px;">Please try again</div>
                `;
                document.body.appendChild(errorDiv);
                
                setTimeout(() => {
                    errorDiv.remove();
                }, 3000);
            }
        })
        .catch(error => {
            // Remove loading message
            const loadingElement = document.getElementById('service-delete-loading');
            if (loadingElement) {
                loadingElement.remove();
            }

            console.error('There was an error calling the PHP script:', error);
            
            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = floatingMessage.style.cssText;
            errorDiv.innerHTML = `
                <div style="margin-bottom: 10px;">
                    <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #f44336;"></i>
                </div>
                <div>An error occurred during service deletion</div>
                <div style="font-size: 12px; margin-top: 5px;">Please try again</div>
            `;
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 3000);
        });
}

// Add multiselection functionality
let selectedServices = [];

function toggleServiceSelection(serviceName, element) {
    const index = selectedServices.indexOf(serviceName);
    
    if (index === -1) {
        // Add to selection
        selectedServices.push(serviceName);
        element.classList.add('selected');
    } else {
        // Remove from selection
        selectedServices.splice(index, 1);
        element.classList.remove('selected');
    }
    
    // Update corresponding row selection visual in list view (if present)
    try {
        const encodedName = serviceName.replace(/\s+/g, '+');
        const row = document.querySelector(`tr[data-service="${encodedName}"]`);
        if (row) {
            const nowSelected = selectedServices.includes(serviceName);
            row.classList.toggle('selected', nowSelected);
        }
    } catch (e) {
        // Fail silently if any error occurs
    }

    // Update the delete button visibility
    updateDeleteButtonVisibility();
}

function updateDeleteButtonVisibility() {
    const deleteButton = document.getElementById('multi-delete-button');
    if (deleteButton) {
        if (selectedServices.length > 0) {
            deleteButton.style.display = 'flex';
            deleteButton.textContent = `Delete (${selectedServices.length})`;
        } else {
            deleteButton.style.display = 'none';
        }
    }
}

function deleteSelectedServices() {
    if (selectedServices.length > 0) {
        const hostip = urlParams.get('hostip');
        callDeleteService(hostip, selectedServices);
    }
}

function toggleSelectionMode() {
    const servicesContainer = document.querySelector('.services-grid');
    const serviceCards = document.querySelectorAll('.service-card');
    const selectionButton = document.getElementById('selection-mode-button');
    
    // Clear any previous selections
    selectedServices = [];
    serviceCards.forEach(card => card.classList.remove('selected'));
    // Clear selection highlight from any table rows
    const selectedRows = document.querySelectorAll('.services-table tbody tr.selected');
    selectedRows.forEach(row => row.classList.remove('selected'));
    
    if (servicesContainer.classList.contains('selection-mode')) {
        // Turn off selection mode
        servicesContainer.classList.remove('selection-mode');
        serviceCards.forEach(card => {
            card.removeEventListener('click', handleSelectionClick);
        });
        selectionButton.innerHTML = '<i class="fa fa-check-square-o"></i>';
        selectionButton.title = 'Enable selection mode';
        
        // Hide delete button
        const deleteButton = document.getElementById('multi-delete-button');
        if (deleteButton) {
            deleteButton.style.display = 'none';
        }
    } else {
        // Turn on selection mode
        servicesContainer.classList.add('selection-mode');
        serviceCards.forEach(card => {
            const serviceName = card.querySelector('.service-title').textContent;
            card.addEventListener('click', handleSelectionClick);
        });
        selectionButton.innerHTML = '<i class="fa fa-times"></i>';
        selectionButton.title = 'Cancel selection mode';
        
        // Show delete button if not already present
        let deleteButton = document.getElementById('multi-delete-button');
        if (!deleteButton) {
            deleteButton = document.createElement('button');
            deleteButton.id = 'multi-delete-button';
            deleteButton.className = 'multi-delete-button';
            deleteButton.innerHTML = 'Delete (0)';
            deleteButton.style.display = 'none';
            deleteButton.addEventListener('click', deleteSelectedServices);
            document.querySelector('.status-header').appendChild(deleteButton);
        }
    }
}

function handleSelectionClick(e) {
    // Prevent clicking on service options icon
    if (e.target.classList.contains('service-options') || 
        e.target.parentElement.classList.contains('service-options')) {
        return;
    }
    
    const card = this;
    const serviceName = card.querySelector('.service-title').textContent;
    toggleServiceSelection(serviceName, card);
}

// Add styles for selection mode
document.head.insertAdjacentHTML('beforeend', `
<style>
.selection-mode .service-card {
    cursor: pointer;
    position: relative;
}

.selection-mode .service-card::before {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border);
    border-radius: 4px;
    background-color: var(--surface);
}

.selection-mode .service-card.selected::before {
    content: '✓';
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
}

.multi-delete-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--critical);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 30px;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    z-index: 100;
    display: none;
    align-items: center;
    justify-content: center;
}

.multi-delete-button:hover {
    background-color: #d32f2f;
}

.selection-mode-button {
    background-color: var(--surface);
    border: 1px solid var(--border);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 10px;
}

.selection-mode-button:hover {
    background-color: var(--hover);
}
</style>
`);