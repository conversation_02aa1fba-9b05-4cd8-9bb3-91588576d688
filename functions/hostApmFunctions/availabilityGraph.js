// availabilityGraph.js - draws host availability donut graph above search bar
// Author: AI assistant

(function() {
    // Utility to format seconds into human readable string
    function formatDuration(sec) {
        const h = Math.floor(sec / 3600);
        const m = Math.floor((sec % 3600) / 60);
        const s = sec % 60;
        return `${h}h ${m}m ${s}s`;
    }

    // Get CSS variable value helper
    function getCssVar(name) {
        const style = getComputedStyle(document.documentElement);
        return style.getPropertyValue(name) || '#ccc';
    }

    // Initialise after DOM ready
    document.addEventListener('DOMContentLoaded', () => {
        const container = document.getElementById('availability-container');
        if (!container) return;

        const startInput = document.getElementById('availability-start');
        const endInput = document.getElementById('availability-end');
        const refreshBtn = document.getElementById('availability-refresh');
        const chartDiv = document.getElementById('availability-chart');

        const toLocalIso = (dateObj) => {
            // Convert to ISO string in local timezone without seconds
            const tzOffset = dateObj.getTimezoneOffset() * 60000; // in ms
            return new Date(dateObj.getTime() - tzOffset).toISOString().slice(0, 16);
        };

        // Fetch server current time from Nagios programstatus API
        const initWithServerTime = () => {
            const progUrl = `/nagios/cgi-bin/statusjson.cgi?query=programstatus`;
            fetch(progUrl, { credentials: 'include' })
                .then(r => r.json())
                .then(js => {
                    const srvSec = js?.data?.programstatus?.current_time;
                    if (srvSec) {
                        const srvDate = new Date(srvSec * 1000);
                        endInput.value = toLocalIso(srvDate);
                        startInput.value = toLocalIso(new Date(srvDate.getTime() - 24*60*60*1000));
                    } else {
                        // Fallback to client time
                        const now = new Date();
                        endInput.value = toLocalIso(now);
                        startInput.value = toLocalIso(new Date(now.getTime() - 24 * 60 * 60 * 1000));
                    }
                })
                .catch(() => {
                    const now = new Date();
                    endInput.value = toLocalIso(now);
                    startInput.value = toLocalIso(new Date(now.getTime() - 24 * 60 * 60 * 1000));
                })
                .finally(() => {
                    // After dates set, if host already known render
                    if (currentHost) fetchAndRender();
                });
        };

        initWithServerTime();

        let currentHost = null;

        function fetchAndRender() {
            if (!currentHost) return;

            const startTs = Math.floor(new Date(startInput.value).getTime() / 1000);
            const endTs = Math.floor(new Date(endInput.value).getTime() / 1000);
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
                console.warn('Invalid time range for availability graph.');
                return;
            }

            setTitle(startTs, endTs);

            chartDiv.innerHTML = '<div style="text-align:center;padding:20px;"><i class="fa fa-spinner fa-spin"></i></div>';

            const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&hostname=${encodeURIComponent(currentHost)}&starttime=${startTs}&endtime=${endTs}`;

            fetch(url, { credentials: 'include' })
                .then(r => r.json())
                .then(data => {
                    if (!data || !data.data || !data.data.host) {
                        chartDiv.innerHTML = '<div style="text-align:center;color:var(--critical);">Error loading availability</div>';
                        return;
                    }
                    drawChart(data.data.host);
                })
                .catch(err => {
                    console.error('Availability fetch error', err);
                    chartDiv.innerHTML = '<div style="text-align:center;color:var(--critical);">Error loading availability</div>';
                });
        }

        function drawChart(hostData) {
            // Clear previous
            chartDiv.innerHTML = '';

            // Build flex wrapper
            const wrapper = document.createElement('div');
            wrapper.className = 'availability-flex';
            // Give the wrapper a fixed id so we can toggle it when switching tabs
            wrapper.id = 'availability-summary';
            chartDiv.appendChild(wrapper);

            const donutContainer = document.createElement('div');
            donutContainer.className = 'availability-donut';
            wrapper.appendChild(donutContainer);

            const legendContainer = document.createElement('div');
            legendContainer.className = 'availability-legend';
            wrapper.appendChild(legendContainer);

            // sum real and scheduled times so we show realistic availability
            const up = (+hostData.time_up || 0) + (+hostData.scheduled_time_up || 0);
            const down = (+hostData.time_down || 0) + (+hostData.scheduled_time_down || 0);
            const unreach = (+hostData.time_unreachable || 0) + (+hostData.scheduled_time_unreachable || 0);
            const total = up + down + unreach;

            const dataset = [
                { label: 'Up', value: up, color: getCssVar('--success') },
                { label: 'Down', value: down, color: getCssVar('--critical') },
                { label: 'Unreachable', value: unreach, color: getCssVar('--unknown') }
            ].filter(d => d.value > 0);

            if (total === 0 || dataset.length === 0) {
                chartDiv.innerHTML = '<div style="text-align:center;color:var(--text);">No availability data for selected range</div>';
                return;
            }

            // Build donut chart with D3
            const size = 140;
            const radius = size / 2;
            const innerRadius = radius * 0.55;

            const svg = d3.select(donutContainer)
                .append('svg')
                .attr('viewBox', `0 0 ${size} ${size}`)
                .attr('preserveAspectRatio', 'xMidYMid meet');

            const g = svg.append('g').attr('transform', `translate(${radius},${radius})`);

            const pie = d3.pie().value(d => d.value);
            const arc = d3.arc().innerRadius(innerRadius).outerRadius(radius);

            g.selectAll('path')
                .data(pie(dataset))
                .enter()
                .append('path')
                .attr('d', arc)
                .attr('fill', d => d.data.color)
                .append('title')
                .text(d => `${d.data.label}: ${formatDuration(d.data.value)} (${((d.data.value / total) * 100).toFixed(1)}%)`);

            // Legend (HTML for easier styling)
            dataset.forEach(d => {
                const item = document.createElement('div');
                item.className = 'availability-legend-item';

                const box = document.createElement('span');
                box.className = 'availability-legend-box';
                box.style.background = d.color;

                const text = document.createElement('span');
                text.textContent = `${d.label} (${((d.value / total) * 100).toFixed(1)}%)`;

                item.appendChild(box);
                item.appendChild(text);
                legendContainer.appendChild(item);
            });
        }

        function setTitle(startSec, endSec) {
            const ttl = document.getElementById('availability-title');
            if (!ttl) return;
            const diffMs = (endSec - startSec) * 1000;
            const diffH = diffMs / 3600000;
            const diffD = diffH / 24;
            if (Math.abs(diffH - 24) < 0.1) {
                ttl.textContent = 'Availability (last 24 h)';
            } else if (diffD >= 1 && Number.isInteger(diffD)) {
                ttl.textContent = `Availability (last ${diffD} d)`;
            } else if (diffH >= 1) {
                ttl.textContent = `Availability (last ${diffH.toFixed(0)} h)`;
            } else {
                ttl.textContent = 'Availability';
            }
        }

        refreshBtn.addEventListener('click', fetchAndRender);

        // ---------------- NEW: simple helper to map host state to theme colour -----------------
        function getStateColor(state) {
            switch (state) {
                case 'up':
                    return getCssVar('--success');
                case 'down':
                    return getCssVar('--critical');
                case 'unreachable':
                    return getCssVar('--unknown');
                default:
                    return getCssVar('--pending');
            }
        }

        // ---------------- NEW: draw timeline of state changes -----------------
        function drawTimeline(changes, startSec, endSec) {
            const container = document.getElementById('availability-trends');
            if (!container) return;

            container.innerHTML = '';

            if (!changes || changes.length === 0) {
                container.innerHTML = '<div style="text-align:center;color:var(--text);">No state changes for selected range</div>';
                return;
            }

            const width = container.clientWidth || 400;
            const height = 60;

            const svg = d3.select(container)
                .append('svg')
                .attr('viewBox', `0 0 ${width} ${height}`)
                .attr('preserveAspectRatio', 'xMidYMid meet');

            const x = d3.scaleTime()
                .domain([new Date(startSec * 1000), new Date(endSec * 1000)])
                .range([0, width]);

            for (let i = 0; i < changes.length; i++) {
                const cur = changes[i];
                const segStart = new Date(cur.timestamp);
                const segEnd = (i < changes.length - 1) ? new Date(changes[i + 1].timestamp) : new Date(endSec * 1000);

                // Skip segments fully outside range
                if (segEnd < new Date(startSec * 1000) || segStart > new Date(endSec * 1000)) continue;

                const xStart = x(segStart);
                const xEnd = x(segEnd);
                const segWidth = Math.max(xEnd - xStart, 1);

                svg.append('rect')
                    .attr('x', xStart)
                    .attr('y', 10)
                    .attr('width', segWidth)
                    .attr('height', 20)
                    .attr('fill', getStateColor(cur.state))
                    .append('title')
                    .text(`${cur.state.toUpperCase()} - ${new Date(cur.timestamp).toLocaleString()}`);
            }

            const axis = d3.axisBottom(x).ticks(5);
            svg.append('g')
                .attr('transform', `translate(0,35)`)
                .call(axis)
                .selectAll('text')
                .attr('font-size', '10px')
                .attr('fill', getCssVar('--text'));
        }

        // --------------- NEW: fetch state change list ----------------
        function fetchTimelineData(startSec, endSec) {
            const url = `/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=host&hostname=${encodeURIComponent(currentHost)}&starttime=${startSec}&endtime=${endSec}`;
            return fetch(url, { credentials: 'include' })
                .then(r => r.json())
                .then(js => js?.data?.statechangelist || [])
                .catch(err => {
                    console.error('Timeline fetch error', err);
                    return [];
                });
        }

        // Listen for hostLoaded event dispatched from hostApm.js
        document.addEventListener('hostLoaded', e => {
            currentHost = e.detail.hostname;
            // if date inputs already filled render, else will render after initWithServerTime
            if (startInput.value && endInput.value) fetchAndRender();
        });

        // Inject simple tab styles once
        if (!document.getElementById('availability-style')) {
            const styleEl = document.createElement('style');
            styleEl.id = 'availability-style';
            styleEl.textContent = `
                .availability-tabs { margin-bottom: 8px; }
                .availability-tab { background: var(--surface); border: 1px solid var(--border); color: var(--text); padding: 4px 10px; cursor: pointer; font-size: 12px; margin-right: 4px; border-radius: 4px; transition: all 0.2s ease; }
                .availability-tab:hover { background: var(--hover, #444); }
                .availability-tab.active { background: var(--primary); color: var(--surface); font-weight: 600; }
            `;
            document.head.appendChild(styleEl);
        }

        // Whenever user changes dates, refresh automatically
        [startInput, endInput].forEach(el => el.addEventListener('change', fetchAndRender));

        // ----------------- MODIFY fetchAndRender to build tabs and trends -----------------
        function fetchAndRender() {
            if (!currentHost) return;

            const startTs = Math.floor(new Date(startInput.value).getTime() / 1000);
            const endTs = Math.floor(new Date(endInput.value).getTime() / 1000);
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
                console.warn('Invalid time range for availability graph.');
                return;
            }

            setTitle(startTs, endTs);

            chartDiv.innerHTML = '<div style="text-align:center;padding:20px;"><i class="fa fa-spinner fa-spin"></i></div>';

            const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&hostname=${encodeURIComponent(currentHost)}&starttime=${startTs}&endtime=${endTs}`;

            fetch(url, { credentials: 'include' })
                .then(r => r.json())
                .then(data => {
                    if (!data || !data.data || !data.data.host) {
                        chartDiv.innerHTML = '<div style="text-align:center;color:var(--critical);">Error loading availability</div>';
                        return;
                    }

                    // Draw summary donut first
                    drawChart(data.data.host);

                    // Build tab navigation and trends container
                    const nav = document.createElement('div');
                    nav.className = 'availability-tabs';
                    nav.innerHTML = `
                        <button class="availability-tab active" data-tab="summary">Availability</button>
                        <button class="availability-tab" data-tab="trends">Trends</button>`;

                    // Insert nav at top
                    chartDiv.prepend(nav);

                    // Create trends container (below summary wrapper)
                    let trendsDiv = document.getElementById('availability-trends');
                    if (!trendsDiv) {
                        trendsDiv = document.createElement('div');
                        trendsDiv.id = 'availability-trends';
                        trendsDiv.style.display = 'none';
                        chartDiv.appendChild(trendsDiv);
                    } else {
                        trendsDiv.innerHTML = '';
                    }

                    // Tab switching logic
                    const summaryWrapper = document.getElementById('availability-summary');
                    nav.querySelectorAll('.availability-tab').forEach(btn => {
                        btn.addEventListener('click', () => {
                            nav.querySelectorAll('.availability-tab').forEach(b => b.classList.remove('active'));
                            btn.classList.add('active');
                            const tab = btn.getAttribute('data-tab');
                            if (tab === 'summary') {
                                summaryWrapper.style.display = 'flex';
                                trendsDiv.style.display = 'none';
                            } else {
                                summaryWrapper.style.display = 'none';
                                trendsDiv.style.display = 'block';
                            }
                        });
                    });

                    // Fetch timeline and draw inside trends container
                    fetchTimelineData(startTs, endTs).then(changes => {
                        drawTimeline(changes, startTs, endTs);
                    });
                })
                .catch(err => {
                    console.error('Availability fetch error', err);
                    chartDiv.innerHTML = '<div style="text-align:center;color:var(--critical);">Error loading availability</div>';
                });
        }

        // override the earlier definition of fetchAndRender by hoisting the new one above listeners
        // (the old definition was above but this new one replaces it)
    });
})(); 