/**
 * Service Search Functionality
 * Handles filtering of service cards based on user input and status
 */

document.addEventListener('DOMContentLoaded', () => {
    // Get the search elements
    const searchInput = document.getElementById('service-search');
    const clearSearchBtn = document.getElementById('clear-search');
    const searchContainer = document.querySelector('.search-container');
    let filterButtons = [];
    let activeStatusFilter = 'all';
    
    // Initialize search only after services are loaded
    const initSearchFunctionality = () => {
        // First check if services exist
        const serviceCards = document.querySelectorAll('.service-card');
        
        // If no services, hide search container and retry later
        if (serviceCards.length === 0) {
            searchContainer.classList.remove('visible');
            setTimeout(initSearchFunctionality, 500);
            return;
        }
        
        // Services exist, show search container
        searchContainer.classList.add('visible');
        
        // Add event listeners for search
        searchInput.addEventListener('input', applyAllFilters);
        clearSearchBtn.addEventListener('click', clearSearch);
        
        // Add event listeners for status filter buttons
        filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(button => {
            // Store original label in dataset for future use
            if (!button.dataset.label) {
                button.dataset.label = button.textContent.trim();
            }
            button.addEventListener('click', () => {
                const status = button.getAttribute('data-status');
                setActiveStatusFilter(status);
                applyAllFilters();
            });
        });
        
        // Initialize with "All" as active
        setActiveStatusFilter('all');
        
        // Show/hide clear button based on search input
        searchInput.addEventListener('input', toggleClearButton);
        
        // ----- NEW: compute initial counts and visibility ----- //
        applyAllFilters();
        // ----------------------------------------------------- //
    };
    
    // Initialize after a delay to ensure services are loaded
    setTimeout(initSearchFunctionality, 500);
    
    // Also watch for DOM changes to handle dynamic service loading
    watchForServiceChanges();
    
    /**
     * Watch for changes in the service container to handle dynamically loaded services
     */
    function watchForServiceChanges() {
        const statusContainer = document.getElementById('status');
        if (!statusContainer) return;
        
        // Create a MutationObserver to watch for changes
        const observer = new MutationObserver(mutations => {
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    // Check if services exist after DOM changes
                    const serviceCards = document.querySelectorAll('.service-card');
                    if (serviceCards.length > 0) {
                        // Services exist, show search container if not already visible
                        if (!searchContainer.classList.contains('visible')) {
                            searchContainer.classList.add('visible');
                            // Re-initialize search functionality
                            setTimeout(initSearchFunctionality, 100);
                        }
                    } else {
                        // No services, hide search container
                        searchContainer.classList.remove('visible');
                    }
                }
            }
        });
        
        // Start observing with configuration
        observer.observe(statusContainer, { childList: true, subtree: true });
    }
    
    /**
     * Set active status filter and update button UI
     * @param {string} status - The status to set active
     */
    function setActiveStatusFilter(status) {
        activeStatusFilter = status;
        
        // Update button UI
        filterButtons.forEach(button => {
            if (button.getAttribute('data-status') === status) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    }
    
    /**
     * Apply both text search and status filters
     */
    function applyAllFilters() {
        const searchTerm = searchInput.value.trim().toLowerCase();
        const serviceCards = document.querySelectorAll('.service-card');
        const listRows = document.querySelectorAll('.services-table tbody tr');
        let visibleCount = 0;
        
        // ----- NEW: prepare count map for statuses among cards that match current search term ----- //
        const statusCounters = {
            all: 0,
            ok: 0,
            warning: 0,
            critical: 0,
            unknown: 0,
            pending: 0
        };
        // ------------------------------------------------------------------------------------------ //
        
        serviceCards.forEach((card, idx) => {
            // Apply both filters - first check search text
            const isMatchingSearch = matchesSearchTerm(card, searchTerm);
            
            // Increment counters for buttons if card matches search term (regardless of status filter)
            if (isMatchingSearch) {
                statusCounters.all++;
                ['ok','warning','critical','unknown','pending'].forEach(stat => {
                    if (card.classList.contains(stat)) {
                        statusCounters[stat]++;
                    }
                });
            }
            
            // Then check status
            const isMatchingStatus = activeStatusFilter === 'all' || matchesStatusFilter(card, activeStatusFilter);
            
            // Show card only if it passes both filters
            if (isMatchingSearch && isMatchingStatus) {
                card.classList.remove('filtered');
                visibleCount++;
            } else {
                card.classList.add('filtered');
            }
        });
        
        // Apply same visibility to list rows if present
        if (listRows.length) {
            listRows.forEach(row => {
                // Identify service name cell text for search matching
                const nameCell = row.querySelector('.svc-name-cell');
                const statusCell = row.querySelector('.status-cell');
                const titleText = nameCell ? nameCell.textContent.toLowerCase() : '';
                const statusText = statusCell ? statusCell.textContent.toLowerCase() : '';

                const isMatchingSearch = searchTerm === '' || titleText.includes(searchTerm) || statusText.includes(searchTerm);

                const statusClassList = ['ok','warning','critical','unknown','pending'];
                let rowStatusClass = statusClassList.find(cls => row.classList.contains(`service-row-${cls}`));
                if (!rowStatusClass) rowStatusClass = statusClassList.find(cls => row.classList.contains(cls));

                const isMatchingStatus = activeStatusFilter === 'all' || rowStatusClass === activeStatusFilter;

                if (isMatchingSearch && isMatchingStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // ----- NEW: Update button labels or hide if zero ----- //
        let activeStillVisible = true;
        filterButtons.forEach(button => {
            const statusKey = button.getAttribute('data-status');
            // 'all' filter should always remain visible
            if (statusKey !== 'all') {
                const count = statusCounters[statusKey];
                if (count === 0) {
                    button.style.display = 'none';
                    if (activeStatusFilter === statusKey) {
                        activeStillVisible = false;
                    }
                } else {
                    button.style.display = '';
                }
            }
            // Update label and badge for visible buttons (including 'all')
            if (button.style.display !== 'none') {
                const label = button.dataset.label || statusKey;
                const count = statusCounters[statusKey];
                if (statusKey === 'all') {
                    // For "All" filter, just display the label without the count badge
                    button.innerHTML = label;
                } else {
                    button.innerHTML = `${label}<span class="count-badge">${count}</span>`;
                }
            }
        });
        // If the previously active filter disappeared, reset to 'all'
        if (!activeStillVisible) {
            setActiveStatusFilter('all');
        }
        // -------------------------------------------------- //
        
        // Update service count display
        updateServiceCount(visibleCount, serviceCards.length);
    }
    
    // Make the filter function accessible to other modules (e.g., viewToggle.js)
    window.applyAllFilters = applyAllFilters;
    
    /**
     * Check if a card matches the search term
     * @param {Element} card - The service card element
     * @param {string} searchTerm - The search term to match
     * @returns {boolean} - Whether the card matches the search term
     */
    function matchesSearchTerm(card, searchTerm) {
        if (!searchTerm) return true; // Empty search matches everything
        
        const serviceTitle = card.querySelector('.service-title');
        if (!serviceTitle) return false;
        
        const titleText = serviceTitle.textContent.toLowerCase();
        const statusInfo = card.querySelector('.service-details');
        const statusText = statusInfo ? statusInfo.textContent.toLowerCase() : '';
        
        // Check if card contains the search term in title or status
        return titleText.includes(searchTerm) || statusText.includes(searchTerm);
    }
    
    /**
     * Check if a card matches the active status filter
     * @param {Element} card - The service card element
     * @param {string} statusFilter - The status to filter by
     * @returns {boolean} - Whether the card matches the status filter
     */
    function matchesStatusFilter(card, statusFilter) {
        if (statusFilter === 'all') return true;
        
        // Check card classes to determine status
        return card.classList.contains(statusFilter);
    }
    
    /**
     * Clear the search input and reset to show all service cards
     * but maintain status filter
     */
    function clearSearch() {
        searchInput.value = '';
        
        // Apply only status filter since search is cleared
        applyAllFilters();
        
        // Hide clear button
        clearSearchBtn.classList.remove('visible');
        
        // Focus on search input
        searchInput.focus();
    }
    
    /**
     * Toggle visibility of clear button based on search input
     */
    function toggleClearButton() {
        if (searchInput.value.trim() !== '') {
            clearSearchBtn.classList.add('visible');
        } else {
            clearSearchBtn.classList.remove('visible');
        }
    }
    
    /**
     * Update service count display
     * @param {number} visibleCount - Number of visible services
     * @param {number} totalCount - Total number of services
     */
    function updateServiceCount(visibleCount, totalCount) {
        const serviceCountElement = document.getElementById('service-count');
        if (serviceCountElement) {
            if (visibleCount < totalCount) {
                serviceCountElement.innerHTML = `<i class="fa fa-server" aria-hidden="true"></i> ${visibleCount} / ${totalCount} Services`;
            } else {
                serviceCountElement.innerHTML = `<i class="fa fa-server" aria-hidden="true"></i> ${totalCount} Services`;
            }
        }
    }
}); 