(function(){
    // Inject Export PDF button once DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        const controls = document.querySelector('#availability-container .availability-controls');
        if (controls && !document.getElementById('availability-export')) {
            const btn = document.createElement('button');
            btn.id = 'availability-export';
            btn.className = 'availability-export';
            btn.title = 'Export availability & trends as PDF';
            btn.innerHTML = '<i class="fa fa-file-pdf-o" aria-hidden="true"></i>';
            controls.appendChild(btn);

            btn.addEventListener('click', () => {
                exportPdf(btn);
            });
        }
    });

    // Keep track of current host name (populated from hostLoaded event)
    let currentHost = null;
    document.addEventListener('hostLoaded', e => {
        currentHost = e.detail.hostname;
    });

    // ---------------- PHP-based PDF generation -----------------
    async function exportPdf(buttonEl){
        try {
            if (!currentHost){
                alert('Please load a host first');
                return;
            }

            const startInput = document.getElementById('availability-start');
            const endInput   = document.getElementById('availability-end');
            const startTs = Math.floor(new Date(startInput.value).getTime()/1000);
            const endTs   = Math.floor(new Date(endInput.value).getTime()/1000);
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs){
                alert('Invalid date range');
                return;
            }

            // Disable button + spinner
            buttonEl.disabled = true;
            buttonEl.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

            // Get host IP from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const hostIp = urlParams.get('hostip') || urlParams.get('ip') || '';

            // Prepare form data for PHP request
            const formData = new FormData();
            formData.append('hostname', currentHost);
            formData.append('startTs', startTs);
            formData.append('endTs', endTs);
            formData.append('hostIp', hostIp);

            // Make POST request to PHP report generator
            const response = await fetch('functions/hostApmFunctions/generateAvailabilityReport.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // Trigger download using the download URL
                const downloadLink = document.createElement('a');
                downloadLink.href = result.download_url;
                downloadLink.download = result.filename;
                downloadLink.style.display = 'none';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                
                // Show success message
                console.log('PDF report generated successfully:', result.filename);
            } else {
                alert('Error generating PDF: ' + result.message);
            }

        } catch (err){
            console.error('PDF export error', err);
            alert('Error generating PDF: ' + err.message);
        } finally {
            if (buttonEl){
                buttonEl.disabled = false;
                buttonEl.innerHTML = '<i class="fa fa-file-pdf-o" aria-hidden="true"></i>';
            }
        }
    }
})();
