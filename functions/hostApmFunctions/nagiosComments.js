function fetchNagiosComments(hostname, divId, servicedescription = "none") {
    // API base URL
    const baseUrl = `https://${window.location.hostname}/nagios/cgi-bin/statusjson.cgi`;

    // Construct the full URL with parameters
    let url;
    if (servicedescription === "none") {
        url = `${baseUrl}?query=commentlist&details=true&hostname=${encodeURIComponent(hostname)}`;
    } else {
        url = `${baseUrl}?query=commentlist&details=true&hostname=${encodeURIComponent(hostname)}&servicedescription=${encodeURIComponent(servicedescription)}`;
    }

    // Create options for the fetch request
    const options = {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            // Add any required authentication headers here
        }
    };

    // Fetch data from the API
    fetch(url, options)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            displayComments(data, divId, hostname, servicedescription);
        })
        .catch(error => {
            console.error('Error fetching comments:', error);
            const commentsDiv = document.getElementById(divId);
            commentsDiv.innerHTML += `<p class="error">Error fetching comments: ${error.message}</p>`;
        });
}

function displayComments(data, divId, hostname, servicedescription) {
    const commentsDiv = document.getElementById(divId);

    // If no comments or invalid data, append a message below existing content
    if (!data.data || !data.data.commentlist || Object.keys(data.data.commentlist).length === 0) {
        commentsDiv.innerHTML += '<p>No comments found for this host' + (servicedescription !== "none" ? ` and service: ${servicedescription}` : '') + '.</p>';
        return;
    }

    // Create the comments table
    const table = document.createElement('table');
    table.className = 'comments-table';

    // Create table header
    const thead = document.createElement('thead');
    thead.innerHTML = `
    <tr>
        <th>Entry Time</th>
        <th>Author</th>
        <th>Comment</th>
        <th>Comment ID</th>
        <th>Persistent</th>
        <th>Type</th>
        <th>Expires</th>
        <th>Actions</th>
    </tr>
`;
    table.appendChild(thead);

    // Create table body
    const tbody = document.createElement('tbody');

    // Process each comment and add it to the table
    let commentCount = 0;
    for (const commentId in data.data.commentlist) {
        const comment = data.data.commentlist[commentId];

        // Filter comments based on servicedescription
        if (servicedescription === "none" && comment.comment_type !== 1) continue; // Show only Host comments (type 1)
        if (servicedescription !== "none" && comment.comment_type !== 2) continue; // Show only Service comments (type 2)

        const row = document.createElement('tr');

        // Format the entry time
        const entryDate = new Date(comment.entry_time);
        const formattedDate = entryDate.toLocaleString();

        // Determine entry type text (for the Type column)
        let typeText = "Unknown";
        switch (comment.entry_type) {
            case 1:
                typeText = "User";
                break;
            case 2:
                typeText = "Downtime";
                break;
            case 3:
                typeText = "Flapping";
                break;
            case 4:
                typeText = "Acknowledgement";
                break;
        }

        // Determine cmd_typ based on servicedescription
        const cmdTyp = servicedescription !== "none" ? 4 : 2;

        // Create row content
        row.innerHTML = `
        <td data-label="Entry Time">${formattedDate}</td>
        <td data-label="Author">${comment.author}</td>
        <td data-label="Comment">${comment.comment_data}</td>
        <td data-label="Comment ID">${comment.comment_id}</td>
        <td data-label="Persistent">${comment.persistent ? 'Yes' : 'No'}</td>
        <td data-label="Type">${typeText}</td>
        <td data-label="Expires">${comment.expires ? 'Yes' : 'No'}</td>
        <td data-label="Actions">
            <a href="#" onclick="openCommandModal('https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi?cmd_typ=${cmdTyp}&com_id=${comment.comment_id}', 'Delete Comment'); return false;">
                <i class="fa fa-trash-o fa-lg" title="Delete This Comment"></i>
            </a>
        </td>
    `;

        tbody.appendChild(row);
        commentCount++;
    }

    // If no comments matched the filter, show a message
    if (commentCount === 0) {
        commentsDiv.innerHTML += '<p>No ' + (servicedescription === "none" ? 'host' : 'service') + ' comments found.</p>';
        return;
    }

    table.appendChild(tbody);

    // Append the table below the existing command buttons
    commentsDiv.appendChild(table);

    // Add a summary of total comments
    const summary = document.createElement('p');
    summary.textContent = `Total comments: ${commentCount}`;
    commentsDiv.appendChild(summary);
}