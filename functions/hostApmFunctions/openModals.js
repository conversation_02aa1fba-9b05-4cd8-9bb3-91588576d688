function openModalWithIframe(url, title) {
    const modal = document.getElementById("service-modal");
    const modalTitle = document.getElementById("modal-title");
    const modalBody = document.getElementById("modal-body");
    const modalClose = document.getElementById("modal-close");
    document.body.style.overflow = "hidden";
    document.getElementById('modal-options').style.display = 'none';
    document.getElementById('modal-delete').style.display = 'none';
    const modalRefreshIcon = document.getElementById('modal-refresh');
    if (modalRefreshIcon) modalRefreshIcon.style.display = 'none';

    // Add loading animation
    modalBody.innerHTML = `
        <div id="loading-animation" style="display: flex; justify-content: center; align-items: center; height: 400px;">
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite;"></div>
        </div>
        <iframe id="modal-iframe" src="${url}" width="100%" height="400px" style="border: none; display: none;"></iframe>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
    
    modal.style.display = "flex";
    modalTitle.innerHTML = title;

    const iframe = document.getElementById("modal-iframe");

    iframe.onload = () => {
        try {
            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
            const navElement = iframeDocument.querySelector("nav");
            if (navElement) navElement.style.display = "none";

            // Handle form submissions
            const forms = iframeDocument.getElementsByTagName('form');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', (e) => {
                    setTimeout(() => {
                        // Close the modal and reload only the immediate parent window 
                        // Removed the reload as it was not needed anymore
                        const modalElement = modal.closest('.modal') || modal;
                        modalElement.style.display = 'none';
                        //modalElement.innerHTML = '';
                        document.body.style.overflow = 'auto';
                        //window.location.reload();
                        // In hostlist.php, we need to refresh the table and update the feature status indicators
                        if (typeof populateHostListPage === 'function') {
                            Promise.all([
                                populateHostListPage(),
                                updateFeatureStatusIndicators() // Consolidated update
                            ]);
                        }
                    }, 500);
                });
            });

            // Get parent document's body styles
            const parentStyles = window.getComputedStyle(document.body);
            const parentBackgroundColor = parentStyles.backgroundColor;
            const parentColor = parentStyles.color;

            // Get current theme from PHP
            getCurrentTheme().then(theme => {
                // Inject custom CSS to reorder and style the layout based on theme
                const style = iframeDocument.createElement('style');
                style.textContent = theme === 'light-theme' ? 
                    getLightThemeStyles() : getDarkThemeStyles();
                
                iframeDocument.head.appendChild(style);

                // Hide the specific alert div
                const alertElement = iframeDocument.querySelector('.alert.alert-warning.alert-dismissible');
                if (alertElement) {
                    alertElement.dataset.originalDisplay = alertElement.style.display || 'block';
                    alertElement.style.display = 'none';
                }

                const commitButton = iframeDocument.querySelector('input[name="btnSubmit"][value="Commit"]');
                if (commitButton) {
                    commitButton.addEventListener("click", () => {
                        setTimeout(() => {
                            iframe.src = "";
                            modal.style.display = "none";
                            document.body.style.overflow = "auto";
                        }, 100);
                    });
                }
                
                // Hide loading animation and show iframe after styles are applied
                document.getElementById('loading-animation').style.display = 'none';
                iframe.style.display = 'block';
            });
        } catch (error) {
            console.warn("Cannot access iframe content due to cross-origin restrictions:", error);
            // If there's an error, still hide loading and show iframe
            document.getElementById('loading-animation').style.display = 'none';
            iframe.style.display = 'block';
        }
    };

    modalClose.onclick = () => {
        modal.style.display = "none";
        modalBody.innerHTML = "";
        document.body.style.overflow = "auto";
    };

    window.onclick = (event) => {
        if (event.target === modal) {
            modal.style.display = "none";
            modalBody.innerHTML = "";
            document.body.style.overflow = "auto";
        }
    };
}

function openCommandModal(url, title, triggerElement = null) {
    // Remove any existing command modals first
    const existingCommandModals = document.querySelectorAll('.modal.command-modal');
    existingCommandModals.forEach(modal => modal.remove());

    // Create modal elements
    const modal = document.createElement('div');
    modal.className = 'modal command-modal'; // Add command-modal class for identification
    modal.style.display = 'flex';

    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';

    const modalHeader = document.createElement('div');
    modalHeader.className = 'modal-header';
    modalHeader.innerHTML = `
    <h2 class="modal-title">${title}</h2>
    <span class="modal-close" onclick="this.closest('.modal').remove()">×</span>
`;

    const modalBody = document.createElement('div');
    modalBody.className = 'modal-body';

    // Generate unique ID for this modal's loading animation
    const uniqueId = 'command-loading-animation-' + Date.now();
    
    // Add loading animation
    modalBody.innerHTML = `
        <div id="${uniqueId}" style="display: flex; justify-content: center; align-items: center; height: 400px;">
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite;"></div>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;

    const iframe = document.createElement('iframe');
    iframe.src = url;
    iframe.style.width = '100%';
    iframe.style.height = '400px';
    iframe.style.border = 'none';
    iframe.style.display = 'none'; // Hide iframe initially
    iframe.sandbox = 'allow-forms allow-scripts allow-same-origin';

    // Add iframe to modal body
    modalBody.appendChild(iframe);

    // When iframe loads, apply custom styles and behavior
    iframe.onload = () => {
        try {
            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
            const navElement = iframeDocument.querySelector("nav");
            if (navElement) navElement.style.display = "none";

            // Handle form submissions
            const forms = iframeDocument.getElementsByTagName('form');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', (e) => {
                    setTimeout(() => {
                        // Remove the command modal completely instead of just hiding it
                        modal.remove();
                        document.body.style.overflow = 'auto';

                        // Show processing animation next to the clicked status label if provided
                        if (triggerElement) {
                            showStatusUpdateAnimation(triggerElement);
                        }
                        
                        // In hostlist.php, we need to refresh the table and update the feature status indicators
                        if (typeof populateHostListPage === 'function') {
                            Promise.all([
                                populateHostListPage(),
                                updateFeatureStatusIndicators() // Consolidated update
                            ]);
                        }
                    }, 500);
                });
            });

            // Get current theme from PHP
            getCurrentTheme().then(theme => {
                // Inject custom CSS based on theme
                const style = iframeDocument.createElement('style');
                style.textContent = theme === 'light-theme' ? 
                    getLightThemeStyles() : getDarkThemeStyles();
                    
                iframeDocument.head.appendChild(style);

                // Hide the specific alert div
                const alertElement = iframeDocument.querySelector('.alert.alert-warning.alert-dismissible');
                if (alertElement) {
                    alertElement.dataset.originalDisplay = alertElement.style.display || 'block';
                    alertElement.style.display = 'none';
                }

                const commitButton = iframeDocument.querySelector('input[name="btnSubmit"][value="Commit"]');
                if (commitButton) {
                    commitButton.addEventListener("click", () => {
                        setTimeout(() => {
                            iframe.src = "";
                            modal.remove(); // Remove the modal completely
                            document.body.style.overflow = "auto";
                        }, 100);
                    });
                }
                
                // Hide loading animation and show iframe after styles are applied
                const loadingAnimation = document.getElementById(uniqueId);
                if (loadingAnimation) loadingAnimation.style.display = 'none';
                iframe.style.display = 'block';
            });
        } catch (error) {
            console.warn("Cannot access iframe content due to cross-origin restrictions:", error);
            // If there's an error, still hide loading and show iframe
            const loadingAnimation = document.getElementById(uniqueId);
            if (loadingAnimation) loadingAnimation.style.display = 'none';
            iframe.style.display = 'block';
        }
    };

    modalContent.appendChild(modalHeader);
    modalContent.appendChild(modalBody);
    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.remove();
    });
}

// Helper function to close current modal and open new one
function closeCurrentAndOpenModal(url, title) {
    const currentModal = document.getElementById('service-modal');
    const currentModalBody = document.getElementById('modal-body');
    currentModal.style.display = 'none'; // Close current modal
    currentModalBody.innerHTML = ''; // Clear current modal content
    document.body.style.overflow = 'auto'; // Reset overflow
    openModalWithIframe(url, title); // Open new modal
}

// Get current theme from PHP
async function getCurrentTheme() {
    try {
        // Check if there's a link element with href containing the theme name
        const themeLinks = document.querySelectorAll('link[rel="stylesheet"]');
        for (const link of themeLinks) {
            if (link.href.includes('/styles/')) {
                const themePath = link.href;
                // Extract theme name from path like "styles/light-theme/style.css"
                const themeMatch = themePath.match(/\/styles\/([^\/]+)\//);
                if (themeMatch && themeMatch[1]) {
                    return themeMatch[1];
                }
            }
        }
        // Fallback: check if body has a class indicating the theme
        const bodyClasses = document.body.className;
        if (bodyClasses.includes('light-theme')) return 'light-theme';
        if (bodyClasses.includes('dark-theme')) return 'dark-theme';
        
        return 'dark-theme';
    } catch (error) {
        console.error('Error detecting theme:', error);
        return 'dark-theme';
    }
}

// Light theme styles
function getLightThemeStyles() {
    return `
        body {
            font-family: 'Calibri', sans-serif;
            color: #333;
            background-color: #f4f4f4;
            line-height: 1.2;
            font-size: 13px;
            margin: 0;
            padding: 0;
        }
        
        table[width="90%"] {
            width: 100% !important;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 6px;
            border-radius: 3px;
        }
        
        table[width="90%"] > tbody > tr > td {
            display: block;
            width: 100% !important;
            padding: 6px;
        }
        
        /* Hide the Command Description section completely */
        td:has(.descriptionTitle) {
            display: none !important;
        }
        
        .commandDescription {
            background-color: #f4f4f4;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #B0B0B0;
            margin-bottom: 6px;
        }
        
        .jumbotron {
            margin: 0 0 8px 0;
            padding: 8px;
            border-radius: 4px;
        }
        
        .optBox {
            padding: 8px;
        }
        
        .optBoxItem {
            padding: 0px;    
        }
        
        .optBox input[type="text"].form-control {
            padding: 4px;
            margin: 4px 0;
            height: 28px;
        }
        
        input[type="submit"].btn-warning, 
        input[type="reset"].btn-warning,
        .btn.btn-warning {
            background-color: #f0f0f0 !important;
            color: #333;
            border: 1px solid #cbd5e1 !important;
            border-radius: 4px;
            padding: 4px 12px;
            min-width: 70px;
            height: 28px;
            margin: 6px 3px;
            font-size: 11px;
        }

        input[type="submit"].btn-warning:hover, 
        input[type="reset"].btn-warning:hover,
        .btn.btn-warning:hover {
            background-color: #e0e0e0 !important;
            transform: translateY(-1px);
        }
        .optBoxTitle, .descriptionTitle {
            margin-bottom: 6px;
            font-size: 14px;
            padding-bottom: 4px;
        }
        
        select {
            padding: 4px;
            height: 28px;
        }
        
        label {
            margin-bottom: 3px;
            font-size: 12px;
        }
    `;
}

// Dark theme styles (original)
function getDarkThemeStyles() {
    return `
        body {
            font-family: 'Calibri', sans-serif;
            color: #fff;
            background-color: #2f2f2f;
            line-height: 1.2;
            font-size: 13px;
            margin: 0;
            padding: 0;
        }
        
        table[width="90%"] {
            width: 100% !important;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 6px;
            border-radius: 3px;
        }
        
        table[width="90%"] > tbody > tr > td {
            display: block;
            width: 100% !important;
            padding: 6px;
        }
        
        /* Hide the Command Description section completely */
        td:has(.descriptionTitle) {
            display: none !important;
        }
        
        .commandDescription {
            background-color: #222;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #444;
            margin-bottom: 6px;
        }
        
        .jumbotron {
            background-color: #1f1f1f;
            margin: 0 0 8px 0;
            padding: 8px;
            border-radius: 4px;
        }
        
        .optBox {
            padding: 8px;
        }
        
        .optBoxItem {
            padding: 0px;
        }
        
        .optBox input[type="text"].form-control {
            padding: 4px;
            margin: 4px 0;
            height: 28px;
        }
        
        input[type="submit"].btn-warning, 
        input[type="reset"].btn-warning,
        .btn.btn-warning {
            background-color: #333 !important;
            color: #fff;
            border: 1px solid #888 !important;
            border-radius: 4px;
            padding: 4px 12px;
            min-width: 70px;
            height: 28px;
            margin: 6px 3px;
            font-size: 11px;
        }

        input[type="submit"].btn-warning:hover, 
        input[type="reset"].btn-warning:hover,
        .btn.btn-warning:hover {
            background-color: #444 !important;
            transform: translateY(-1px);
        }
        
        .optBoxTitle, .descriptionTitle {
            margin-bottom: 6px;
            font-size: 14px;
            padding-bottom: 4px;
        }
        
        select {
            padding: 4px;
            height: 28px;
        }
        
        label {
            margin-bottom: 3px;
            font-size: 12px;
        }
    `;
}

// Helper to display a temporary spinner/checkmark next to the status label
function showStatusUpdateAnimation(el) {
    if (!el || !el.parentNode) return;

    // Avoid duplicating spinner if one already exists
    if (el.parentNode.querySelector('.status-update-spinner')) return;

    const spinner = document.createElement('i');
    spinner.className = 'fa fa-refresh fa-spin status-update-spinner';
    spinner.style.marginLeft = '6px';
    spinner.style.color = 'var(--text-secondary)';

    // Insert spinner right after the element
    el.parentNode.insertBefore(spinner, el.nextSibling);

    // After 5 seconds, change spinner to success icon, then remove after 2 more seconds
    setTimeout(() => {
        // Switch to success icon
        spinner.className = 'fa fa-check-circle status-update-success';
        spinner.classList.remove('fa-spin');
        spinner.style.color = 'var(--success)';

        // Remove after additional 2 seconds
        setTimeout(() => {
            spinner.remove();
        }, 2000);
    }, 5000);
}