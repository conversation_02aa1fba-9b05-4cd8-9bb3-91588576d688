// Toggle between card (grid) view and list (table) view for services in hostApm page
// Author: added via AI assistant

(function () {
    document.addEventListener('DOMContentLoaded', () => {
        const toggleBtn = document.getElementById('view-toggle-button');
        if (!toggleBtn) return;

        let isListView = false;

        toggleBtn.addEventListener('click', () => {
            const servicesGrid = document.querySelector('.services-grid');
            const statusContainer = document.getElementById('status');
            if (!servicesGrid || !statusContainer) return;

            let listTable = document.getElementById('services-list-table');

            if (!isListView) {
                // Switch to list view
                if (!listTable) {
                    listTable = buildTableFromCards();
                    statusContainer.appendChild(listTable);
                    // ensure filters/search reflect new table
                    if (typeof applyAllFilters === 'function') {
                        setTimeout(() => applyAllFilters(), 50);
                    }
                }
                servicesGrid.style.display = 'none';
                listTable.style.display = 'table';
                toggleBtn.innerHTML = '<i class="fa fa-th-large"></i>';
                toggleBtn.title = 'Switch to card view';
                isListView = true;
            } else {
                // Switch back to card view
                servicesGrid.style.display = 'grid';
                if (listTable) listTable.style.display = 'none';
                toggleBtn.innerHTML = '<i class="fa fa-list"></i>';
                toggleBtn.title = 'Switch to list view';
                isListView = false;
                if (typeof applyAllFilters === 'function') {
                    setTimeout(() => applyAllFilters(), 50);
                }
            }
        });

        function buildTableFromCards() {
            const table = document.createElement('table');
            table.id = 'services-list-table';
            table.className = 'services-table';

            // inject simple styles once
            if (!document.getElementById('services-table-styles')) {
                const style = document.createElement('style');
                style.id = 'services-table-styles';
                style.textContent = `
                    .services-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 12px;
                        font-size: 14px;
                    }
                    .services-table th, .services-table td {
                        padding: 8px 10px;
                        border-bottom: 1px solid var(--border, #555);
                        text-align: left;
                    }
                    .services-table thead {
                        background: var(--surface);
                        color: var(--text);
                    }
                    .services-table thead th {
                        font-weight: 600;
                        border-bottom: 2px solid var(--border);
                    }
                    /* Hover row */
                    .services-table tbody tr:hover {
                        background: rgba(0,0,0,0.02);
                        cursor: pointer;
                    }
                    /* Highlight selected rows when in multiselect mode */
                    .services-table tbody tr.selected {
                        background: rgba(0, 123, 255, 0.15);
                        border-left: 4px solid var(--primary, #007bff);
                    }
                    .services-table .status-badge-table {
                        padding: 2px 8px;
                        border-radius: 4px;
                        font-weight: 600;
                        color: var(--surface);
                    }
                    .services-table .badge-ok { background: var(--success); }
                    .services-table .badge-warning { background: var(--warning); }
                    .services-table .badge-critical { background: var(--critical); }
                    .services-table .badge-unknown { background: var(--unknown); }
                    .services-table .badge-pending { background: var(--pending); }
                    .icons-cell i { color: var(--text-secondary); font-size: 14px; }
                `;
                document.head.appendChild(style);
            }

            // Table header
            const thead = document.createElement('thead');
            thead.innerHTML = '<tr><th>Service</th><th style="width:80px;"></th><th>Status</th><th>Last Check</th><th>Duration</th><th>Attempt</th><th>Info</th></tr>';
            table.appendChild(thead);

            const tbody = document.createElement('tbody');

            const hostname = new URLSearchParams(window.location.search).get('ip');

            // Iterate over existing service cards to populate the table
            const cardElements = document.querySelectorAll('.service-card');
            cardElements.forEach(card => {
                const serviceNameEl = card.querySelector('.service-title');
                const statusDetailEl = card.querySelector('.service-details');
                if (!serviceNameEl || !statusDetailEl) return;

                const serviceName = serviceNameEl.textContent;
                const encodedServiceName = serviceName.replace(/\s+/g, '+');

                // Prepare basic values we already have
                const statusTextMatch = statusDetailEl.textContent.match(/Status:\s*(.*)/i);
                const statusText = statusTextMatch ? statusTextMatch[1].trim() : '';
                const statusClass = ['ok', 'warning', 'critical', 'unknown', 'pending']
                    .find(cls => card.classList.contains(cls)) || '';

                // Build row with placeholders for async fields
                const row = document.createElement('tr');
                row.className = `service-row-${statusClass}`;
                row.dataset.service = encodedServiceName;
                row.dataset.serviceName = serviceName;

                // If this service is already in the selectedServices list, visually mark it
                if (typeof selectedServices !== 'undefined' && Array.isArray(selectedServices) && selectedServices.includes(serviceName)) {
                    row.classList.add('selected');
                }

                row.innerHTML = `
                    <td class="svc-name-cell">${serviceName}</td>
                    <td class="icons-cell">Loading...</td>
                    <td class="status-cell">Loading...</td>
                    <td class="last-check-cell">Loading...</td>
                    <td class="duration-cell">–</td>
                    <td class="attempt-cell">–</td>
                    <td class="info-cell">Loading...</td>
                `;

                // Same click / context behaviour
                row.addEventListener('click', () => {
                    card.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                });
                row.addEventListener('contextmenu', e => {
                    e.preventDefault();
                    card.dispatchEvent(new MouseEvent('contextmenu', {
                        bubbles: true,
                        clientX: e.clientX,
                        clientY: e.clientY
                    }));
                });

                tbody.appendChild(row);

                // Fetch extra details for the service from the global variable
                if (window.apmFullStatus && window.apmFullStatus.servicestatus) {
                    const serviceData = window.apmFullStatus.servicestatus.find(s => s.service_description === serviceName);
                    if (serviceData) {
                        const svc = mapServiceForModal(serviceData); // Use helper to get consistent data format
                        
                        // Update cells
                        row.querySelector('.last-check-cell').textContent = new Date(svc.last_check).toLocaleString();
                        row.querySelector('.duration-cell').textContent = typeof calculateDuration === 'function' ? calculateDuration(svc.last_state_change) : '–';
                        row.querySelector('.attempt-cell').textContent = `${svc.current_attempt} / ${svc.max_attempts}`;
                        row.querySelector('.info-cell').textContent = svc.plugin_output || '–';

                        // Clone icons from card (service-icon elements and graph icon)
                        let iconsHTML = '';
                        const iconEls = card.querySelectorAll('.service-icon-container i, .service-graphs-available');
                        iconEls.forEach(ic => {
                            const clone = ic.cloneNode(true);
                            // adjust class to fit table styling
                            clone.classList.remove('service-icon');
                            clone.style.marginRight = '4px';
                            iconsHTML += clone.outerHTML;
                        });

                        // Build status badge
                        const badgeHTML = `<span class="status-badge-table badge-${statusClass}">${statusText}</span>`;

                        row.innerHTML = `
                            <td class="svc-name-cell">${serviceName}</td>
                            <td class="icons-cell">${iconsHTML}</td>
                            <td class="status-cell">${badgeHTML}</td>
                            <td class="last-check-cell">${new Date(svc.last_check).toLocaleString()}</td>
                            <td class="duration-cell">${typeof calculateDuration === 'function' ? calculateDuration(svc.last_state_change) : '–'}</td>
                            <td class="attempt-cell">${svc.current_attempt} / ${svc.max_attempts}</td>
                            <td class="info-cell">${svc.plugin_output || '–'}</td>
                        `;
                    } else {
                        row.querySelector('.last-check-cell').textContent = 'N/A';
                        row.querySelector('.info-cell').textContent = 'N/A';
                    }
                } else {
                    row.querySelector('.last-check-cell').textContent = 'N/A';
                    row.querySelector('.info-cell').textContent = 'Data not available';
                }
            });

            table.appendChild(tbody);
            return table;
        }
    });
})(); 