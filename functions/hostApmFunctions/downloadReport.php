<?php
// downloadReport.php - handles downloading of generated PDF reports

if (!isset($_GET['file']) || empty($_GET['file'])) {
    http_response_code(400);
    echo 'File parameter is required';
    exit;
}

$filename = basename($_GET['file']); // Security: prevent directory traversal
$filepath = sys_get_temp_dir() . '/' . $filename;

// Validate file exists and is a PDF
if (!file_exists($filepath)) {
    http_response_code(404);
    echo 'File not found';
    exit;
}

if (!str_ends_with($filename, '.pdf')) {
    http_response_code(400);
    echo 'Invalid file type';
    exit;
}

// Set headers for PDF download
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($filepath));
header('Cache-Control: private, max-age=0, must-revalidate');
header('Pragma: public');

// Output file
readfile($filepath);

// Clean up temporary file after download
unlink($filepath);
exit;
?>
