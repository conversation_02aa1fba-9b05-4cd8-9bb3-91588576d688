function openRenameServiceModal(serviceName, hostIp) {
    const modal = document.getElementById('rename-service-modal');
    const currentNameInput = document.getElementById('current-service-name');
    const newNameInput = document.getElementById('new-service-name');
    
    // Set current service name
    currentNameInput.value = serviceName;
    newNameInput.value = '';
    
    // Store the service info for later use
    modal.dataset.serviceName = serviceName;
    modal.dataset.hostIp = hostIp;
    
    // Show modal
    modal.classList.add('show');
    
    // Focus on new name input
    setTimeout(() => {
        newNameInput.focus();
    }, 100);
}

function closeRenameServiceModal() {
    const modal = document.getElementById('rename-service-modal');
    modal.classList.remove('show');
    
    // Clear form
    document.getElementById('current-service-name').value = '';
    document.getElementById('new-service-name').value = '';
}

function renameService() {
    const modal = document.getElementById('rename-service-modal');
    const serviceName = modal.dataset.serviceName;
    const hostIp = modal.dataset.hostIp;
    const newServiceName = document.getElementById('new-service-name').value.trim();
    
    // Validation
    if (!newServiceName) {
        alert('Please enter a new service name');
        return;
    }
    
    if (newServiceName === serviceName) {
        alert('New service name must be different from current name');
        return;
    }
    
    if (newServiceName.length > 255) {
        alert('Service name is too long (maximum 255 characters)');
        return;
    }
    
    if (/[<>"']/.test(newServiceName)) {
        alert('Service name contains invalid characters');
        return;
    }
    
    // Show loading message
    const loadingMessage = document.createElement('div');
    loadingMessage.id = 'service-rename-loading';
    loadingMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 8px;
        z-index: 10001;
        text-align: center;
    `;
    loadingMessage.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 24px;"></i>
        </div>
        <div>Renaming service...</div>
    `;
    
    document.body.appendChild(loadingMessage);
    
    // Close modal
    closeRenameServiceModal();
    
    // Prepare form data
    const formData = new FormData();
    formData.append('ip', hostIp);
    formData.append('current_service_name', serviceName);
    formData.append('new_service_name', newServiceName);
    
    // Send request
    fetch('rename_service.php', {
        method: 'POST',
        body: formData,
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Remove loading message
        const loadingElement = document.getElementById('service-rename-loading');
        if (loadingElement) {
            loadingElement.remove();
        }
        
        if (data.success) {
            // Show success message
            const successDiv = document.createElement('div');
            successDiv.style.cssText = loadingMessage.style.cssText;
            successDiv.innerHTML = `
                <div style="margin-bottom: 10px;">
                    <i class="fa fa-check-circle" style="font-size: 24px; color: #4caf50;"></i>
                </div>
                <div>Service renamed successfully!</div>
                <div style="font-size: 12px; margin-top: 5px;">Refreshing...</div>
            `;
            document.body.appendChild(successDiv);
            
            setTimeout(() => {
                window.location.reload();
            }, 5000);
        } else {
            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = loadingMessage.style.cssText;
            errorDiv.innerHTML = `
                <div style="margin-bottom: 10px;">
                    <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #ff9800;"></i>
                </div>
                <div>Rename failed!</div>
                <div style="font-size: 12px; margin-top: 5px;">${data.error || 'Please try again'}</div>
            `;
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }
    })
    .catch(error => {
        // Remove loading message
        const loadingElement = document.getElementById('service-rename-loading');
        if (loadingElement) {
            loadingElement.remove();
        }
        
        console.error('Error renaming service:', error);
        
        // Show error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = loadingMessage.style.cssText;
        errorDiv.innerHTML = `
            <div style="margin-bottom: 10px;">
                <i class="fa fa-exclamation-triangle" style="font-size: 24px; color: #f44336;"></i>
            </div>
            <div>An error occurred during service rename</div>
            <div style="font-size: 12px; margin-top: 5px;">Please try again</div>
        `;
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    });
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Close modal events
    const closeBtn = document.getElementById('rename-modal-close');
    const cancelBtn = document.getElementById('rename-cancel-btn');
    const confirmBtn = document.getElementById('rename-confirm-btn');
    const modal = document.getElementById('rename-service-modal');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', closeRenameServiceModal);
    }
    
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeRenameServiceModal);
    }
    
    if (confirmBtn) {
        confirmBtn.addEventListener('click', renameService);
    }
    
    // Close modal when clicking outside
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeRenameServiceModal();
            }
        });
    }
    
    // Handle Enter key in new service name input
    const newNameInput = document.getElementById('new-service-name');
    if (newNameInput) {
        newNameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                renameService();
            }
        });
    }
});
