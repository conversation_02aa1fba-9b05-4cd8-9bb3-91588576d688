<?php
header('Content-Type: text/plain');
include "loadenv.php";

$host = $_ENV["DB_SERVER"];
$dbname = 'db_nagiosql_v3';
$username = $_ENV["DB_USER"];
$password = $_ENV["DB_PASSWORD"];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Modified query to select both id and alias
    $query = "SELECT id, alias FROM tbl_hostgroup";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    
    // Fetch all results as associative array
    $hostgroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format output as "id. alias" for each line
    $output = [];
    foreach ($hostgroups as $hg) {
        $output[] = $hg['id'] . '. ' . $hg['alias'];
    }
    
    // Output as plain text with newlines
    echo implode("\n", $output);

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}

$pdo = null;
?>