<?php
include_once "loadenv.php";

function getDatabaseConnectionNagiosql() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "db_nagiosql_v3";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function checkServiceRenameable($ip, $serviceName) {
    try {
        $nagiosConn = getDatabaseConnectionNagiosql();
        
        // 1. Get host ID from tbl_host where address matches the IP
        $hostQuery = "SELECT id FROM tbl_host WHERE address = ?";
        $hostStmt = $nagiosConn->prepare($hostQuery);
        $hostStmt->bind_param("s", $ip);
        $hostStmt->execute();
        $hostResult = $hostStmt->get_result();
        
        if ($hostResult->num_rows === 0) {
            $hostStmt->close();
            $nagiosConn->close();
            return array('renameable' => false, 'reason' => 'Host not found');
        }
        
        $hostRow = $hostResult->fetch_assoc();
        $hostId = $hostRow['id'];
        $hostStmt->close();
        
        // 2. Check if service exists and has config_name starting with 'imp_'
        $serviceQuery = "
            SELECT s.config_name 
            FROM tbl_service s 
            INNER JOIN tbl_lnkServiceToHost lsh ON s.id = lsh.idMaster 
            WHERE lsh.idSlave = ? 
            AND s.service_description = ? 
            AND s.register = '1' 
            AND s.active = '1'
        ";
        $serviceStmt = $nagiosConn->prepare($serviceQuery);
        $serviceStmt->bind_param("is", $hostId, $serviceName);
        $serviceStmt->execute();
        $serviceResult = $serviceStmt->get_result();
        
        if ($serviceResult->num_rows === 0) {
            $serviceStmt->close();
            $nagiosConn->close();
            return array('renameable' => false, 'reason' => 'Service not found');
        }
        
        $serviceRow = $serviceResult->fetch_assoc();
        $configName = $serviceRow['config_name'];
        $serviceStmt->close();
        $nagiosConn->close();
        
        // 3. Check if config_name starts with 'imp_'
        if ($configName && strpos($configName, 'imp_') === 0) {
            return array('renameable' => true, 'config_name' => $configName);
        } else {
            return array('renameable' => false, 'reason' => 'Service config_name does not start with imp_');
        }
        
    } catch (Exception $e) {
        error_log("Error checking service renameable: " . $e->getMessage());
        return array('renameable' => false, 'reason' => 'Database error');
    }
}

// Handle GET request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $ip = isset($_GET['ip']) ? $_GET['ip'] : '';
    $serviceName = isset($_GET['service_name']) ? $_GET['service_name'] : '';

    // Basic validation
    if (empty($ip) || empty($serviceName)) {
        echo json_encode(array(
            'renameable' => false,
            'reason' => 'Both ip and service_name parameters are required'
        ));
        exit;
    }

    try {
        $result = checkServiceRenameable($ip, $serviceName);
        echo json_encode($result);
    } catch (Exception $e) {
        echo json_encode(array(
            'renameable' => false,
            'reason' => $e->getMessage()
        ));
    }
} else {
    echo json_encode(array(
        'renameable' => false,
        'reason' => 'This script expects a GET request'
    ));
}
?>
