<?php
// Global variables
$selectedLang = 'en';  // Default value
$dictionaries = [];
$languages = [
    'en' => 'English',
    'fr' => 'Français'
];

function loadLanguageData($jsonFilePath = 'languages.json') {
    global $selectedLang, $dictionaries;
    
    // Load the JSON file
    $jsonData = file_get_contents($jsonFilePath);
    $data = json_decode($jsonData, true);
    $selectedLang = $data['selectedLang'] ?? 'en';
    $dictionaries = $data['dictionaries'] ?? [];
}

function renderLanguageSelector($selectId = 'langSelect', $labelText = 'Language:') {
    global $selectedLang, $languages;
    
    $output = "<label for=\"$selectId\">$labelText</label>";
    $output .= "<select class='language-select' id=\"$selectId\">";
    
    foreach ($languages as $code => $name) {
        $selected = ($selectedLang === $code) ? 'selected' : '';
        $output .= "<option value=\"$code\" $selected>$name</option>";
    }
    
    $output .= '</select>';
    
    return $output;
}

// Load data immediately when included
loadLanguageData();

// If file is accessed directly, render the selector
if (basename($_SERVER['PHP_SELF']) === 'language_selector.php') {
    echo renderLanguageSelector();
}
?>