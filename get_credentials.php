<?php
header('Content-Type: application/json');
include "loadenv.php";

function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "blesk";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();

    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "tfUsername" => $row["username"],
            "tfPassword" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }

    $conn->close();

    return $userCredentials;
}

echo json_encode(getUserCredentials());
?>