<?php
include_once "loadenv.php";

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "blesk";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getDatabaseConnectionBubblemaps() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getDatabaseConnectionNagiosql() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "db_nagiosql_v3";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();

    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "username" => $row["username"],
            "password" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }

    $conn->close();

    return $userCredentials;
}

function updateHostMutedStatus($ip) {
    $conn = getDatabaseConnectionBubblemaps();
    
    // First check if the IP exists in servicesPending
    $checkSql = "SELECT host_ip FROM servicesPending WHERE host_ip = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param("s", $ip);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    // Only mute if the IP is not found in servicesPending
    if ($result->num_rows === 0) {
        $updateSql = "UPDATE hosts SET muted = 1 WHERE ip = ?";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bind_param("s", $ip);
        $updateResult = $updateStmt->execute();
        $updateStmt->close();
    } else {
        $updateResult = true; // No need to mute, IP exists in servicesPending
    }
    
    $checkStmt->close();
    $conn->close();
    
    return $updateResult;
}

function simulateVerifyActions($hostname) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        // Initialize cURL session with cookies
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        // Login to Nagios
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => $_ENV['NAGIOS_USER'],
            'tfPassword' => $_ENV['NAGIOS_PASS'],
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Login failed: ' . curl_error($ch));

        // Handle redirect
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        // Perform verification actions
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if(curl_errno($ch)) throw new Exception("Action $action failed");
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log($e->getMessage());
    }
}

function deleteServiceDirectly($ip, $serviceToDelete) {
    try {
        $nagiosConn = getDatabaseConnectionNagiosql();
        
        // 1. Get host ID from tbl_host where address matches the IP
        $hostQuery = "SELECT id FROM tbl_host WHERE address = ?";
        $hostStmt = $nagiosConn->prepare($hostQuery);
        $hostStmt->bind_param("s", $ip);
        $hostStmt->execute();
        $hostResult = $hostStmt->get_result();
        
        if ($hostResult->num_rows === 0) {
            $hostStmt->close();
            $nagiosConn->close();
            throw new Exception("Host with IP $ip not found in Nagios database");
        }
        
        $hostRow = $hostResult->fetch_assoc();
        $hostId = $hostRow['id'];
        $hostStmt->close();
        
        // Support both single service and array of services
        $servicesToDelete = is_array($serviceToDelete) ? $serviceToDelete : array($serviceToDelete);
        $deletedCount = 0;
        
        foreach ($servicesToDelete as $service) {
            // 2. Get service IDs and config_name from tbl_service where service_description matches
            $serviceQuery = "SELECT id, config_name FROM tbl_service WHERE service_description = ? AND register = '1' AND active = '1'";
            $serviceStmt = $nagiosConn->prepare($serviceQuery);
            $serviceStmt->bind_param("s", $service);
            $serviceStmt->execute();
            $serviceResult = $serviceStmt->get_result();
            
            if ($serviceResult->num_rows === 0) {
                error_log("Service $service not found for host $ip");
                $serviceStmt->close();
                continue; // Skip to next service
            }
            
            $serviceData = array();
            while ($serviceRow = $serviceResult->fetch_assoc()) {
                $serviceData[] = array(
                    'id' => $serviceRow['id'],
                    'config_name' => $serviceRow['config_name']
                );
            }
            $serviceStmt->close();
            
            $serviceDeleted = false;
            
            // 3. Delete from tbl_lnkServiceToHost where idMaster is service ID and idSlave is host ID
            foreach ($serviceData as $serviceInfo) {
                $serviceId = $serviceInfo['id'];
                $configName = $serviceInfo['config_name'];
                
                $deleteQuery = "DELETE FROM tbl_lnkServiceToHost WHERE idMaster = ? AND idSlave = ?";
                $deleteStmt = $nagiosConn->prepare($deleteQuery);
                $deleteStmt->bind_param("ii", $serviceId, $hostId);
                $deleteResult = $deleteStmt->execute();
                
                if ($deleteResult && $deleteStmt->affected_rows > 0) {
                    $serviceDeleted = true;
                    $deletedCount++;
                    
                    // 4. Check if the service is still linked to any host
                    $checkQuery = "SELECT COUNT(*) as count FROM tbl_lnkServiceToHost WHERE idMaster = ?";
                    $checkStmt = $nagiosConn->prepare($checkQuery);
                    $checkStmt->bind_param("i", $serviceId);
                    $checkStmt->execute();
                    $checkResult = $checkStmt->get_result();
                    $checkRow = $checkResult->fetch_assoc();
                    $checkStmt->close();
                    
                    // 5. If no more links, update tbl_service to set register='0' and active='0'
                    if ($checkRow['count'] == 0) {
                        // Check if config_name starts with "imp_"
                        if ($configName && strpos($configName, 'imp_') === 0) {
                            // For imp_ services, check if any other services have the same config_name
                            $checkConfigQuery = "SELECT COUNT(*) as count FROM tbl_service WHERE config_name = ? AND id != ?";
                            $checkConfigStmt = $nagiosConn->prepare($checkConfigQuery);
                            $checkConfigStmt->bind_param("si", $configName, $serviceId);
                            $checkConfigStmt->execute();
                            $checkConfigResult = $checkConfigStmt->get_result();
                            $checkConfigRow = $checkConfigResult->fetch_assoc();
                            $checkConfigStmt->close();
                            
                            // If no other services have this config_name, delete the service and config file
                            if ($checkConfigRow['count'] == 0) {
                                // Delete the service from tbl_service
                                $deleteServiceQuery = "DELETE FROM tbl_service WHERE id = ?";
                                $deleteServiceStmt = $nagiosConn->prepare($deleteServiceQuery);
                                $deleteServiceStmt->bind_param("i", $serviceId);
                                $deleteServiceStmt->execute();
                                $deleteServiceStmt->close();
                                
                                // Delete the config file
                                $configPath = "/etc/nagiosql/services/" . $configName . ".cfg";
                                if (file_exists($configPath)) {
                                    if (!unlink($configPath)) {
                                        error_log("Failed to delete config file: $configPath");
                                    } else {
                                        error_log("Successfully deleted config file: $configPath");
                                    }
                                }
                            } else {
                                // Other services have this config_name, just delete this service
                                $deleteServiceQuery = "DELETE FROM tbl_service WHERE id = ?";
                                $deleteServiceStmt = $nagiosConn->prepare($deleteServiceQuery);
                                $deleteServiceStmt->bind_param("i", $serviceId);
                                $deleteServiceStmt->execute();
                                $deleteServiceStmt->close();
                            }
                        } else {
                            // For non-imp_ services, just set register='0' and active='0'
                            $updateQuery = "UPDATE tbl_service SET register = '0', active = '0' WHERE id = ?";
                            $updateStmt = $nagiosConn->prepare($updateQuery);
                            $updateStmt->bind_param("i", $serviceId);
                            $updateStmt->execute();
                            $updateStmt->close();
                            
                            // Delete the config file if it exists
                            if ($configName) {
                                $configPath = "/etc/nagiosql/services/" . $configName . ".cfg";
                                if (file_exists($configPath)) {
                                    if (!unlink($configPath)) {
                                        error_log("Failed to delete config file: $configPath");
                                    } else {
                                        error_log("Successfully deleted config file: $configPath");
                                    }
                                }
                            }
                        }
                    }
                }
                $deleteStmt->close();
            }
            
            if (!$serviceDeleted) {
                error_log("Failed to delete service $service for host $ip");
            }
        }
        
        $nagiosConn->close();
        
        // 7. Run simulateVerifyActions after successful deletion
        if ($deletedCount > 0) {
            $hostname = getSelfIp();
            simulateVerifyActions($hostname);
        }
        
        return array(
            'success' => $deletedCount > 0,
            'deleted_count' => $deletedCount,
            'total_requested' => count($servicesToDelete)
        );
        
    } catch (Exception $e) {
        error_log("Error deleting service: " . $e->getMessage());
        return array(
            'success' => false,
            'error' => $e->getMessage()
        );
    }
}

$ip = '';
$serviceToDelete = '';

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get the 'ip' value from the POST request
    $ip = isset($_POST['ip']) ? $_POST['ip'] : '';
    
    // Handle both single service and array of services
    if (isset($_POST['servicetodelete']) && is_string($_POST['servicetodelete'])) {
        $serviceToDelete = $_POST['servicetodelete'];
    } elseif (isset($_POST['servicetodelete']) && is_array($_POST['servicetodelete'])) {
        $serviceToDelete = $_POST['servicetodelete'];
    } else {
        $serviceToDelete = '';
    }

    // Basic validation to ensure both parameters are present
    if (empty($ip) || empty($serviceToDelete)) {
        echo "Error: Both 'ip' and 'servicetodelete' parameters are required in the POST request.\n";
    } else {
        try {
            $result = deleteServiceDirectly($ip, $serviceToDelete);
            
            if ($result['success']) {
                // Update the muted status after successful deletion
                if (updateHostMutedStatus($ip)) {
                    echo "Success: Deleted {$result['deleted_count']} services and muted host.\n";
                } else {
                    echo "Success: Deleted {$result['deleted_count']} services but failed to mute host.\n";
                }
            } else {
                if (isset($result['error'])) {
                    echo "Error: " . $result['error'] . "\n";
                } else {
                    echo "Error: No services were deleted. Check service names.\n";
                }
            }
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }
} else {
    // If the request is not POST, provide a message indicating how to use the script
    echo "This script expects 'ip' and 'servicetodelete' parameters to be sent via a POST request.\n";
}
?>