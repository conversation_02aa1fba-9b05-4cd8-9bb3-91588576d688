<?php
include "loadenv.php";

/**
 * Handle host approval or rejection
 * If approved, sets apmStatus to 'not-added' to be picked up by the background scanner
 * If rejected, adds the host to blacklist similar to blacklistHost.php
 */

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "blesk";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();

    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "username" => $row["username"],
            "password" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }

    $conn->close();

    return $userCredentials;
}

function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getDatabaseConnectionNagiosql() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "db_nagiosql_v3";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getAllServicesForHost($ip) {
    try {
        $nagiosConn = getDatabaseConnectionNagiosql();
        
        // 1. Get host ID from tbl_host where address matches the IP
        $hostQuery = "SELECT id FROM tbl_host WHERE address = ?";
        $hostStmt = $nagiosConn->prepare($hostQuery);
        $hostStmt->bind_param("s", $ip);
        $hostStmt->execute();
        $hostResult = $hostStmt->get_result();
        
        if ($hostResult->num_rows === 0) {
            $hostStmt->close();
            $nagiosConn->close();
            return array(); // No host found
        }
        
        $hostRow = $hostResult->fetch_assoc();
        $hostId = $hostRow['id'];
        $hostStmt->close();
        
        // 2. Get all services linked to this host
        $servicesQuery = "
            SELECT DISTINCT s.service_description 
            FROM tbl_service s 
            INNER JOIN tbl_lnkServiceToHost lsh ON s.id = lsh.idMaster 
            WHERE lsh.idSlave = ? AND s.register = '1' AND s.active = '1'
        ";
        $servicesStmt = $nagiosConn->prepare($servicesQuery);
        $servicesStmt->bind_param("i", $hostId);
        $servicesStmt->execute();
        $servicesResult = $servicesStmt->get_result();
        
        $services = array();
        while ($serviceRow = $servicesResult->fetch_assoc()) {
            $services[] = $serviceRow['service_description'];
        }
        $servicesStmt->close();
        $nagiosConn->close();
        
        return $services;
        
    } catch (Exception $e) {
        error_log("Error getting services for host: " . $e->getMessage());
        return array();
    }
}

function simulateVerifyActions($hostname) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        // Initialize cURL session with cookies
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        // Login to Nagios
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => $_ENV['NAGIOS_USER'],
            'tfPassword' => $_ENV['NAGIOS_PASS'],
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Login failed: ' . curl_error($ch));

        // Handle redirect
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        // Perform verification actions
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if(curl_errno($ch)) throw new Exception("Action $action failed");
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log($e->getMessage());
    }
}

function deleteServiceDirectly($ip, $serviceToDelete) {
    try {
        $nagiosConn = getDatabaseConnectionNagiosql();
        
        // 1. Get host ID from tbl_host where address matches the IP
        $hostQuery = "SELECT id FROM tbl_host WHERE address = ?";
        $hostStmt = $nagiosConn->prepare($hostQuery);
        $hostStmt->bind_param("s", $ip);
        $hostStmt->execute();
        $hostResult = $hostStmt->get_result();
        
        if ($hostResult->num_rows === 0) {
            $hostStmt->close();
            $nagiosConn->close();
            throw new Exception("Host with IP $ip not found in Nagios database");
        }
        
        $hostRow = $hostResult->fetch_assoc();
        $hostId = $hostRow['id'];
        $hostStmt->close();
        
        // Support both single service and array of services
        $servicesToDelete = is_array($serviceToDelete) ? $serviceToDelete : array($serviceToDelete);
        $deletedCount = 0;
        
        foreach ($servicesToDelete as $service) {
            // 2. Get service IDs and config_name from tbl_service where service_description matches
            $serviceQuery = "SELECT id, config_name FROM tbl_service WHERE service_description = ? AND register = '1' AND active = '1'";
            $serviceStmt = $nagiosConn->prepare($serviceQuery);
            $serviceStmt->bind_param("s", $service);
            $serviceStmt->execute();
            $serviceResult = $serviceStmt->get_result();
            
            if ($serviceResult->num_rows === 0) {
                error_log("Service $service not found for host $ip");
                $serviceStmt->close();
                continue; // Skip to next service
            }
            
            $serviceData = array();
            while ($serviceRow = $serviceResult->fetch_assoc()) {
                $serviceData[] = array(
                    'id' => $serviceRow['id'],
                    'config_name' => $serviceRow['config_name']
                );
            }
            $serviceStmt->close();
            
            $serviceDeleted = false;
            
            // 3. Delete from tbl_lnkServiceToHost where idMaster is service ID and idSlave is host ID
            foreach ($serviceData as $serviceInfo) {
                $serviceId = $serviceInfo['id'];
                $configName = $serviceInfo['config_name'];
                
                $deleteQuery = "DELETE FROM tbl_lnkServiceToHost WHERE idMaster = ? AND idSlave = ?";
                $deleteStmt = $nagiosConn->prepare($deleteQuery);
                $deleteStmt->bind_param("ii", $serviceId, $hostId);
                $deleteResult = $deleteStmt->execute();
                
                if ($deleteResult && $deleteStmt->affected_rows > 0) {
                    $serviceDeleted = true;
                    $deletedCount++;
                    
                    // 4. Check if the service is still linked to any host
                    $checkQuery = "SELECT COUNT(*) as count FROM tbl_lnkServiceToHost WHERE idMaster = ?";
                    $checkStmt = $nagiosConn->prepare($checkQuery);
                    $checkStmt->bind_param("i", $serviceId);
                    $checkStmt->execute();
                    $checkResult = $checkStmt->get_result();
                    $checkRow = $checkResult->fetch_assoc();
                    $checkStmt->close();
                    
                    // 5. If no more links, update tbl_service to set register='0' and active='0'
                    if ($checkRow['count'] == 0) {
                        // Check if config_name starts with "imp_"
                        if ($configName && strpos($configName, 'imp_') === 0) {
                            // For imp_ services, check if any other services have the same config_name
                            $checkConfigQuery = "SELECT COUNT(*) as count FROM tbl_service WHERE config_name = ? AND id != ?";
                            $checkConfigStmt = $nagiosConn->prepare($checkConfigQuery);
                            $checkConfigStmt->bind_param("si", $configName, $serviceId);
                            $checkConfigStmt->execute();
                            $checkConfigResult = $checkConfigStmt->get_result();
                            $checkConfigRow = $checkConfigResult->fetch_assoc();
                            $checkConfigStmt->close();
                            
                            // If no other services have this config_name, delete the service and config file
                            if ($checkConfigRow['count'] == 0) {
                                // Delete the service from tbl_service
                                $deleteServiceQuery = "DELETE FROM tbl_service WHERE id = ?";
                                $deleteServiceStmt = $nagiosConn->prepare($deleteServiceQuery);
                                $deleteServiceStmt->bind_param("i", $serviceId);
                                $deleteServiceStmt->execute();
                                $deleteServiceStmt->close();
                                
                                // Delete the config file
                                $configPath = "/etc/nagiosql/services/" . $configName . ".cfg";
                                if (file_exists($configPath)) {
                                    if (!unlink($configPath)) {
                                        error_log("Failed to delete config file: $configPath");
                                    } else {
                                        error_log("Successfully deleted config file: $configPath");
                                    }
                                }
                            } else {
                                // Other services have this config_name, just delete this service
                                $deleteServiceQuery = "DELETE FROM tbl_service WHERE id = ?";
                                $deleteServiceStmt = $nagiosConn->prepare($deleteServiceQuery);
                                $deleteServiceStmt->bind_param("i", $serviceId);
                                $deleteServiceStmt->execute();
                                $deleteServiceStmt->close();
                            }
                        } else {
                            // For non-imp_ services, just set register='0' and active='0'
                            $updateQuery = "UPDATE tbl_service SET register = '0', active = '0' WHERE id = ?";
                            $updateStmt = $nagiosConn->prepare($updateQuery);
                            $updateStmt->bind_param("i", $serviceId);
                            $updateStmt->execute();
                            $updateStmt->close();
                            
                            // Delete the config file if it exists
                            if ($configName) {
                                $configPath = "/etc/nagiosql/services/" . $configName . ".cfg";
                                if (file_exists($configPath)) {
                                    if (!unlink($configPath)) {
                                        error_log("Failed to delete config file: $configPath");
                                    } else {
                                        error_log("Successfully deleted config file: $configPath");
                                    }
                                }
                            }
                        }
                    }
                }
                $deleteStmt->close();
            }
            
            if (!$serviceDeleted) {
                error_log("Failed to delete service $service for host $ip");
            }
        }
        
        $nagiosConn->close();
        
        // 7. Run simulateVerifyActions after successful deletion
        if ($deletedCount > 0) {
            $hostname = getSelfIp();
            simulateVerifyActions($hostname);
        }
        
        return array(
            'success' => $deletedCount > 0,
            'deleted_count' => $deletedCount,
            'total_requested' => count($servicesToDelete)
        );
        
    } catch (Exception $e) {
        error_log("Error deleting service: " . $e->getMessage());
        return array(
            'success' => false,
            'error' => $e->getMessage()
        );
    }
}

function deleteServices($ip, $selfIP, $usr, $pwd) {
    try {
        // Get all services for this host
        $allServices = getAllServicesForHost($ip);
        
        if (empty($allServices)) {
            error_log("No services found for host $ip");
            return "No services found for host $ip";
        }
        
        // Delete all services using the deleteServiceDirectly function
        $result = deleteServiceDirectly($ip, $allServices);
        
        if ($result['success']) {
            error_log("Successfully deleted {$result['deleted_count']} services for host $ip");
            return "Successfully deleted {$result['deleted_count']} services for host $ip";
        } else {
            $errorMsg = isset($result['error']) ? $result['error'] : "Unknown error occurred";
            error_log("Failed to delete services for host $ip: $errorMsg");
            return "Failed to delete services for host $ip: $errorMsg";
        }
        
    } catch (Exception $e) {
        error_log("Error in deleteServices: " . $e->getMessage());
        return "Error: " . $e->getMessage();
    }
}

function deleteHost($ip, $selfIP, $usr, $pwd){
    $externalUrl = "https://$selfIP/ndd/apm-delete-host-proc.php?url_ip=" . urlencode($ip);

    $ch = curl_init($externalUrl);

    // Disable SSL certificate and host verification
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERPWD, "$usr:$pwd");

    $response = curl_exec($ch);

    if ($response === false) {
        echo "cURL error: " . curl_error($ch);
    }

    curl_close($ch);
}

function approveHost() {
    if (isset($_GET['ip']) && isset($_GET['infra']) && isset($_GET['action'])) {
        $ip = $_GET['ip'];
        $infra = $_GET['infra'];
        $action = $_GET['action'];
        
        $conn = getDatabaseConnection();
        
        if ($action === 'approve') {
            // First, get the current hostname for this host
            $getHostnameSql = "SELECT hostname FROM hosts WHERE ip = ? AND infra = ? AND apmStatus = 'ask'";
            $hostname = "";
            
            if ($hostnameStmt = $conn->prepare($getHostnameSql)) {
                $hostnameStmt->bind_param('ss', $ip, $infra);
                $hostnameStmt->execute();
                $hostnameResult = $hostnameStmt->get_result();
                
                if ($hostnameRow = $hostnameResult->fetch_assoc()) {
                    $hostname = $hostnameRow['hostname'];
                    
                    // Check if the hostname is not a valid IP and contains dots
                    if (!filter_var($hostname, FILTER_VALIDATE_IP) && strpos($hostname, '.') !== false) {
                        // Extract only the first part of the hostname before the first dot
                        $parts = explode('.', $hostname);
                        $hostname = $parts[0];
                    }
                }
                
                $hostnameStmt->close();
            }
            
            // Approve the host by setting apmStatus to 'not-added' and updating hostname if needed
            $sql = "UPDATE hosts SET apmStatus = 'not-added', hostname = ? WHERE ip = ? AND infra = ? AND apmStatus = 'ask'";
            
            if ($stmt = $conn->prepare($sql)) {
                $stmt->bind_param('sss', $hostname, $ip, $infra);
                
                if ($stmt->execute()) {
                    echo "Host approved and queued for monitoring";
                } else {
                    echo "Error approving host: " . $stmt->error;
                }
                
                $stmt->close();
            } else {
                echo "Error preparing approval query: " . $conn->error;
            }
        } elseif ($action === 'reject') {
            // Get the self IP and admin credentials
            $selfIP = getSelfIp();
            $adminUser = getUserCredentials();
            $usr = $adminUser["username"];
            $pwd = $adminUser["password"];

            // Count how many times this IP appears across all infrastructures
            $countSql = "SELECT COUNT(*) as ip_count FROM hosts WHERE ip = ? AND blacklist = 0";
            if ($countStmt = $conn->prepare($countSql)) {
                $countStmt->bind_param('s', $ip);
                $countStmt->execute();
                $result = $countStmt->get_result();
                $row = $result->fetch_assoc();
                $ipCount = $row['ip_count'];
                $countStmt->close();
            } else {
                echo "Error preparing count query: " . $conn->error;
                $conn->close();
                return;
            }

            // Mark the host as blacklisted
            $sql = "UPDATE hosts SET blacklist = 1 WHERE ip = ? AND infra = ?";
            if ($stmt = $conn->prepare($sql)) {
                $stmt->bind_param('ss', $ip, $infra);
                
                if ($stmt->execute()) {
                    // If this was the only instance of the IP, delete from APM
                    if ($ipCount == 1 && filter_var($ip, FILTER_VALIDATE_IP)) {
                        try {
                            deleteServices($ip, $selfIP, $usr, $pwd);
                            deleteHost($ip, $selfIP, $usr, $pwd);
                        } catch (Exception $e) {
                            echo "Host rejected and added to blacklist. Note: " . $e->getMessage();
                            $stmt->close();
                            $conn->close();
                            return;
                        }
                    }
                    echo "Host rejected and added to blacklist";
                } else {
                    echo "Error rejecting host: " . $stmt->error;
                }
                
                $stmt->close();
            } else {
                echo "Error preparing rejection query: " . $conn->error;
            }
        } else {
            echo "Invalid action. Use 'approve' or 'reject'.";
        }
        
        $conn->close();
    } else {
        echo "Missing required parameters. Need ip, infra, and action.";
    }
}

// Get all hosts waiting for approval
function getHostsWaitingApproval() {
    $conn = getDatabaseConnection();
    
    // Get pagination parameters
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 50; // Number of results per page
    $offset = ($page - 1) * $limit;
    
    // Get search term if present
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    
    // Build WHERE clause
    $where = "apmStatus = 'ask' AND blacklist = 0";
    $params = [];
    $types = "";
    
    if (isset($_GET['infra'])) {
        $infra = $_GET['infra'];
        $where .= " AND infra = ?";
        $params[] = $infra;
        $types .= "s";
        
        if (isset($_GET['subnet']) && $_GET['subnet'] !== "all" && $_GET['subnet'] !== "true") {
            $subnet = $_GET['subnet'];
            $where .= " AND subnet = ?";
            $params[] = $subnet;
            $types .= "s";
        }
    }
    
    // Add search condition if search term is provided
    if (!empty($search)) {
        $where .= " AND (hostname LIKE ? OR ip LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $types .= "ss";
    }
    
    // First, get the total count of hosts matching the criteria
    $countSql = "SELECT COUNT(*) as total FROM hosts WHERE $where";
    
    $countStmt = $conn->prepare($countSql);
    if (!empty($params)) {
        $countStmt->bind_param($types, ...$params);
    }
    $countStmt->execute();
    $countResult = $countStmt->get_result();
    $countRow = $countResult->fetch_assoc();
    $totalHosts = $countRow['total'];
    $countStmt->close();
    
    // Calculate total pages
    $totalPages = ceil($totalHosts / $limit);
    
    // Make sure page is in valid range
    if ($page < 1) $page = 1;
    if ($page > $totalPages && $totalPages > 0) $page = $totalPages;
    
    // Then, get the hosts data with pagination
    $sql = "SELECT id, ip, hostname, subnet, infra FROM hosts WHERE $where";
    
    // Add ORDER BY clause for consistent results
    $sql .= " ORDER BY hostname ASC, ip ASC";
    
    // Add LIMIT clause for pagination
    $sql .= " LIMIT ?, ?";
    
    // Add pagination parameters
    $params[] = $offset;
    $params[] = $limit;
    $types .= "ii"; // integer types for offset and limit
    
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $hosts = [];
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $hosts[] = $row;
        }
    }
    
    $stmt->close();
    $conn->close();
    
    // Include pagination info in response
    $response = [
        'hosts' => $hosts,
        'total' => $totalHosts,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => $totalPages
    ];
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Approve or reject all hosts at once
function approveOrRejectAllHosts() {
    $action = $_GET['action'];
    
    if ($action !== 'approve_all' && $action !== 'reject_all') {
        echo "Invalid action. Use 'approve_all' or 'reject_all'.";
        return;
    }
    
    $conn = getDatabaseConnection();
    $isApprove = ($action === 'approve_all');
    
    // Build the WHERE clause
    $where = "apmStatus = 'ask' AND blacklist = 0";
    $params = [];
    $types = "";
    
    if (isset($_GET['infra'])) {
        $infra = $_GET['infra'];
        $where .= " AND infra = ?";
        $params[] = $infra;
        $types .= "s";
        
        if (isset($_GET['subnet']) && $_GET['subnet'] !== "all" && $_GET['subnet'] !== "true") {
            $subnet = $_GET['subnet'];
            $where .= " AND subnet = ?";
            $params[] = $subnet;
            $types .= "s";
        }
    }
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Get the self IP and admin credentials for host deletion if needed
        $selfIP = getSelfIp();
        $adminUser = getUserCredentials();
        $usr = $adminUser["username"];
        $pwd = $adminUser["password"];
        
        // First, get all the hosts matching the criteria
        $sql = "SELECT id, ip, hostname, infra FROM hosts WHERE $where";
        
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $hostData = [];
        
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $hostData[] = [
                    'id' => $row['id'],
                    'ip' => $row['ip'],
                    'hostname' => $row['hostname'],
                    'infra' => $row['infra']
                ];
            }
        }
        
        $stmt->close();
        
        if (empty($hostData)) {
            $conn->commit();
            echo "No hosts found to " . ($isApprove ? "approve" : "reject");
            return;
        }
        
        // Process all hosts
        $processedCount = 0;
        
        foreach ($hostData as $host) {
            $ip = $host['ip'];
            $hostname = $host['hostname'];
            $infra = $host['infra'];
            
            if ($isApprove) {
                // Sanitize hostname for approval
                if (!filter_var($hostname, FILTER_VALIDATE_IP) && strpos($hostname, '.') !== false) {
                    // Extract only the first part of the hostname before the first dot
                    $parts = explode('.', $hostname);
                    $hostname = $parts[0];
                }
                
                // Approve the host by setting apmStatus to 'not-added'
                $updateSql = "UPDATE hosts SET apmStatus = 'not-added', hostname = ? WHERE ip = ? AND infra = ? AND apmStatus = 'ask'";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->bind_param('sss', $hostname, $ip, $infra);
                
                if (!$updateStmt->execute()) {
                    throw new Exception("Error approving host: " . $updateStmt->error);
                }
                
                $updateStmt->close();
            } else {
                // Count how many times this IP appears across all infrastructures
                $countSql = "SELECT COUNT(*) as ip_count FROM hosts WHERE ip = ? AND blacklist = 0";
                $countStmt = $conn->prepare($countSql);
                $countStmt->bind_param('s', $ip);
                $countStmt->execute();
                $countResult = $countStmt->get_result();
                $countRow = $countResult->fetch_assoc();
                $ipCount = $countRow['ip_count'];
                $countStmt->close();
                
                // Mark the host as blacklisted
                $updateSql = "UPDATE hosts SET blacklist = 1 WHERE ip = ? AND infra = ?";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->bind_param('ss', $ip, $infra);
                
                if (!$updateStmt->execute()) {
                    throw new Exception("Error rejecting host: " . $updateStmt->error);
                }
                
                $updateStmt->close();
                
                // If this was the only instance of the IP, delete from APM
                if ($ipCount == 1 && filter_var($ip, FILTER_VALIDATE_IP)) {
                    try {
                        deleteServices($ip, $selfIP, $usr, $pwd);
                        deleteHost($ip, $selfIP, $usr, $pwd);
                    } catch (Exception $e) {
                        // Continue even if API calls fail
                        error_log("API call error for IP $ip: " . $e->getMessage());
                    }
                }
            }
            
            $processedCount++;
        }
        
        // Commit the transaction
        $conn->commit();
        echo ($isApprove ? "Approved" : "Rejected") . " $processedCount hosts successfully";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        echo $e->getMessage();
    }
    
    $conn->close();
}

// Route based on the request
if (isset($_GET['action'])) {
    if ($_GET['action'] === 'approve' || $_GET['action'] === 'reject') {
        approveHost();
    } else if ($_GET['action'] === 'approve_all' || $_GET['action'] === 'reject_all') {
        approveOrRejectAllHosts();
    } else {
        echo "Invalid action. Specify action=approve|reject|approve_all|reject_all";
    }
} else if (isset($_GET['list']) && $_GET['list'] === 'pending') {
    getHostsWaitingApproval();
} else {
    echo "Invalid request. Specify action=approve|reject|approve_all|reject_all or list=pending";
}

