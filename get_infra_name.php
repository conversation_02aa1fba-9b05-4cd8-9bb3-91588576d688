<?php
include "loadenv.php";
$servername = $_ENV["DB_SERVER"];
$username = $_ENV["DB_USER"];
$password = $_ENV["DB_PASSWORD"]; 
$dbname = "bubblemaps";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$sql = "SELECT id, name FROM infra";
$result = $conn->query($sql);

$infraData = array();
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $infraData[] = $row;
    }
}

echo json_encode($infraData);

$conn->close();
?>
