<?php
include "loadenv.php";

// Configuration constants
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);

// Database credentials
$dbHost = $_ENV["DB_SERVER"];
$dbName = 'db_nagiosql_v3';
$dbUser = $_ENV["DB_USER"];
$dbPass = $_ENV['DB_PASSWORD'];

header('Content-Type: application/json');

// Validate input
if (!isset($_POST['oldName']) || !isset($_POST['newName'])) {
    echo json_encode(['success' => false, 'message' => 'Missing oldName or newName parameter']);
    exit;
}

$oldName = trim($_POST['oldName']);
$newName = trim($_POST['newName']);

if ($oldName === '' || $newName === '') {
    echo json_encode(['success' => false, 'message' => 'Hostgroup names cannot be empty']);
    exit;
}

try {
    // Connect to the database
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Update hostgroup_name and alias
    $stmt = $pdo->prepare("UPDATE tbl_hostgroup SET hostgroup_name = :newName, alias = :newName WHERE hostgroup_name = :oldName OR alias = :oldName");
    $stmt->bindParam(':newName', $newName);
    $stmt->bindParam(':oldName', $oldName);
    $stmt->execute();

    $affectedRows = $stmt->rowCount();
    if ($affectedRows === 0) {
        echo json_encode(['success' => false, 'message' => 'Hostgroup not found or already has that name']);
        exit;
    }

    // Also update bubblemaps database so hosts reflect renamed group
    $bubbleDbName = 'bubblemaps';
    $bubblePdo = new PDO("mysql:host=$dbHost;dbname=$bubbleDbName", $dbUser, $dbPass);
    $bubblePdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $selectStmt = $bubblePdo->prepare("SELECT id, hostgroup FROM hosts WHERE hostgroup IS NOT NULL AND hostgroup LIKE :search");
    $like = '%' . $oldName . '%';
    $selectStmt->bindParam(':search', $like);
    $selectStmt->execute();

    $hostsUpdated = 0;
    while ($row = $selectStmt->fetch(PDO::FETCH_ASSOC)) {
        $id = $row['id'];
        $hostgroupJson = $row['hostgroup'];

        $groups = json_decode($hostgroupJson, true);
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($groups)) {
            // Skip invalid JSON formats
            continue;
        }

        $modified = false;
        foreach ($groups as &$g) {
            if ($g === $oldName) {
                $g = $newName;
                $modified = true;
            }
        }
        unset($g);

        if ($modified) {
            $newJson = json_encode($groups);
            $updateStmt = $bubblePdo->prepare("UPDATE hosts SET hostgroup = :hg WHERE id = :id");
            $updateStmt->bindParam(':hg', $newJson);
            $updateStmt->bindParam(':id', $id, PDO::PARAM_INT);
            $updateStmt->execute();
            $hostsUpdated += $updateStmt->rowCount();
        }
    }

    // Trigger NagiosQL verify actions so the change is picked up
    simulateVerifyActions(getSelfIp());

    $msg = "Renamed hostgroup successfully (affected $affectedRows record(s))";
    if ($hostsUpdated > 0) {
        $msg .= " and updated $hostsUpdated host(s) in bubblemaps";
    }

    echo json_encode(['success' => true, 'message' => $msg]);
} catch (Exception $e) {
    error_log("Error in rename_hostgroup.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * Get the server's IP address
 */
function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        return '127.0.0.1';
    }
    return trim($ip);
}

/**
 * Simulate verify actions in NagiosQL so that configuration changes are applied
 */
function simulateVerifyActions($ip) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];

    try {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
        ]);

        // Login
        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit'    => 'Login'
        ]));
        curl_exec($ch);

        // Run each verify step
        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            curl_exec($ch);
        }
        curl_close($ch);
    } catch (Exception $e) {
        error_log("Error in simulateVerifyActions (rename_hostgroup): " . $e->getMessage());
    }
}
?> 