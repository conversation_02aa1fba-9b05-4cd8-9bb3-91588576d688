<?php
include "loadenv.php";

// Nagios configuration
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);

// Disable SSL verification
$context = stream_context_create([
    'ssl' => [
        'verify_peer' => false,
        'verify_peer_name' => false,
        'allow_self_signed' => true
    ]
]);

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function simulateVerifyActions($hostname) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        // Initialize cURL session with cookies
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        // Login to Nagios
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Login failed: ' . curl_error($ch));

        // Handle redirect
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        // Perform verification actions
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if(curl_errno($ch)) throw new Exception("Action $action failed");
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log($e->getMessage());
    }
}

$hostname = getSelfIp();

echo "Hostname: $hostname\n";

simulateVerifyActions($hostname);

echo "Done\n";
?>