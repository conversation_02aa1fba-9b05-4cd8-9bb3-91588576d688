<?php
include "loadenv.php";
$host = $_ENV["DB_SERVER"];
$dbname = 'bubblemaps';
$username = $_ENV["DB_USER"];
$password = $_ENV["DB_PASSWORD"];

// Get hostname from query parameter
$hostname = $_GET['hostname'] ?? null;

if (!$hostname) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Hostname parameter is required']);
    exit;
}

try {
    // Create database connection
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Prepare and execute query
    $stmt = $conn->prepare("SELECT ip FROM hosts WHERE hostname = :hostname");
    $stmt->bindParam(':hostname', $hostname);
    $stmt->execute();

    // Fetch the result
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    header('Content-Type: application/json');
    
    if ($result) {
        echo json_encode(['ip' => $result['ip']]);
    } else {
        echo json_encode(['message' => 'Hostname not found']);
    }
} catch(PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}

// Close connection
$conn = null;
?>