<?php
header('Content-Type: application/json');

include "loadenv.php";

function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"];
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

// Get the IP and vendor from the POST request
$ip = $_POST['ip'] ?? '';
$vendor = $_POST['vendor'] ?? '';

if (empty($ip) || empty($vendor)) {
    echo json_encode([
        'success' => false,
        'message' => 'IP or vendor not provided'
    ]);
    exit;
}

// Update the Vendor column in the hosts table
$conn = getDatabaseConnection();

$stmt = $conn->prepare("UPDATE hosts SET Vendor = ? WHERE ip = ?");
if ($stmt === false) {
    error_log("Prepare failed: " . $conn->error);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to prepare statement'
    ]);
    $conn->close();
    exit;
}

$stmt->bind_param("ss", $vendor, $ip);
if (!$stmt->execute()) {
    error_log("Execute failed: " . $stmt->error);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to update vendor'
    ]);
} else {
    echo json_encode([
        'success' => true,
        'message' => 'Vendor updated successfully'
    ]);
}

$stmt->close();
$conn->close();
?>