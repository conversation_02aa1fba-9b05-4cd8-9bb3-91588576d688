<?php
header('Content-Type: application/json');

$response = ['success' => false, 'message' => 'Invalid request'];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['theme'])) {
    $newTheme = $_POST['theme'];
    $themeJsonPath = 'styles/chosen-theme.json';

    // Validate the theme name format
    if (!preg_match('/^[a-zA-Z0-9_-]+$/', $newTheme)) {
        $response['message'] = 'Invalid theme name format.';
        echo json_encode($response);
        exit;
    }

    if (file_exists($themeJsonPath)) {
        $themeJsonContent = @file_get_contents($themeJsonPath);
        if ($themeJsonContent !== false) {
            $themeData = json_decode($themeJsonContent, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($themeData['available_themes']) && is_array($themeData['available_themes'])) {
                // Check if the selected theme is actually in the available list
                if (in_array($newTheme, $themeData['available_themes'])) {
                    $themeData['chosen_theme'] = $newTheme;
                    // Attempt to write the updated data back to the file
                    if (@file_put_contents($themeJsonPath, json_encode($themeData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)) !== false) {
                        $response['success'] = true;
                        $response['message'] = 'Theme updated successfully. Reloading page...';
                    } else {
                        $response['message'] = 'Error writing theme file.';
                        error_log("Error writing to $themeJsonPath");
                    }
                } else {
                    $response['message'] = 'Selected theme is not available.';
                }
            } else {
                $response['message'] = 'Error reading or decoding theme configuration.';
                 error_log("Error decoding JSON or missing 'available_themes' in $themeJsonPath. JSON error: " . json_last_error_msg());
            }
        } else {
            $response['message'] = 'Could not read theme file.';
             error_log("Could not read theme file: $themeJsonPath");
        }
    } else {
        $response['message'] = 'Theme configuration file not found.';
         error_log("Theme configuration file not found: $themeJsonPath");
    }
} else {
     $response['message'] = 'Invalid request method or missing theme parameter.';
}

echo json_encode($response);
?>