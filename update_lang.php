<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $newLang = $_POST['lang'] ?? 'en';
  $jsonFile = 'languages.json';

  // Load existing JSON
  $jsonData = file_get_contents($jsonFile);
  $data = json_decode($jsonData, true);

  // Update selectedLang
  $data['selectedLang'] = $newLang;

  // Write back to file
  file_put_contents($jsonFile, json_encode($data, JSON_PRETTY_PRINT));

  // Send success response
  echo 'Language updated';
} else {
  http_response_code(405); // Method Not Allowed
  echo 'Invalid request method';
}
?>