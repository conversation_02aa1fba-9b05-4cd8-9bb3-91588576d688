<?php
header('Content-Type: application/json');
include 'loadenv.php';

try {
    // Connect to the database
    $db = new PDO('mysql:host=' . $_ENV["DB_SERVER"] . ';dbname=db_nagiosql_v3', $_ENV["DB_USER"], $_ENV["DB_PASSWORD"]);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get POST data
    $hostgroup_name = $_POST['hostgroup_name'] ?? '';
    $alias = $_POST['alias'] ?? $hostgroup_name;
    $config_id = $_POST['config_id'] ?? 1;

    // Validate input
    if (empty($hostgroup_name)) {
        throw new Exception('Hostgroup name cannot be empty');
    }

    // Prepare and execute SQL
    $stmt = $db->prepare('INSERT INTO tbl_hostgroup (hostgroup_name, alias, config_id) VALUES (:hostgroup_name, :alias, :config_id)');
    $stmt->bindParam(':hostgroup_name', $hostgroup_name);
    $stmt->bindParam(':alias', $alias);
    $stmt->bindParam(':config_id', $config_id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Hostgroup added successfully']);
    } else {
        throw new Exception('Failed to add hostgroup');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>