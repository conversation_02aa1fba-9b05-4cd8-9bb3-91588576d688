<?php
include "loadenv.php";
$servername = $_ENV["DB_SERVER"];
$username = $_ENV["DB_USER"];
$password = $_ENV["DB_PASSWORD"];
$dbname = "bubblemaps";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if 'id' parameter is set in the GET request
if (isset($_GET['id'])) {
    $id = $_GET['id'];

    // First, check the current apmStatus for the given id
    $stmt_check = $conn->prepare("SELECT apmStatus FROM hosts WHERE id = ?");
    if ($stmt_check === false) {
        die("Prepare failed: " . $conn->error);
    }

    $stmt_check->bind_param("s", $id);
    $stmt_check->execute();
    $stmt_check->store_result();

    // Bind the result to a variable
    $stmt_check->bind_result($current_status);

    // Fetch the result
    if ($stmt_check->fetch()) {
        // Check if 'notAdded' parameter is set - this takes precedence over any other update
        if (isset($_GET['notAdded']) && $_GET['notAdded'] === 'Yes') {
            // Update to 'not-added' regardless of current status
            $stmt_update_not_added = $conn->prepare("UPDATE hosts SET apmStatus = 'not-added' WHERE id = ?");
            if ($stmt_update_not_added === false) {
                die("Prepare failed: " . $conn->error);
            }
            $stmt_update_not_added->bind_param("s", $id);
            if ($stmt_update_not_added->execute()) {
                echo "Record updated to 'not-added' successfully.";
            } else {
                echo "Error updating record to 'not-added': " . $stmt_update_not_added->error;
            }
            $stmt_update_not_added->close();
        } elseif ($current_status === "pending") {
            // If current status is already "pending", no need to update
            echo "Record already has 'pending' status. No update needed.";
        } else {
            // If the status is not "pending" and 'notAdded' is not the case, proceed with the update to 'pending'
            $stmt_update_pending = $conn->prepare("UPDATE hosts SET apmStatus = ? WHERE id = ?");
            if ($stmt_update_pending === false) {
                die("Prepare failed: " . $conn->error);
            }

            $status = "pending";
            $stmt_update_pending->bind_param("ss", $status, $id);

            // Execute the update statement
            if ($stmt_update_pending->execute()) {
                echo "Record updated to 'pending' successfully.";
            } else {
                echo "Error updating record to 'pending': " . $stmt_update_pending->error;
            }

            // Close the update statement
            $stmt_update_pending->close();
        }
    } else {
        echo "No record found for the given id.";
    }

    // Close the check statement
    $stmt_check->close();

} else {
    echo "id parameter is missing.";
}

// Close connection
$conn->close();
?>