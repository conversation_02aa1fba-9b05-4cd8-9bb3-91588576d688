<?php
include __DIR__ . "/loadenv.php";

// Check if SPM module is available
$spm_check = shell_exec("php " . __DIR__ . "/checkmods.php spm 2>&1");
if (trim($spm_check) !== "true") {
    echo "SPM module is not available. Exiting script.\n";
    exit(1);
}

// Database configuration
define('DB_HOST', $_ENV["DB_SERVER"]);
define('DB_USER', $_ENV["DB_USER"]);
define('DB_PASS', $_ENV["DB_PASSWORD"]);
define('DB_NAME', 'bubblemaps');



/**
 * Get database connection
 */
function getDatabaseConnection() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    return $conn;
}

/**
 * Validate IP address format
 */
function isValidIP($ip) {
    return filter_var($ip, FILTER_VALIDATE_IP) !== false;
}

/**
 * Run netdisco discovery command for a given IP
 */
function runNetdiscoDiscovery($ip) {
    // Validate IP address
    if (!isValidIP($ip)) {
        echo "  Warning: Invalid IP address format: $ip\n";
        return false;
    }
    
    // Skip localhost/127.0.0.1
    if ($ip === '127.0.0.1' || $ip === 'localhost') {
        echo "  Skipping localhost IP: $ip\n";
        return true;
    }
    
    $command = "sudo -u netdisco /home/<USER>/bin/netdisco-do discover -d $ip --enqueue";
    
    echo "  Running: $command\n";
    
    // Execute the command
    $output = [];
    $returnCode = 0;
    
    exec($command . " 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "  Success: Discovery command enqueued for $ip\n";
        return true;
    } else {
        echo "  Error: Discovery command failed for $ip (return code: $returnCode)\n";
        echo "  Output: " . implode("\n    ", $output) . "\n";
        return false;
    }
}

/**
 * Process all hosts in the database
 */
function processAllHosts() {
    $conn = getDatabaseConnection();
    
    // Get all IPs from hosts table, excluding blacklisted hosts, External subnets, and localhost
    $query = "SELECT DISTINCT ip FROM hosts WHERE blacklist = 0 AND ip IS NOT NULL AND ip != '' AND subnet != 'External' AND subnet != 'localhost'";
    $result = $conn->query($query);
    
    if (!$result) {
        error_log("Query failed: " . $conn->error);
        $conn->close();
        return;
    }
    
    $totalHosts = $result->num_rows;
    $processed = 0;
    $errors = 0;
    $skipped = 0;
    
    echo "Starting to process $totalHosts unique IPs\n";
    
    while ($row = $result->fetch_assoc()) {
        $ip = $row['ip'];
        
        echo "Processing IP: $ip\n";
        
        try {
            // Run netdisco discovery for this IP
            if (runNetdiscoDiscovery($ip)) {
                $processed++;
            } else {
                $errors++;
            }
        } catch (Exception $e) {
            echo "  Error processing IP $ip: " . $e->getMessage() . "\n";
            error_log("Error processing IP $ip: " . $e->getMessage());
            $errors++;
        }
        
        // Add a small delay between commands to avoid overwhelming the system
        usleep(100000); // 100ms delay
    }
    
    $conn->close();
    
    echo "\nSummary:\n";
    echo "  Total IPs processed: $totalHosts\n";
    echo "  Successfully enqueued: $processed\n";
    echo "  Errors: $errors\n";
    echo "  Skipped: $skipped\n";
}

// Script start

// Run the main function
try {
    processAllHosts();
    echo "Script completed successfully.\n";
} catch (Exception $e) {
    // Handle any errors that occur during execution
    echo "Error: " . $e->getMessage() . "\n";
}
