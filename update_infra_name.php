<?php
include "loadenv.php";
$servername = $_ENV["DB_SERVER"];
$username = $_ENV["DB_USER"];
$password = $_ENV["DB_PASSWORD"]; 
$dbname = "bubblemaps";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$id = $_POST['id'];
$newName = $_POST['newName'];

// Get the old name
$sql = "SELECT name FROM infra WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $id);
$stmt->execute();
$stmt->bind_result($oldName);
$stmt->fetch();
$stmt->close();

// Update infra table
$sql = "UPDATE infra SET name = ? WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('si', $newName, $id);

if ($stmt->execute()) {
    // Update hosts table
    $sql = "UPDATE hosts SET infra = ? WHERE infra = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ss', $newName, $oldName);
    if ($stmt->execute()) {
        // Update subnets table
        $sql = "UPDATE subnets SET infra = ? WHERE infra = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('ss', $newName, $oldName);
        if ($stmt->execute()) {
            echo "Success";
        } else {
            echo "Error updating subnets: " . $stmt->error;
        }
    } else {
        echo "Error updating hosts: " . $stmt->error;
    }
} else {
    echo "Error updating infra: " . $stmt->error;
}

$stmt->close();
$conn->close();
?>
