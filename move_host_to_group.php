<?php
include "loadenv.php";
// Configuration
define('NAGIOS_USER', $_ENV['NAGIOS_USER']);
define('NAGIOS_PASS', $_ENV['NAGIOS_PASS']);

// Database connection for Nagios
$dbHost = $_ENV["DB_SERVER"];
$dbName = 'db_nagiosql_v3';
$dbUser = $_ENV["DB_USER"];
$dbPass = $_ENV['DB_PASSWORD'];

// Bubblemaps database has same credentials but different name
$bubbleMapsDbName = 'bubblemaps';

try {
    // Get the IP(s) and host group ID from the POST request
    if (!isset($_POST['hostGroupId'])) {
        throw new Exception('Missing hostGroupId parameter');
    }

    $hostGroupId = $_POST['hostGroupId'];
    
    // Handle both single IP and multiple IPs
    $ips = [];
    if (isset($_POST['ip'])) {
        // Single IP (backward compatibility)
        $ips = [$_POST['ip']];
    } elseif (isset($_POST['ips'])) {
        // Multiple IPs
        $ips = is_array($_POST['ips']) ? $_POST['ips'] : json_decode($_POST['ips'], true);
        if (!is_array($ips)) {
            throw new Exception('Invalid IPs format');
        }
    } else {
        throw new Exception('Missing ip or ips parameter');
    }

    if (empty($ips)) {
        throw new Exception('No IPs provided');
    }

    // Connect to the Nagios database
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $successCount = 0;
    $errorCount = 0;
    $totalDeletedRows = 0;
    $processedHosts = [];
    $errors = [];

    // Process each IP
    foreach ($ips as $ip) {
        try {
            // Get the host ID from the IP address
            $stmt = $pdo->prepare("SELECT id FROM tbl_host WHERE address = :ip");
            $stmt->bindParam(':ip', $ip);
            $stmt->execute();
            
            $hostResult = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$hostResult) {
                $errors[] = "Host with IP $ip not found";
                $errorCount++;
                continue;
            }
            
            $hostId = $hostResult['id'];

            // First, delete any existing relationships for this host
            $deleteStmt = $pdo->prepare("DELETE FROM tbl_lnkHostToHostgroup WHERE idMaster = :hostId");
            $deleteStmt->bindParam(':hostId', $hostId);
            $deleteStmt->execute();
            $deletedRows = $deleteStmt->rowCount();
            $totalDeletedRows += $deletedRows;

            // Now insert the new relationship
            $insertStmt = $pdo->prepare("INSERT INTO tbl_lnkHostToHostgroup (idMaster, idSlave) VALUES (:hostId, :hostGroupId)");
            $insertStmt->bindParam(':hostId', $hostId);
            $insertStmt->bindParam(':hostGroupId', $hostGroupId);
            $insertStmt->execute();
            
            $processedHosts[] = $ip;
            $successCount++;
            
        } catch (Exception $e) {
            $errors[] = "Error processing IP $ip: " . $e->getMessage();
            $errorCount++;
        }
    }

    // Build success message
    $message = "";
    if ($successCount > 0) {
        if ($totalDeletedRows > 0) {
            $message .= "Removed $totalDeletedRows existing host group relationships and ";
        }
        $message .= "Successfully moved $successCount host(s) to host group";
    }
    
    if ($errorCount > 0) {
        if ($successCount > 0) {
            $message .= ", but encountered $errorCount error(s)";
        } else {
            $message = "Failed to move any hosts - $errorCount error(s) occurred";
        }
    }
        
    // Run the verify actions only if we had some success
    if ($successCount > 0) {
        simulateVerifyActions(getSelfIp());
    }

    // Now update the bubblemaps database for successful hosts
    if (!empty($processedHosts)) {
        try {
            // Fetch the host group name from db_nagiosql_v3
            $stmt = $pdo->prepare("SELECT alias FROM tbl_hostgroup WHERE id = :hostGroupId");
            $stmt->bindParam(':hostGroupId', $hostGroupId);
            $stmt->execute();
            $hostGroupResult = $stmt->fetch(PDO::FETCH_ASSOC);
        
            if (!$hostGroupResult) {
                throw new Exception("Host group with ID $hostGroupId not found");
            }
        
            $hostGroupName = $hostGroupResult['alias'];
            $hostGroupJson = json_encode([$hostGroupName]); // Convert to JSON array
        
            // Connect to the bubblemaps database
            $bubblePdo = new PDO("mysql:host=$dbHost;dbname=$bubbleMapsDbName", $dbUser, $dbPass);
            $bubblePdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
            $bubbleUpdateCount = 0;
            foreach ($processedHosts as $ip) {
                // Update the hostgroup in bubblemaps database
                $updateBubbleStmt = $bubblePdo->prepare("UPDATE hosts SET hostgroup = :hostGroupJson WHERE ip = :ip");
                $updateBubbleStmt->bindParam(':hostGroupJson', $hostGroupJson);
                $updateBubbleStmt->bindParam(':ip', $ip);
                $updateBubbleStmt->execute();
                
                if ($updateBubbleStmt->rowCount() > 0) {
                    $bubbleUpdateCount++;
                }
            }
        
            if ($bubbleUpdateCount > 0) {
                $message .= " and updated $bubbleUpdateCount host(s) in bubblemaps database";
            } else {
                $message .= " but no hosts found in bubblemaps database";
            }
        } catch (Exception $e) {
            error_log("Error updating bubblemaps database: " . $e->getMessage());
            $message .= " but failed to update bubblemaps database: " . $e->getMessage();
        }
    }
    
    // Determine overall success
    $overallSuccess = $successCount > 0;
    
    echo json_encode([
        'success' => $overallSuccess, 
        'message' => $message,
        'details' => [
            'successCount' => $successCount,
            'errorCount' => $errorCount,
            'totalProcessed' => count($ips),
            'errors' => $errors
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Error in move_host_to_group.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * Get the server's IP address
 */
function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

/**
 * Simulate verify actions in NagiosQL
 */
function simulateVerifyActions($ip) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    
    try {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
        ]);

        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => NAGIOS_USER,
            'tfPassword' => NAGIOS_PASS,
            'Submit' => 'Login'
        ]));
        
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception('Login failed: ' . curl_error($ch));
        }
        error_log("Login response code: " . curl_getinfo($ch, CURLINFO_HTTP_CODE));

        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            error_log("Redirecting to: $redirect");
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }

        curl_setopt($ch, CURLOPT_URL, "https://$ip/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                throw new Exception("Action $action failed: " . curl_error($ch));
            }
            error_log("Action $action completed with response code: " . curl_getinfo($ch, CURLINFO_HTTP_CODE));
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log("Error in simulateVerifyActions: " . $e->getMessage());
    }
}
?>