<?php
include "loadenv.php";

class WindowsWMIScanner {
    private $host;
    private $outputMessages = [];
    private $windows_ports = [135, 139, 445, 3389];
    private $wmi_script_path = '/usr/lib64/nagios/plugins/check_wmi_plus.pl';
    private $wmi_users = [];
    private $db_connection;

    public function __construct($host) {
        $this->host = $host;
        $this->loadWmiUsers();
    }

    // Load WMI users from database
    private function loadWmiUsers() {
        try {
            $this->db_connection = $this->getDatabaseConnection();
            $sql = "SELECT id FROM cwmi";
            $result = $this->db_connection->query($sql);

            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $this->wmi_users[] = 'user' . $row['id'];
                }
            } else {
                // Fallback to default user if no users found in database
                $this->wmi_users[] = 'user1';
            }
            $this->db_connection->close();
        } catch (Exception $e) {
            // Fallback to default user if database connection fails
            $this->wmi_users[] = 'user1';
        }
    }

    // Database connection
    private function getDatabaseConnection() {
        $servername = $_ENV["DB_SERVER"];
        $username = $_ENV["DB_USER"];
        $password = $_ENV["DB_PASSWORD"] ?? ''; 
        $dbname = "ndd";

        $conn = new mysqli($servername, $username, $password, $dbname);

        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }

        return $conn;
    }

    // Check if IP is valid
    public function isValidIp() {
        return filter_var($this->host, FILTER_VALIDATE_IP) !== false;
    }

    // Check if network firewall allows pings using fping
    public function isNetworkPingAllowed() {
        // First, try pinging the specific host
        $command = "/usr/sbin/fping " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output);
        // Check if the output contains "is alive"
        foreach ($output as $line) {
            if (strpos($line, "is alive") !== false) {
                return true; // Host responded
            }
        }

        // Extract the subnet and scan the range if the host didn't respond
        $ipParts = explode('.', $this->host);
        if (count($ipParts) !== 4) {
            return false; // Invalid IP format
        }
        
        $subnetPrefix = "{$ipParts[0]}.{$ipParts[1]}.{$ipParts[2]}";
        $scanRange = "{$subnetPrefix}.2 {$subnetPrefix}.253";

        // Scan the subnet range
        $scanCommand = "/usr/sbin/fping -r 0 -g $scanRange 2>&1";
        $scanOutput = [];
        exec($scanCommand, $scanOutput);

        // Check if any host in the range responded
        foreach ($scanOutput as $line) {
            if (strpos($line, "is alive") !== false) {
                return true;
            }
        }

        return false; // No hosts responded, likely blocked
    }

    // Check if device firewall responds to pings
    public function isPingResponsive() {
        // Run nmap with sudo; -Pn disables host discovery so the scan is performed even if ping is disabled on network level
        $command = "sudo nmap " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output, $exitCode);
        $result = implode("\n", $output);
        
        // Check if nmap worked by verifying exit code and looking for a scan report in the output
        if ($exitCode === 0 && strpos($result, "Nmap scan report for") !== false) {
            return true;
        }
        
        return false;
    }

    // Check if host appears to be Windows based on TTL
    public function isWindowsHost() {
        $command = "ping -c 1 " . escapeshellarg($this->host);
        $output = [];
        exec($command, $output, $exitCode);

        if ($exitCode !== 0) {
            return false;
        }

        foreach ($output as $line) {
            if (preg_match('/ttl=(\d+)/i', $line, $matches)) {
                $ttl = intval($matches[1]);
                return $ttl > 100 && $ttl <= 128; // Windows TTL is typically 128
            }
        }
        return false;
    }

    // Check if WMI ports are open
    public function areWmiPortsOpen() {
        $openPorts = [];
        
        // Define the well-known WMI ports + dynamic range
        $ports = implode(',', array_merge($this->windows_ports, range(1024, 65535, 500))); // Step scan every 500 ports
        
        // Run the Nmap scan on the specified ports
        $command = "nmap -p " . escapeshellarg($ports) . " -Pn " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output, $exitCode);
        $result = implode("\n", $output);
        
        // Check if any of the specified ports are open
        foreach (explode(',', $ports) as $port) {
            if (strpos($result, "$port/tcp open") !== false) {
                $openPorts[] = $port;
            }
        }
    
        return count($openPorts) > 0; // Return true if at least one WMI-related port is open
    }

    // Execute WMI check with multiple users
    public function executeWmiCheck() {
        $success = false;
        
        // Try standard WMI checks first
        foreach ($this->wmi_users as $wmi_user) {
            $command = "{$this->wmi_script_path} -H " . escapeshellarg($this->host) . 
                      " -u " . escapeshellarg($wmi_user) . " -m checkuptime -t 10 2>&1";
            $output = [];
            exec($command, $output, $exitCode);

            if ($exitCode === 0 && !empty($output)) {
                // DEBUG: $this->outputMessages[] = "✓ Successfully connected with user: {$wmi_user}";
                return true;
            }
        }
        
        // If all standard checks fail, try legacy method
        return $this->executeLegacyWmiCheck();
    }
    
    // Execute WMI check using legacy method as fallback
    private function executeLegacyWmiCheck() {
        // Try for each user ID
        foreach ($this->wmi_users as $wmi_user) {
            // Extract user ID from username (e.g., "user1" -> 1)
            if (preg_match('/user(\d+)/', $wmi_user, $matches)) {
                $id = (int)$matches[1];
                
                // Set config file path based on ID
                $cfg_file = '/etc/nagios/wmiauth';
                if ($id > 1) {
                    $cfg_file = $cfg_file . $id;
                }
                
                // Execute legacy WMI check
                $command = "{$this->wmi_script_path} -H " . escapeshellarg($this->host) . 
                          " -A " . escapeshellarg($cfg_file) . " -m checkuptime --legacywmicclient 2>&1";
                $output = [];
                exec($command, $output, $exitCode);
                
                if ($exitCode === 0 && !empty($output)) {
                   // DEBUG: $this->outputMessages[] = "✓ Successfully connected using legacy method with config: {$cfg_file}";
                    return true;
                }
            }
        }
        
        return false;
    }

    // Generate output messages
    public function generateOutput() {
        $pingsPassed = true;

        // Check Network firewall
        if($this->isNetworkPingAllowed()){
            $this->outputMessages[] = "✓ The firewall on the network lets pings pass to {$this->host}";
        } else {
            $this->outputMessages[] = "✗ The firewall on the network does not let pings pass to {$this->host}";
            return implode("\n", $this->outputMessages);
        }

        // Ping check
        if ($this->isPingResponsive()) {
            $this->outputMessages[] = "✓ The device responds to pings.";
        } else {
            $this->outputMessages[] = "✗ The device does not respond to pings. ";
            return implode("\n", $this->outputMessages);
        }

        // Windows host check
        if ($this->isWindowsHost()) {
            $this->outputMessages[] = "✓ The host {$this->host} appears to be a Windows machine (based on TTL).";
        } else {
            $this->outputMessages[] = "✗ The host {$this->host} does not appear to be a Windows machine (based on TTL).";
            $this->outputMessages[] = "• Scan aborted: This tool is designed for Windows hosts only.";
            return implode("\n", $this->outputMessages);
        }

        // WMI ports check
        if ($this->areWmiPortsOpen()) {
            $this->outputMessages[] = "✓ WMI-related ports are open on {$this->host}.";
            
            // WMI check
            if ($this->executeWmiCheck()) {
                $this->outputMessages[] = "✓ WMI check executed successfully.";
                $this->outputMessages[] = "✓ WMI credentials are valid and working.";
            } else {
                $this->outputMessages[] = "✗ WMI check failed to execute.";
                $this->outputMessages[] = "✗ WMI credentials may be invalid or service unavailable.";
            }
        } else {
            $this->outputMessages[] = "✗ WMI-related ports are not open on {$this->host}.";
            $this->outputMessages[] = "✗ WMI check cannot be performed due to blocked ports.";
            $this->outputMessages[] = "✗ WMI credentials status cannot be determined.";
        }

        return implode("\n", $this->outputMessages);
    }

    // Main scanning function
    public function scan() {
        if (!$this->isValidIp()) {
            return "✗ Invalid IP address provided\n• Use a valid IP like ***********";
        }

        return $this->generateOutput();
    }
}

// Usage
if (isset($_GET['host'])) {
    $scanner = new WindowsWMIScanner($_GET['host']);
    echo htmlspecialchars($scanner->scan());
} else {
    echo "✗ No host provided\n• Add a host parameter, e.g., ?host=*******";
}
?>