<?php
include 'language_selector.php';

include 'theme_loader.php'; // Include the theme loader

$link = "infra.php";  // Default fallback link
$text = "Undefined";  // Default fallback text
$textInfra = "Infrastructure";  // Default infra text

// Check URL parameters and set breadcrumb values
if (isset($_GET['hostgroup']) && !empty($_GET['hostgroup'])) {
    if (isset($_GET['subnetNickname']) && !empty($_GET['subnetNickname'])) {
        // Case 1: Both hostgroup and subnetNickname are present (4 tabs)
        $homeLink = "infra.php";
        
        // If we're in All Hosts view, maintain that in infraLink
        if (isset($_GET['subnet']) && $_GET['subnet'] === 'all' && isset($_GET['subnetNickname']) && $_GET['subnetNickname'] === 'All Hosts') {
            $infraLink = "hosts.php?subnet=all&subnetNickname=All Hosts&infra=" . urlencode($_GET['infra'] ?? 'default');
        } else {
            // Otherwise link to subnets view
            $infraLink = "subnets.php?infra=" . urlencode($_GET['infra'] ?? 'default') . '&subnet=true';
        }

        // Current URL without hostgroup parameter for subnet link
        $currentUrl = $_SERVER['REQUEST_URI'];
        $subnetLink = preg_replace('/&?hostgroup=[^&]*/', '', $currentUrl);
        $subnetLink = preg_replace('/\?$/', '', $subnetLink); // Clean up trailing ? if exists

        $textInfra = htmlspecialchars($_GET['infra'] ?? 'Infrastructure');
        $textSubnet = htmlspecialchars($_GET['subnetNickname']);
        $textHostgroup = htmlspecialchars($_GET['hostgroup']);
    } else {
        // Case 2: Only hostgroup is present (current functionality)
        $link = "hosts.php?subnet=all&subnetNickname=All Hosts";
        if (isset($_GET['infra']) && !empty($_GET['infra'])) {
            $link .= "&infra=" . urlencode($_GET['infra']);
        }
        $text = htmlspecialchars($_GET['hostgroup']);
        $textInfra = htmlspecialchars('All Hosts');
    }
} elseif (isset($_GET['infra']) && !empty($_GET['infra'])) {
    // Case 3: Only infra is present
    if (isset($_GET['subnet']) && $_GET['subnet'] === 'all' && isset($_GET['subnetNickname']) && $_GET['subnetNickname'] === 'All Hosts') {
        // We're in All Hosts view, link back to All Hosts view for the infra
        $link = "hosts.php?subnet=all&subnetNickname=All Hosts&infra=" . urlencode($_GET['infra']);
    } else {
        // We're in Subnets view, link to Subnets
        $link = "subnets.php?infra=" . urlencode($_GET['infra']) . '&subnet=true';
    }
    $text = htmlspecialchars($_GET['subnetNickname'] ?? 'Undefined');
    $textInfra = htmlspecialchars($_GET['infra']);
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Blesk</title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/hostlist.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <script src="functions/d3.v7.min.js"></script>
    <script src="functions/fetchHostGroups.js"></script>
    <script src="functions/fetchHostsBubbles.js"></script>
    <script src="functions/helperFunctions.js"></script>
    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <script src="functions/helperFunctions/advancedSettingsModal.js"></script>
    <script src="functions/background_beacon.js"></script>
    <script src="functions/host_approval.js"></script>
    <script src="functions/service_approval.js"></script>
    <script src="functions/hostlistPhpFunctions/hostListView.js"></script>
</head>

<body>
    <header>
        <div style="display: flex; align-items: center; gap: 15px;">
            <a href="../../index.php">
                <img style="width: 25px;" src="../../images/blesk-favicon.svg"
                    alt="Home">
            </a>
            <div class="breadcrumbs">
                <!-- Home links to infra.php with view parameter based on current view -->
                <a href="infra.php<?php echo (isset($_GET['subnet']) && $_GET['subnet'] === 'all' && isset($_GET['subnetNickname']) && $_GET['subnetNickname'] === 'All Hosts') ? '' : '?subnet=true'; ?>">
                    <i class="fa fa-home"></i> Home
                </a>
                <span class="separator">/</span>

                <?php if (isset($_GET['hostgroup']) && isset($_GET['subnetNickname']) && !empty($_GET['subnetNickname'])): ?>
                    <!-- Case with 4 tabs: Home → Infra → Subnet → Hostgroup -->
                    <a href="<?php echo $infraLink; ?>">
                        <i class="fa fa-sitemap"></i> <?php echo $textInfra; ?>
                    </a>
                    <span class="separator">/</span>
                    <a href="<?php echo htmlspecialchars($subnetLink); ?>">
                        <i class="fa fa-network-wired"></i> <?php echo $textSubnet; ?>
                    </a>
                    <span class="separator">/</span>
                    <span class="current">
                        <i class="fa fa-sitemap"></i> <?php echo $textHostgroup; ?>
                    </span>
                <?php else: ?>
                    <!-- Original cases with 3 tabs -->
                    <a href="<?php echo $link; ?>">
                        <i class="fa fa-sitemap"></i> <?php echo $textInfra; ?>
                    </a>
                    <span class="separator">/</span>
                    <span class="current">
                        <i class="fa fa-network-wired"></i> <?php echo $text; ?>
                    </span>
                <?php endif; ?>
            </div>
            
            <!-- Simple APM Progress Indicator -->
            <div id="apm-progress-simple" class="apm-progress-simple" title="Updating ALL hosts status..." style="display: none;">
                <i class="fa fa-refresh fa-spin"></i>
                <span id="apm-progress-count">0/0</span>
            </div>
            
            <!-- Feature Status Indicators -->
            <div class="hostlist-status-indicators">
                <div class="status-indicator" id="feature-flap-detection-status" title="Flap Detection (Hosts & Services)">
                    <i class="fa fa-flag"></i>
                    <span class="indicator-badge"></span>
                </div>
                <div class="status-indicator" id="feature-notifications-status" title="Notifications (Hosts & Services)">
                    <i class="fa fa-bell"></i>
                    <span class="indicator-badge"></span>
                </div>
                <div class="status-indicator" id="feature-event-handlers-status" title="Event Handlers (Hosts & Services)">
                    <i class="fa fa-cogs"></i>
                    <span class="indicator-badge"></span>
                </div>
                <div class="status-indicator" id="feature-active-checks-status" title="Active Checks (Hosts & Services)">
                    <i class="fa fa-check-circle"></i>
                    <span class="indicator-badge"></span>
                </div>
                <div class="status-indicator" id="feature-passive-checks-status" title="Passive Checks (Hosts & Services)">
                    <i class="fa fa-eye"></i>
                    <span class="indicator-badge"></span>
                </div>
                <div class="status-indicator" id="feature-acknowledged-problems-status" title="Acknowledged Problems (Hosts & Services)">
                    <i class="fa fa-gavel"></i>
                    <span class="indicator-badge"></span>
                </div>
            </div>
        </div>

        <div class="header-content">
            <div class="hamburger">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </div>
            <div class="header-buttons">
                <div class="toggle-container">
                    <div class="toggle-switch <?php echo (isset($_GET['subnet']) && $_GET['subnet'] !== 'all') || (isset($_GET['subnetNickname']) && $_GET['subnetNickname'] !== 'All Hosts') ? 'right' : 'left'; ?>">
                        <a
                            href="<?php echo (isset($_GET['subnet']) && $_GET['subnet'] !== 'all') || (isset($_GET['subnetNickname']) && $_GET['subnetNickname'] !== 'All Hosts') ? 'hosts.php?subnet=all&subnetNickname=All Hosts&infra=' . urlencode($_GET['infra'] ?? '') : 'subnets.php?infra=' . urlencode($_GET['infra'] ?? '') . '&subnet=true'; ?>">
                            <span class="toggle-option left-option">All Hosts</span>
                            <span class="toggle-option right-option">Subnets View</span>
                            <div class="toggle-slider"></div>
                        </a>
                    </div>
                </div>
                <div class="search-container">
                    <input type="text" id="searchBar" placeholder="Filter by name..." oninput="filterBubbles()">
                    <select id="searchMode" onchange="filterBubbles()">
                        <option value="hosts" selected>By Hosts</option>
                        <option value="hostgroups">By Hostgroups</option>
                    </select>
                </div>
                <a class="header-button" href="hostlist.php<?php 
                    // Only pass hostgroup parameter if present
                    if (isset($_GET['hostgroup']) && !empty($_GET['hostgroup'])) {
                        echo '?hostgroup=' . urlencode($_GET['hostgroup']);
                    }
                ?>"><i class="fa fa-list"></i> List View</a>
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="menuDropdownBtn"><i class="fa fa-bars"></i> Control Panel <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="menuDropdownContent">
                        <a class="header-button" id="formModal-button"><i class="fa fa-search"></i> Scan</a>
                        <a class="header-button" onclick='showModal("https://<?php echo $_SERVER["HTTP_HOST"]; ?>/bubblemaps/credentials.php")'><i class="fa fa-key"></i> Credentials</a>
                        <a class="header-button" onclick='showModal("https://<?php echo $_SERVER["HTTP_HOST"]; ?>/nagvis/frontend/nagvis-js/index.php")'><i class="fa fa-map"></i> Network Map</a>
                        <a class="header-button" href="reports.php"><i class="fa fa-file-text"></i> Reports</a>
                    </div>
                </div>
                <a class="header-button" id="openSettingsBtn"><i class="fa fa-cog"></i> Advanced Settings</a>
                <!-- Dropdown for language selection -->
            </div>
        </div>
    </header>
    
    <!-- Mobile Status Container (hidden by default, shown via JS on mobile) -->
    <div class="mobile-status-container" id="mobile-status-container">
        <!-- APM Progress Indicator for mobile -->
        <div id="apm-progress-mobile" class="apm-progress-mobile" title="Updating ALL hosts status..." style="display: none;">
            <i class="fa fa-refresh fa-spin"></i>
            <span id="apm-progress-count-mobile">0/0</span>
        </div>
        
        <!-- Feature Status Indicators for mobile -->
        <div class="hostlist-status-indicators">
            <div class="status-indicator" id="feature-flap-detection-status-mobile" title="Flap Detection (Hosts & Services)">
                <i class="fa fa-flag"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-notifications-status-mobile" title="Notifications (Hosts & Services)">
                <i class="fa fa-bell"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-event-handlers-status-mobile" title="Event Handlers (Hosts & Services)">
                <i class="fa fa-cogs"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-active-checks-status-mobile" title="Active Checks (Hosts & Services)">
                <i class="fa fa-check-circle"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-passive-checks-status-mobile" title="Passive Checks (Hosts & Services)">
                <i class="fa fa-eye"></i>
                <span class="indicator-badge"></span>
            </div>
            <div class="status-indicator" id="feature-acknowledged-problems-status-mobile" title="Acknowledged Problems (Hosts & Services)">
                <i class="fa fa-gavel"></i>
                <span class="indicator-badge"></span>
            </div>
        </div>
    </div>
    
    <!-- Feature Issue Popover -->
    <div id="feature-issue-popover" class="status-hosts-popover" style="display: none;">
        <h4 id="popover-title">Feature Issues</h4>
        <span class="close-popover" onclick="document.getElementById('feature-issue-popover').style.display = 'none'">&times;</span>
        <ul id="popover-list">
            <!-- Populated by JavaScript -->
        </ul>
    </div>
    
    <?php include "settingsModal.php"; ?>
    <div class="loader-container" id="loader-container">
        <div class="spinner">
            <div class="spinner-dot"></div>
            <div class="spinner-particle"></div>
            <div class="spinner-particle"></div>
            <div class="spinner-particle"></div>
            <div class="spinner-particle"></div>
        </div>
        <div class="loading-text">Loading Please Wait...</div>
    </div>
    <div id="context-menu" class="context-menu">
        <div class="context-menu-title"></div>
        <ul>
            <li id="rename">Edit Name</li>
            <li id="view">Open Details</li>
            <li id="show-links">Filter by Connections</li>
            <li id="move-to">Relocate to
                <ul class="submenu">
                </ul>
            </li>
            <li id="delete">Remove Host</li>
        </ul>
    </div>
    <div id="canvas-container">
        <svg id="map"></svg>
    </div>
    <div id="infoModal" class="iframeModal">
        <div class="iframeModal-content" id="iframeModal-content">
            <span class="iframeMclose">×</span>
            <div class="iframe-loader"></div>
            <iframe id="modal-frame"></iframe>
        </div>
    </div>
    <!-- Scan Modal -->
    <div id="formModal" class="formModal">
        <div class="formModal-content">
            <span class="formModal-close">&times;</span>
            <form class="modal-form" id="scanForm" action="scan.php" method="GET">
                <div class="form-group">
                    <label for="infra">Add host(s) to:</label>
                    <select id="infra" name="infra" class="form-control" required>
                        <option value="">Loading...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="ip">Enter a single ip, ip range or url.</label>
                    <input type="text" id="ip" name="ip" class="form-control" required>
                </div>
                <div class="form-group">
                    <label title="If checked, addresses outside RFC 1918 (e.g., 128.1.x.x) will be treated as internal and stored under their own subnet instead of 'External'."><input type="checkbox" id="internal" name="internal" value="yes"> Treat as internal (non-RFC1918)</label>
                </div>
                <input type="hidden" name="forceScan" value="yes">
                <button type="submit" class="btn-submit">Submit</button>
            </form>
        </div>
    </div>
    <script src="functions/hostsPhpFunctions/modals.js"></script>
    <script src="functions/hostsPhpFunctions/contextMenu.js"></script>
    <script src="functions/hostsPhpFunctions/filtering.js"></script>
    <script src="functions/hostsPhpFunctions/hostCount.js"></script>
    <script src="functions/hostsPhpFunctions/featureStatusIndicators.js"></script>
    <script src="functions/hostsPhpFunctions/toggleConnections.js"></script>
    <script>
        // Constants and Global Variables
        const urlParams = new URLSearchParams(window.location.search);
        const dictionaries = <?php echo json_encode($dictionaries); ?>;
        const selectedLang = '<?php echo $selectedLang; ?>';
        let currentBubble = null;
        const modalBody = document.getElementById('iframeModal-content');
        // Store the original inline styles of modalBody
        const originalStyles = modalBody.style.cssText;

        // Add mobile status container styles
        const mobileStyles = document.createElement('style');
        mobileStyles.innerHTML = `
            /* Mobile Status Container Styles */
            .mobile-status-container {
                display: none;
                flex-direction: column;
                padding: 8px 15px;
                border-bottom: 1px solid #555;
                position: relative;
                z-index: 8;
            }
            
            /* Only show the mobile status container in mobile view */
            @media (max-width: 768px) {
                .mobile-status-container {
                    display: flex;
                    gap: 10px;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                }
                
                /* Make sure header doesn't show these elements on mobile */
                header .hostlist-status-filters-header,
                header .hostlist-status-indicators {
                    display: none;
                }
                
                /* APM Progress for mobile */
                .apm-progress-mobile {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.8);
                    margin-bottom: 5px;
                }
                
                .apm-progress-mobile i {
                    color: #ccc;
                    font-size: 13px;
                    margin-right: 2px;
                }
            }
        `;
        document.head.appendChild(mobileStyles);

        const isTouchDevice = () => {
            return ('ontouchstart' in window) ||
                (navigator.maxTouchPoints > 0) ||
                (navigator.msMaxTouchPoints > 0);
        };

        // API Calls
        const updateBubbleName = async (id, newName) => {
            try {
                const response = await fetch('update_host_name.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `id=${id}&newName=${newName}`
                });
                const result = await response.text();
                if (result !== 'Success') throw new Error(`Update failed: ${result}`);
            } catch (error) {
                console.error('Error updating bubble name:', error);
            }
        };

        // Header and Layout
        const setupHeader = () => {
            const hamburger = document.querySelector('.hamburger');
            const headerButtons = document.querySelector('.header-buttons');

            hamburger.addEventListener('click', (e) => {
                e.stopPropagation();
                hamburger.classList.toggle('active');
                headerButtons.classList.toggle('active');
            });

            document.addEventListener('click', (event) => {
                if (!event.target.closest('.header-content') || event.target.id === 'scan-button') {
                    hamburger.classList.remove('active');
                    headerButtons.classList.remove('active');
                }
            });

            window.addEventListener('load', () => {
                const headerHeight = document.querySelector('header').offsetHeight;
                const loaderContainer = document.querySelector('.loader-container');
                loaderContainer.style.top = `${headerHeight}px`;
                loaderContainer.style.height = `calc(100% - ${headerHeight}px)`;
            });

            window.addEventListener('resize', () => window.location.reload());
        };

        // Initialization
        const init = () => {
            document.addEventListener('DOMContentLoaded', () => {
                setupModals();
                setupScanForm();
                setupContextMenu();
                setupHeader();
                populateHostGroupSubmenu();
                
                // Initialize feature status indicators
                initFeatureStatusIndicators();

                const translator = new Translator(dictionaries, selectedLang);
                translator.init();
                initTranslationObserver(translator); // Initialize the observer directly
                getInfrasNames();
                fetchHostsBubbles();
            });
        };

        // Start Application
        init();
        document.querySelector('.loader-container').style.display = 'none';

        // Fallback in case the host_approval.js script fails to load
        if (typeof initHostApproval !== 'function') {
            function initHostApproval() {
                console.log('Host approval module not loaded. Using fallback function.');
            }
        }

        // Fallback in case the service_approval.js script fails to load
        if (typeof initServiceApproval !== 'function') {
            function initServiceApproval() {
                console.log('Service approval module not loaded. Using fallback function.');
            }
        }

        // Initialize host approval notifications
        const infraParam = urlParams.get('infra');
        initHostApproval(infraParam);
        
        // Initialize service approval notifications
        initServiceApproval(infraParam);

        // Helper function to check if we're on mobile
        function isMobile() {
            return window.innerWidth <= 768;
        }

        function checkApmProgress() {
            fetch('src/settingsphp/runUpdateApmStatus.php?action=progress')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    try {
                        const data = JSON.parse(text);
                        
                        // Get both desktop and mobile indicators
                        const simpleIndicator = document.getElementById('apm-progress-simple');
                        const mobileIndicator = document.getElementById('apm-progress-mobile');
                        const countText = document.getElementById('apm-progress-count');
                        const countTextMobile = document.getElementById('apm-progress-count-mobile');

                        // Determine which indicator to show based on screen size
                        const showOnMobile = isMobile();
                        const activeIndicator = showOnMobile ? mobileIndicator : simpleIndicator;
                        const inactiveIndicator = showOnMobile ? simpleIndicator : mobileIndicator;
                        const activeCountText = showOnMobile ? countTextMobile : countText;

                        // Always show the appropriate indicator if we have any data
                        if (data.total > 0 || data.status === 'running' || data.status === 'completed') {
                            if (activeIndicator) {
                                activeIndicator.style.display = 'flex';
                                if (showOnMobile) {
                                    activeIndicator.classList.add('show');
                                }
                            }
                            if (inactiveIndicator) {
                                inactiveIndicator.style.display = 'none';
                                if (showOnMobile) {
                                    inactiveIndicator.classList.remove('show');
                                }
                            }
                            
                            if (activeCountText) {
                                if (data.status === 'running') {
                                    activeCountText.textContent = data.current + '/' + data.total;
                                } else if (data.status === 'completed') {
                                    activeCountText.textContent = data.total + '/' + data.total + ' ✓';
                                } else {
                                    activeCountText.textContent = data.current + '/' + data.total;
                                }
                            }
                        } else if (data.status && data.status.startsWith('error')) {
                            if (activeIndicator) {
                                activeIndicator.style.display = 'flex';
                                if (showOnMobile) {
                                    activeIndicator.classList.add('show');
                                }
                            }
                            if (inactiveIndicator) {
                                inactiveIndicator.style.display = 'none';
                                if (showOnMobile) {
                                    inactiveIndicator.classList.remove('show');
                                }
                            }
                            if (activeCountText) {
                                activeCountText.textContent = 'Error';
                            }
                        } else {
                            // Only hide if truly idle with no data
                            if (data.status === 'idle' && data.total === 0) {
                                if (activeIndicator) {
                                    activeIndicator.style.display = 'none';
                                }
                                if (inactiveIndicator) {
                                    inactiveIndicator.style.display = 'none';
                                }
                                if (showOnMobile) {
                                    if (activeIndicator) activeIndicator.classList.remove('show');
                                    if (inactiveIndicator) inactiveIndicator.classList.remove('show');
                                }
                            }
                        }
                    } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                    }
                })
                .catch(error => {
                    console.error('Error checking APM progress:', error);
                });
        }

        // Start monitoring APM progress - CONTINUOUS STREAM
        function startApmProgressMonitoring() {
            // Check immediately
            checkApmProgress();
            
            // Then check every 500ms continuously - NEVER STOP
            setInterval(checkApmProgress, 500);
        }

        // Start APM progress monitoring
        startApmProgressMonitoring();
    </script>
    <script>
    document.addEventListener('DOMContentLoaded', function(){
        const dropdownBtn = document.getElementById('menuDropdownBtn');
        const dropdownContent = document.getElementById('menuDropdownContent');
        if(dropdownBtn && dropdownContent){
            dropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                dropdownContent.classList.toggle('show');
            });
            document.addEventListener('click', function(e){
                if(!e.target.closest('.header-dropdown')){
                    dropdownContent.classList.remove('show');
                }
            });
        }
    });
    </script>
</body>

</html>