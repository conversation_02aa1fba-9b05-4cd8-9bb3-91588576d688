<?php
/**
 * Backup File Deletion Handler
 */

// Set the response content type to JSON
header('Content-Type: application/json');

// Define the backup directory
$backupDir = '/var/www/html/backups';

// Get the requested filename
$filename = isset($_POST['filename']) ? $_POST['filename'] : '';

// Validate the filename (only allow .tar.gz files)
if (empty($filename) || !preg_match('/^[\w\-\.]+\.tar\.gz$/', $filename)) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid filename'
    ]);
    exit;
}

// Build the full path to the file
$filePath = $backupDir . '/' . $filename;

// Check if the file exists
if (!file_exists($filePath) || !is_file($filePath)) {
    echo json_encode([
        'success' => false,
        'message' => 'File not found'
    ]);
    exit;
}

// Attempt to delete the file
if (unlink($filePath)) {
    echo json_encode([
        'success' => true,
        'message' => 'Backup file deleted successfully'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to delete the backup file'
    ]);
} 