<?php
/**
 * Backup Configuration Handler
 * Handles AJAX requests for backup operations
 */

// Include the helper functions
require_once 'backupConfigHelpers.php';

/**
 * Update or remove the cron job for automatic backups.
 *
 * @param bool $enable True to enable (create/update) the cron job, false to disable (remove)
 * @return bool Success of the operation
 */
function updateAutoBackupCron($enable, $frequency = 'daily', $time = '02:00') {
    $cronFile = '/etc/cron.d/blesk-backup';

    // Parse time (HH:MM)
    if (!preg_match('/^(\d{1,2}):(\d{2})$/', $time, $m)) {
        $hour = 2; $min = 0;
    } else {
        $hour = intval($m[1]);
        $min = intval($m[2]);
        if ($hour < 0 || $hour > 23) $hour = 2;
        if ($min < 0 || $min > 59) $min = 0;
    }

    // Build cron schedule based on frequency
    switch ($frequency) {
        case 'weekly':
            // Run on Sunday (0)
            $cronSchedule = "$min $hour * * 0";
            break;
        case 'monthly':
            // Run on the 1st of every month
            $cronSchedule = "$min $hour 1 * *";
            break;
        case 'daily':
        default:
            $cronSchedule = "$min $hour * * *";
            break;
    }

    $cronLine = $cronSchedule . " root /usr/bin/php /var/www/html/bubblemaps/src/backupConfig/autoBackupCron.php > /dev/null 2>&1";

    $success = true;

    if ($enable) {
        // Write cron using sudo + tee (avoids direct write permission issues)
        $writeCmd = 'echo ' . escapeshellarg($cronLine . "\n") . ' | sudo tee ' . escapeshellarg($cronFile) . ' > /dev/null';
        exec($writeCmd, $output, $statusWrite);

        if ($statusWrite !== 0) {
            error_log("updateAutoBackupCron: Failed to write cron file via sudo. Status: $statusWrite");
            $success = false;
        } else {
            // Set permissions via sudo
            $chmodCmd = 'sudo chmod 644 ' . escapeshellarg($cronFile);
            exec($chmodCmd, $output2, $statusChmod);
            if ($statusChmod !== 0) {
                error_log("updateAutoBackupCron: Failed to chmod cron file via sudo. Status: $statusChmod");
                // Not fatal but log
            }
        }
    } else {
        // Remove cron file via sudo
        $removeCmd = 'sudo rm -f ' . escapeshellarg($cronFile);
        exec($removeCmd, $output3, $statusRemove);
        if ($statusRemove !== 0) {
            error_log("updateAutoBackupCron: Failed to remove cron file via sudo. Status: $statusRemove");
            $success = false;
        }
    }

    return $success;
}

// Set the response content type to JSON
header('Content-Type: application/json');

// Get the requested action
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Response array
$response = ['success' => false, 'message' => 'Invalid action'];

switch ($action) {
    case 'getBackupModules':
        // Get backup modules configuration
        $backupModules = getBackupModules();
        
        // Get available modules and filter the response
        $availableModules = getAvailableModules();
        
        // Add available modules info to the response
        $backupModules['available_modules'] = $availableModules;
        
        $response = [
            'success' => true,
            'modules' => $backupModules
        ];
        break;

    case 'saveBackupModules':
        // Save backup modules configuration
        if (isset($_POST['modules']) && is_array($_POST['modules'])) {
            $modules = $_POST['modules'];
            
            // Check if this is a silent save (no success message needed)
            $silent = isset($_POST['silent']) && $_POST['silent'] === 'true';
            
            // Normal save (all settings)
            // Validate module settings
            $validModules = [
                'backup_type', 'apm', 'npm', 'nta', 'spm', 'elm', 'ncm', 'nsm', 'alm',
                'scp_server', 'scp_user', 'scp_password', 'scp_folder', 'scp_delete_local',
                'ftp_server', 'ftp_user', 'ftp_password', 'ftp_folder', 'ftp_delete_local',
                'auto_schedule', 'auto_frequency', 'auto_time'
            ];
            
            $configData = '';
            foreach ($validModules as $module) {
                if (isset($modules[$module])) {
                    $value = $modules[$module];
                    // For module settings only allow 'yes' or 'no'
                    if ($module !== 'backup_type' && 
                        !in_array($module, ['scp_server', 'scp_user', 'scp_password', 'scp_folder', 
                                           'ftp_server', 'ftp_user', 'ftp_password', 'ftp_folder'])) {
                        $value = ($value === 'yes') ? 'yes' : 'no';
                    }
                    $configData .= "$module=$value\n";
                }
            }
            
            // Save configuration to file
            $configFile = '/var/www/html/backups/backup-modules.conf';
            $success = file_put_contents($configFile, $configData);
            
            if ($success !== false) {
                // Update cron job based on auto_schedule setting
                $cronMessage = '';
                $overallSuccess = true;
                if (isset($modules['auto_schedule'])) {
                    $enableCron = $modules['auto_schedule'] === 'yes';
                    $frequency = isset($modules['auto_frequency']) ? $modules['auto_frequency'] : 'daily';
                    $timeStr   = isset($modules['auto_time']) ? $modules['auto_time'] : '02:00';
                    $cronResult = updateAutoBackupCron($enableCron, $frequency, $timeStr);
                    if ($cronResult) {
                        $cronMessage = $enableCron ? 'Automatic backup schedule enabled.' : 'Automatic backup schedule disabled.';
                    } else {
                        $cronMessage = $enableCron ? 'Failed to enable automatic backup schedule (cron write failed).' : 'Failed to disable automatic backup schedule (cron removal failed).';
                        $overallSuccess = false; // Indicate error so UI shows it
                    }
                }

                if ($overallSuccess) {
                    $msg = $silent ? '' : ('Settings saved successfully' . (!empty($cronMessage) ? ' ' . $cronMessage : ''));
                } else {
                    // Always show error message
                    $msg = !empty($cronMessage) ? $cronMessage : 'Error saving settings';
                }

                $response = [
                    'success' => $overallSuccess,
                    'message' => $msg
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => 'Error saving settings'
                ];
            }
        }
        break;

    case 'startBackup':
        // Check if backup is already in progress
        if (isBackupInProgress()) {
            $response = [
                'success' => false,
                'message' => 'A backup is already in progress'
            ];
            break;
        }
        
        // Get modules from request or fall back to saved config
        // $modules = isset($_POST['modules']) && is_array($_POST['modules']) ? $_POST['modules'] : getBackupModules();
        // Retrieve backup_type and scp_settings from POST data (as sent by JS)
        $backupType = isset($_POST['backup_type']) ? $_POST['backup_type'] : 'local';
        $selectedModules = isset($_POST['modules']) && is_array($_POST['modules']) ? $_POST['modules'] : [];
        $scpSettings = isset($_POST['scp_settings']) && is_array($_POST['scp_settings']) ? $_POST['scp_settings'] : [];
        $ftpSettings = isset($_POST['ftp_settings']) && is_array($_POST['ftp_settings']) ? $_POST['ftp_settings'] : [];

        // If backupType is still not set (e.g. older JS call structure), try to get it from selectedModules
        if (empty($backupType) && isset($selectedModules['backup_type'])){
            $backupType = $selectedModules['backup_type'];
        }
        
        // Get available modules
        $availableModules = getAvailableModules();
        
        // Build the command arguments for blesk-backup
        $args = '';
        $moduleCount = 0;
        
        foreach (['apm', 'npm', 'nta', 'spm', 'elm', 'ncm', 'nsm', 'alm'] as $module) {
            // Only include modules that are available and selected
            if (in_array($module, $availableModules) && isset($selectedModules[$module]) && $selectedModules[$module] === 'yes') {
                $args .= " $module";
                $moduleCount++;
            }
        }
        
        // Check if at least one module is selected
        if ($moduleCount === 0) {
            $response = [
                'success' => false,
                'message' => 'Please select at least one module to backup'
            ];
            break;
        }
        
        // Start the backup process (local backup first)
        $backupCommand = "sudo /usr/bin/blesk-backup$args > /dev/null 2>&1";
        exec($backupCommand, $backupOutput, $backupStatus);
        
        // After exec, check $backupStatus. 0 usually means success.
        if ($backupStatus !== 0) {
            $response = [
                'success' => false,
                'message' => 'Local backup command failed. Status: ' . $backupStatus . ' Output: ' . implode("\n", $backupOutput)
            ];
            // Log detailed error
            error_log("blesk-backup failed. Args: $args, Status: $backupStatus, Output: " . implode("\n", $backupOutput));
            break;
        }

        // Find the newest backup file created by blesk-backup
        // This assumes blesk-backup creates a .tar.gz file in /var/www/html/backups/
        // and we need its name for SCP.
        $localBackupFile = '';
        $backupDir = '/var/www/html/backups'; // Ensure this is the correct local backup directory
        $files = glob($backupDir . '/*.tar.gz');
        if (!empty($files)) {
            usort($files, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });
            $localBackupFile = $files[0]; // Newest file
        } 

        if (empty($localBackupFile) || !file_exists($localBackupFile)) {
            $response = [
                'success' => false,
                'message' => 'Local backup file not found after backup command execution.'
            ];
            error_log("Local backup file not found after blesk-backup. Expected in: $backupDir");
            break;
        }
        
        $localBackupFilename = basename($localBackupFile);

        if ($backupType === 'scp') {
            if (empty($scpSettings['server']) || empty($scpSettings['user']) || empty($scpSettings['folder'])) {
                $response = [
                    'success' => false,
                    'message' => 'SCP settings (server, user, folder) are incomplete.'
                ];
                // Optionally delete the local backup if SCP settings are bad, to prevent clutter
                // unlink($localBackupFile);
                break;
            }

            $scpServer = escapeshellarg($scpSettings['server']);
            $scpUser = escapeshellarg($scpSettings['user']);
            $scpPassword = isset($scpSettings['password']) ? $scpSettings['password'] : ''; // Password can be empty for key-based auth
            $scpRemoteFolder = escapeshellarg(rtrim($scpSettings['folder'], '/') . '/' . $localBackupFilename);
            $deleteLocalAfterTransfer = isset($scpSettings['delete_local']) && $scpSettings['delete_local'] === 'yes';

            // Construct SCP command with sshpass
            // Ensure sshpass is installed on the system: sudo apt-get install sshpass OR sudo yum install sshpass
            // Add StrictHostKeyChecking=no and UserKnownHostsFile=/dev/null to bypass host key checking for simplicity.
            // WARNING: This has security implications. For production, manage known_hosts properly.
            if (!empty($scpPassword)) {
                // Use sshpass for password authentication
                $scpCommand = sprintf(
                    'sshpass -p %s scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null %s %s@%s:%s',
                    escapeshellarg($scpPassword),
                    escapeshellarg($localBackupFile),
                    $scpUser,
                    $scpServer,
                    $scpRemoteFolder
                );
                
                // For security, avoid logging the full command with password if possible, or mask password in logs
                error_log("Executing SCP command with password (masked): sshpass -p '*****' scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null " . escapeshellarg($localBackupFile) . " " . $scpUser . "@" . $scpServer . ":" . $scpRemoteFolder);
            } else {
                // Use standard scp for key-based authentication
                $scpCommand = sprintf(
                    'scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null %s %s@%s:%s',
                    escapeshellarg($localBackupFile),
                    $scpUser,
                    $scpServer,
                    $scpRemoteFolder
                );
                
                error_log("Executing SCP command with SSH key authentication: " . $scpCommand);
            }

            exec($scpCommand . " > /dev/null 2>&1", $scpOutput, $scpStatus);

            if ($scpStatus === 0) {
                $response = [
                    'success' => true,
                    'message' => 'Local backup created and successfully transferred via SCP to ' . $scpSettings['server'] . '.'
                ];
                if ($deleteLocalAfterTransfer) {
                    if (unlink($localBackupFile)) {
                        $response['message'] .= ' Local backup file deleted.';
                    } else {
                        $response['message'] .= ' Failed to delete local backup file.';
                        error_log("Failed to delete local backup file: $localBackupFile after SCP transfer.");
                    }
                }
            } else {
                $response = [
                    'success' => false,
                    'message' => 'Local backup created, but SCP transfer failed. Status: ' . $scpStatus . '. Please check SCP settings, credentials, and server connectivity.'
                ];
                error_log("SCP command failed. Status: $scpStatus. Command (password masked): sshpass -p '*****' scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null " . escapeshellarg($localBackupFile) . " " . $scpUser . "@" . $scpServer . ":" . $scpRemoteFolder . " Output: " . implode("\n", $scpOutput));
                
                // Delete local backup if requested, even if SCP transfer failed
                if ($deleteLocalAfterTransfer) {
                    if (unlink($localBackupFile)) {
                        $response['message'] .= ' Local backup file deleted as requested.';
                        error_log("Local backup file deleted after failed SCP transfer: $localBackupFile");
                    } else {
                        $response['message'] .= ' Failed to delete local backup file.';
                        error_log("Failed to delete local backup file: $localBackupFile after failed SCP transfer.");
                    }
                }
            }

        } else if ($backupType === 'ftp') {
            if (empty($ftpSettings['server']) || empty($ftpSettings['user']) || empty($ftpSettings['folder'])) {
                $response = [
                    'success' => false,
                    'message' => 'FTP settings (server, user, folder) are incomplete.'
                ];
                break;
            }

            $ftpServer = $ftpSettings['server'];
            $ftpUser = $ftpSettings['user'];
            $ftpPassword = isset($ftpSettings['password']) ? $ftpSettings['password'] : '';
            $ftpFolder = $ftpSettings['folder'];
            $deleteLocalAfterTransfer = isset($ftpSettings['delete_local']) && $ftpSettings['delete_local'] === 'yes';

            // Create FTP command script
            $ftpCommand = "ftp -inv $ftpServer <<EOF\n";
            $ftpCommand .= "user $ftpUser $ftpPassword\n";
            $ftpCommand .= "cd $ftpFolder\n";
            $ftpCommand .= "lcd /var/www/html/backups\n";
            $ftpCommand .= "put $localBackupFilename\n";
            $ftpCommand .= "bye\nEOF";

            // Execute the FTP command
            $tempScript = tempnam(sys_get_temp_dir(), 'ftp_script_');
            file_put_contents($tempScript, $ftpCommand);
            chmod($tempScript, 0700); // Make it executable
            
            exec($tempScript . " 2>&1", $ftpOutput, $ftpStatus);
            unlink($tempScript); // Delete the temporary script

            if ($ftpStatus === 0) {
                $response = [
                    'success' => true,
                    'message' => 'Local backup created and successfully transferred via FTP to ' . $ftpSettings['server'] . '.'
                ];
                if ($deleteLocalAfterTransfer) {
                    if (unlink($localBackupFile)) {
                        $response['message'] .= ' Local backup file deleted.';
                    } else {
                        $response['message'] .= ' Failed to delete local backup file.';
                        error_log("Failed to delete local backup file: $localBackupFile after FTP transfer.");
                    }
                }
            } else {
                $response = [
                    'success' => false,
                    'message' => 'Local backup created, but FTP transfer failed. Status: ' . $ftpStatus . '. Please check FTP settings, credentials, and server connectivity.'
                ];
                error_log("FTP command failed. Status: $ftpStatus. Command (password masked): ftp -inv $ftpServer << EOF\nuser $ftpUser ******\ncd $ftpFolder\nlcd /var/www/html/backups\nput $localBackupFilename\nbye\nEOF\nOutput: " . implode("\n", $ftpOutput));
                
                // Delete local backup if requested, even if FTP transfer failed
                if ($deleteLocalAfterTransfer) {
                    if (unlink($localBackupFile)) {
                        $response['message'] .= ' Local backup file deleted as requested.';
                        error_log("Local backup file deleted after failed FTP transfer: $localBackupFile");
                    } else {
                        $response['message'] .= ' Failed to delete local backup file.';
                        error_log("Failed to delete local backup file: $localBackupFile after failed FTP transfer.");
                    }
                }
            }
        } else { // Local backup only
            $response = [
                'success' => true,
                'message' => 'Local backup completed successfully. File: ' . $localBackupFilename
            ];
        }
        break;

    case 'getBackupFiles':
        // Get available backup files
        $files = getBackupFiles();
        
        $response = [
            'success' => true,
            'files' => $files
        ];
        break;

    case 'checkBackupStatus':
        // Check if backup is in progress
        $isInProgress = isBackupInProgress();
        $response = [
            'success' => true,
            'inProgress' => $isInProgress
        ];
        break;

    case 'testConnection':
        // Test connection to remote server (SCP or FTP)
        if (!isset($_POST['connection_type']) || !isset($_POST['settings']) || !is_array($_POST['settings'])) {
            $response = [
                'success' => false,
                'message' => 'Invalid request parameters'
            ];
            break;
        }

        $connectionType = $_POST['connection_type'];
        $settings = $_POST['settings'];

        if ($connectionType === 'scp') {
            // Test SCP connection
            if (empty($settings['server']) || empty($settings['user'])) {
                $response = [
                    'success' => false,
                    'message' => 'Server address and username are required for SCP connection'
                ];
                break;
            }

            // Set up test command
            $server = escapeshellarg($settings['server']);
            $user = escapeshellarg($settings['user']);
            $password = isset($settings['password']) ? $settings['password'] : '';
            
            // For SCP we'll use SSH to test the connection by running a simple command
            if (!empty($password)) {
                // Using sshpass for password authentication
                $command = sprintf(
                    'sshpass -p %s ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=10 %s@%s "echo Connection successful"',
                    escapeshellarg($password),
                    $user,
                    $server
                );
                // Don't log the actual command with password
                error_log("Testing SCP connection with password to {$settings['server']} as {$settings['user']}");
            } else {
                // Using key-based authentication
                $command = sprintf(
                    'ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=10 %s@%s "echo Connection successful"',
                    $user,
                    $server
                );
                error_log("Testing SCP connection with key authentication to {$settings['server']} as {$settings['user']}");
            }
            
            // Execute the command and capture output
            exec($command . " 2>&1", $output, $status);
            
            if ($status === 0) {
                $response = [
                    'success' => true,
                    'message' => 'SCP connection successful'
                ];
            } else {
                $errorMessage = implode("\n", $output);
                // Strip out sensitive information before logging
                $safeErrorMessage = preg_replace('/password|pass/i', '[REDACTED]', $errorMessage);
                error_log("SCP connection test failed: $safeErrorMessage");
                
                $response = [
                    'success' => false,
                    'message' => 'SCP connection failed: ' . $errorMessage
                ];
            }
        } else if ($connectionType === 'ftp') {
            // Test FTP connection
            if (empty($settings['server']) || empty($settings['user'])) {
                $response = [
                    'success' => false,
                    'message' => 'Server address and username are required for FTP connection'
                ];
                break;
            }
            
            $server = $settings['server'];
            $user = $settings['user'];
            $password = isset($settings['password']) ? $settings['password'] : '';
            $folder = isset($settings['folder']) && !empty($settings['folder']) ? $settings['folder'] : '/';
            
            // Try to connect using PHP's FTP functions
            $ftpConn = @ftp_connect($server, 21, 10); // 10 second timeout
            
            if ($ftpConn) {
                // Try to login
                $loginResult = @ftp_login($ftpConn, $user, $password);
                
                if ($loginResult) {
                    // Try to change directory if a folder was specified
                    if ($folder !== '/') {
                        $cdResult = @ftp_chdir($ftpConn, $folder);
                        
                        if ($cdResult) {
                            $response = [
                                'success' => true,
                                'message' => 'FTP connection successful and folder exists'
                            ];
                        } else {
                            $response = [
                                'success' => false,
                                'message' => 'FTP login successful, but could not access the specified folder: ' . $folder
                            ];
                        }
                    } else {
                        $response = [
                            'success' => true,
                            'message' => 'FTP connection successful'
                        ];
                    }
                } else {
                    $response = [
                        'success' => false,
                        'message' => 'FTP connection established, but login failed with the provided credentials'
                    ];
                }
                
                // Close the connection
                ftp_close($ftpConn);
            } else {
                $response = [
                    'success' => false,
                    'message' => 'Could not connect to FTP server: ' . $server
                ];
            }
        } else {
            $response = [
                'success' => false,
                'message' => 'Invalid connection type: ' . $connectionType
            ];
        }
        break;

    default:
        $response = [
            'success' => false,
            'message' => 'Unknown action'
        ];
}

// Output the response
echo json_encode($response); 