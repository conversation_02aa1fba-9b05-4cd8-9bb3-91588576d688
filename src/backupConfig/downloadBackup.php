<?php
/**
 * Backup File Download Handler
 */

// Define the backup directory
$backupDir = '/var/www/html/backups';

// Get the requested file
$filename = isset($_GET['file']) ? $_GET['file'] : '';

// Validate the filename (only allow .tar.gz files)
if (empty($filename) || !preg_match('/^[\w\-\.]+\.tar\.gz$/', $filename)) {
    header('HTTP/1.1 400 Bad Request');
    die('Invalid filename');
}

// Build the full path to the file
$filePath = $backupDir . '/' . $filename;

// Check if the file exists
if (!file_exists($filePath) || !is_file($filePath)) {
    header('HTTP/1.1 404 Not Found');
    die('File not found');
}

// Get file size
$fileSize = filesize($filePath);

// Set appropriate headers
header('Content-Description: File Transfer');
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . basename($filePath) . '"');
header('Content-Length: ' . $fileSize);
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Expires: 0');

// Clean output buffer
ob_clean();
flush();

// Read the file and output it to the browser
readfile($filePath);
exit; 