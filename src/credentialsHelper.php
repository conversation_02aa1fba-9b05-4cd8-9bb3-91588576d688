<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include "loadenv.php";

// Database connection function
function getDBConnection()
{
    $host = $_ENV["DB_SERVER"];
    $dbname = 'ndd';
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"];
    try {
        $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch (PDOException $e) {
        echo "Connection failed: " . $e->getMessage();
        return null;
    }
}

// Helper function to get the server's own IP address
function getSelfIp() {
    // Prioritize SERVER_ADDR if available (more reliable in web context)
    $ip = $_SERVER['SERVER_ADDR'] ?? null;
    if ($ip === null) {
        // Fallback to hostname -I if SERVER_ADDR is not set
        $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
        if ($ip === null || trim($ip) === '') {
             error_log("Error: Unable to retrieve IP address via SERVER_ADDR or hostname -I.");
             // Return a default or handle the error appropriately
             return '127.0.0.1'; // Or throw an exception
        }
    }
    // Return the first IP if multiple are returned by hostname -I
    return trim(explode(' ', $ip)[0]);
}

// Helper function to get admin user credentials from 'blesk' db
// Note: Uses mysqli, unlike the PDO connection elsewhere in this file.
function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"];
    $dbname = "blesk"; // Target 'blesk' database for admin credentials

    // Create connection using mysqli
    $conn = new mysqli($servername, $username, $password, $dbname);

    // Check connection
    if ($conn->connect_error) {
        error_log("Admin DB Connection failed: " . $conn->connect_error);
        // Handle error appropriately, maybe return null or throw exception
        return null;
    }

    return $conn;
}

// Helper function to fetch the first admin user's credentials
function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();
    if (!$conn) {
        return null; // Handle connection error
    }

    $sql = "SELECT username, password FROM users WHERE user_id = 1 LIMIT 1"; // Ensure only one row
    $result = $conn->query($sql);

    $userCredentials = null;
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "username" => $row["username"],
            "password" => $row["password"] // Assuming password is plain text or handled by target script
        ];
    } else {
         error_log("Admin user credentials not found or DB error: " . $conn->error);
    }

    $conn->close();
    return $userCredentials;
}


// Reusable function to POST WMI data using cURL
function postWmiData($targetScriptPath, $postData) {
    // Get admin credentials and self IP
    $adminUser = getUserCredentials();
    $selfIP = getSelfIp();

    if (!$adminUser) {
        error_log("Failed to get admin credentials in postWmiData for target: " . $targetScriptPath);
        // Return false or throw an exception to indicate failure
        return false; // Indicate failure
    }

    $usr = $adminUser["username"];
    $pwd = $adminUser["password"];

    // Construct URL using self IP
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $url = $protocol . $selfIP . $targetScriptPath; // Use provided script path

    // Initialize cURL session
    $ch = curl_init($url);

    // Set cURL options
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_USERPWD, "$usr:$pwd");
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 2);
    // Optional: Add timeout
    // curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 10 second timeout

    // Execute cURL session
    $response = curl_exec($ch);
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
    $curlError = curl_error($ch);

    // Close cURL session
    curl_close($ch);

    // Log potential errors
    $logPrefix = "cURL Error in postWmiData (" . $targetScriptPath . "): ";
    if ($curlError) {
        error_log($logPrefix . $curlError);
        return false; // Indicate failure
    } elseif ($httpcode >= 400) {
         error_log("HTTP Error in postWmiData (" . $targetScriptPath . "): " . $httpcode . " at " . $finalUrl);
         // Consider returning false here too, depending on desired behavior for HTTP errors
         // return false;
    }

    // Return true on success (or lack of detected error)
    return true;
}


// Handle form submissions (Create and Update)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $conn = getDBConnection();
    if ($conn) {
        $type = $_GET['type'] ?? '';

        if (isset($_POST['id']) && !empty($_POST['id'])) {
            // Update existing record using ID
            switch ($type) {
                case 'wmi':
                    // Prepare data for the POST request (including the ID for update)
                    $postData = [
                        'id' => $_POST['id'], // Include ID for the edit script
                        'username' => $_POST['username'],
                        'password' => $_POST['password'],
                        'domain' => $_POST['domain'] ?? '',
                        'description' => $_POST['description'] ?? ''
                    ];

                    // Use the refactored function to post data
                    if (!postWmiData('/ndd/credswmi-edit-proc.php', $postData)) {
                        // Handle potential failure from postWmiData if needed
                        // e.g., set a session error message before breaking
                        $_SESSION['credential_helper_error'] = "Failed to submit WMI update request.";
                    }
                    break;
                case 'vmware':
                    $postData = [
                        'id' => $_POST['id'],
                        'username' => $_POST['username'],
                        'password' => $_POST['password'],
                        'description' => $_POST['description'] ?? ''
                    ];

                    if (!postWmiData('/ndd/credsvmware-edit-proc.php', $postData)) {
                        $_SESSION['credential_helper_error'] = "Failed to submit VMware update request.";
                    }
                    break;
                case 'ssh':
                    $postData = [
                        'id' => $_POST['id'],
                        'username' => $_POST['username'],
                        'password' => $_POST['password'] ?? '',
                        'enable' => $_POST['enable'] ?? '',
                        'port' => $_POST['port'] ?? 22,
                        'description' => $_POST['description'] ?? ''
                    ];

                    if (!postWmiData('/ndd/credsssh-edit-proc.php', $postData)) {
                        $_SESSION['credential_helper_error'] = "Failed to submit SSH update request.";
                    }
                    break;
                case 'telnet':
                    $stmt = $conn->prepare("UPDATE ctel SET username=?, password=?, enable=?, description=? WHERE id=?");
                    $stmt->execute([$_POST['username'], $_POST['password'] ?? '', $_POST['enable'] ?? '', $_POST['description'] ?? '', $_POST['id']]);
                    break;
                case 'snmp':
                    // Fetch current values so we can populate required *_old fields
                    $stmtOld = $conn->prepare("SELECT communityro, communityrw, description FROM csnmp WHERE id = ?");
                    $stmtOld->execute([$_POST['id']]);
                    $old = $stmtOld->fetch(PDO::FETCH_ASSOC);

                    $postData = [
                        'id' => $_POST['id'],
                        'communityro' => $_POST['communityro'] ?? '',
                        'communityrw' => $_POST['communityrw'] ?? '',
                        'description' => $_POST['description'] ?? '',
                        'communityro_old' => $old['communityro'] ?? '',
                        'communityrw_old' => $old['communityrw'] ?? '',
                        'description_old' => $old['description'] ?? ''
                    ];

                    if (!postWmiData('/ndd/credssnmp-edit-proc.php', $postData)) {
                        $_SESSION['credential_helper_error'] = "Failed to submit SNMP update request.";
                    }
                    break;
                case 'snmpv3':
                    // Fetch current values so we can populate required *_old fields
                    $stmtOld = $conn->prepare("SELECT snmp_username, snmp_password, snmp_auth_protocol, snmp_priv_protocol, snmp_priv_passphrase FROM csnmpv3 WHERE id = ?");
                    $stmtOld->execute([$_POST['id']]);
                    $old = $stmtOld->fetch(PDO::FETCH_ASSOC);

                    $postData = [
                        'id' => $_POST['id'],
                        'snmp_username' => $_POST['snmp_username'] ?? '',
                        'snmp_password' => $_POST['snmp_password'] ?? '',
                        'snmp_auth_protocol' => $_POST['snmp_auth_protocol'] ?? '',
                        'snmp_priv_protocol' => $_POST['snmp_priv_protocol'] ?? '',
                        'snmp_priv_passphrase' => $_POST['snmp_priv_passphrase'] ?? '',
                        'snmp_username_old' => $old['snmp_username'] ?? '',
                        'snmp_password_old' => $old['snmp_password'] ?? '',
                        'snmp_auth_protocol_old' => $old['snmp_auth_protocol'] ?? '',
                        'snmp_priv_protocol_old' => $old['snmp_priv_protocol'] ?? '',
                        'snmp_priv_passphrase_old' => $old['snmp_priv_passphrase'] ?? ''
                    ];

                    if (!postWmiData('/ndd/credssnmpv3-edit-proc.php', $postData)) {
                        $_SESSION['credential_helper_error'] = "Failed to submit SNMPv3 update request.";
                    }
                    break;
                case 'other':
                    $stmt = $conn->prepare("UPDATE cother SET type=?, username=?, password=?, description=? WHERE id=?");
                    $stmt->execute([$_POST['type'], $_POST['username'], $_POST['password'] ?? '', $_POST['description'] ?? '', $_POST['id']]);
                    break;
            }
        } else {
            // Create new record
            $positionField = ($type === 'snmpv3') ? 'sequence' : 'position';
            $position = $conn->query("SELECT MAX($positionField) FROM c{$type}")->fetchColumn() + 1;
            switch ($type) {
                case 'wmi':
                    // Prepare data for the POST request
                    $postData = [
                        'username' => $_POST['username'],
                        'password' => $_POST['password'],
                        'domain' => $_POST['domain'] ?? '',
                        'description' => $_POST['description'] ?? ''
                    ];

                    // Use the refactored function to post data
                    if (!postWmiData('/ndd/credswmi-add-proc.php', $postData)) {
                         // Handle potential failure from postWmiData if needed
                         // e.g., set a session error message before breaking
                         $_SESSION['credential_helper_error'] = "Failed to submit WMI create request.";
                    }

                    break; // Break out of the switch statement
                case 'vmware':
                    $postData = [
                        'username' => $_POST['username'],
                        'password' => $_POST['password'],
                        'description' => $_POST['description'] ?? ''
                    ];

                    if (!postWmiData('/ndd/credsvmware-add-proc.php', $postData)) {
                        $_SESSION['credential_helper_error'] = "Failed to submit VMware create request.";
                    }
                    break;
                case 'ssh':
                    $postData = [
                        'username' => $_POST['username'],
                        'password' => $_POST['password'] ?? '',
                        'enable' => $_POST['enable'] ?? '',
                        'port' => $_POST['port'] ?? 22,
                        'description' => $_POST['description'] ?? ''
                    ];

                    if (!postWmiData('/ndd/credsssh-add-proc.php', $postData)) {
                        $_SESSION['credential_helper_error'] = "Failed to submit SSH create request.";
                    }
                    break;
                case 'telnet':
                    $stmt = $conn->prepare("INSERT INTO ctel (username, password, enable, description, position) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$_POST['username'], $_POST['password'] ?? '', $_POST['enable'] ?? '', $_POST['description'] ?? '', $position]);
                    break;
                case 'snmp':
                    $postData = [
                        'communityro' => $_POST['communityro'] ?? '',
                        'communityrw' => $_POST['communityrw'] ?? '',
                        'description' => $_POST['description'] ?? ''
                    ];

                    if (!postWmiData('/ndd/credssnmp-add-proc.php', $postData)) {
                        $_SESSION['credential_helper_error'] = "Failed to submit SNMP create request.";
                    }
                    break;
                case 'snmpv3':
                    $postData = [
                        'snmp_username' => $_POST['snmp_username'] ?? '',
                        'snmp_password' => $_POST['snmp_password'] ?? '',
                        'snmp_auth_protocol' => $_POST['snmp_auth_protocol'] ?? '',
                        'snmp_priv_protocol' => $_POST['snmp_priv_protocol'] ?? '',
                        'snmp_priv_passphrase' => $_POST['snmp_priv_passphrase'] ?? ''
                    ];

                    if (!postWmiData('/ndd/credssnmpv3-add-proc.php', $postData)) {
                        $_SESSION['credential_helper_error'] = "Failed to submit SNMPv3 create request.";
                    }
                    break;
                case 'other':
                    $stmt = $conn->prepare("INSERT INTO cother (type, username, password, description, position) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$_POST['type'], $_POST['username'], $_POST['password'] ?? '', $_POST['description'] ?? '', $position]);
                    break;
            }
        }
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
}

// Handle deletions and position changes
if (isset($_GET['action'])) {
    $conn = getDBConnection();
    if ($conn) {
        $table = $_GET['table'];
        $id = $_GET['id'];
        $positionField = ($table === 'csnmpv3') ? 'sequence' : 'position';

        if ($_GET['action'] === 'delete') {
            $stmt = $conn->prepare("SELECT $positionField FROM $table WHERE id = ?");
            $stmt->execute([$id]);
            if ($stmt->fetchColumn() != 1) {
                $stmt = $conn->prepare("DELETE FROM $table WHERE id = ?");
                $stmt->execute([$id]);
                $stmt = $conn->query("SELECT id FROM $table ORDER BY $positionField");
                $i = 1;
                while ($row = $stmt->fetch()) {
                    $conn->prepare("UPDATE $table SET $positionField = ? WHERE id = ?")->execute([$i++, $row['id']]);
                }
            }
        } elseif ($_GET['action'] === 'move') {
            $direction = $_GET['direction'];
            $stmt = $conn->prepare("SELECT $positionField FROM $table WHERE id = ?");
            $stmt->execute([$id]);
            $currentPos = $stmt->fetchColumn();

            $stmt = $conn->query("SELECT MAX($positionField) FROM $table");
            $maxPos = $stmt->fetchColumn();

            if ($direction === 'up' && $currentPos > 1) {
                $newPos = $currentPos - 1;
                $stmt = $conn->prepare("UPDATE $table SET $positionField = ? WHERE $positionField = ? AND id != ?");
                $stmt->execute([9999, $newPos, $id]);
                $stmt = $conn->prepare("UPDATE $table SET $positionField = ? WHERE id = ?");
                $stmt->execute([$newPos, $id]);
                $stmt = $conn->prepare("UPDATE $table SET $positionField = ? WHERE $positionField = ?");
                $stmt->execute([$currentPos, 9999]);
            } elseif ($direction === 'down' && $currentPos < $maxPos) {
                $newPos = $currentPos + 1;
                $stmt = $conn->prepare("UPDATE $table SET $positionField = ? WHERE $positionField = ? AND id != ?");
                $stmt->execute([9999, $newPos, $id]);
                $stmt = $conn->prepare("UPDATE $table SET $positionField = ? WHERE id = ?");
                $stmt->execute([$newPos, $id]);
                $stmt = $conn->prepare("UPDATE $table SET $positionField = ? WHERE $positionField = ?");
                $stmt->execute([$currentPos, 9999]);
            }
        }
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
}

// Function to display table data with edit functionality
function displayTable($table, $fields, $type)
{
    $conn = getDBConnection();
    if ($conn) {
        try {
            $positionField = ($type === 'snmpv3') ? 'sequence' : 'position';
            $stmt = $conn->query("SELECT * FROM $table ORDER BY $positionField");
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            if ($results) {
                echo "<table><tr>";
                $displayFields = ($type === 'snmpv3')
                    ? ['id', 'snmp_auth_protocol', 'snmp_username', 'snmp_password', 'snmp_priv_protocol', 'snmp_priv_passphrase']
                    : array_filter($fields, function ($field) {
                        return $field !== 'position' && $field !== 'sequence';
                    });
                foreach ($displayFields as $field) {
                    $displayName = ($field === 'snmp_password') ? 'auth password' : (($field === 'snmp_priv_passphrase') ? 'priv passphrase' : (($field === 'snmp_auth_protocol') ? 'auth protocol' : (($field === 'snmp_priv_protocol') ? 'priv protocol' : $field)));
                    echo "<th data-label='$displayName'>$displayName</th>";
                }
                echo "<th data-label='Actions'>Actions</th></tr>";
                $rowCount = 0;
                foreach ($results as $row) {
                    $rowClass = ($rowCount++ % 2 == 0) ? 'even-row' : 'odd-row';
                    echo "<tr class='$rowClass' data-row-id='{$row['id']}'>";
                    foreach ($displayFields as $field) {
                        if (strpos($field, 'password') !== false || $field === 'snmp_priv_passphrase') {
                            echo "<td data-label='$field'>";
                            echo "<span class='password-hidden'>••••••••</span>";
                            echo "<span class='password-text hidden' data-field='$field'>" . htmlspecialchars($row[$field]) . "</span>";
                            echo "<span class='toggle-password fa fa-eye' data-target='$field'></span>";
                            echo "</td>";
                        } else {
                            echo "<td data-label='$field'>" . htmlspecialchars($row[$field]) . "</td>";
                        }
                    }
                    echo "<td data-label='Actions'>";
                    echo "<button class='action-btn edit-btn' onclick='editEntry(\"$type\", " . json_encode($row) . ")'>Edit</button>";
                    if ($row[$positionField] != 1) {
                        echo "<button class='action-btn delete-btn' onclick='deleteEntry(\"$table\", {$row['id']})'>Delete</button>";
                    }
                    echo "<button class='action-btn move-btn' onclick='moveUp(\"$table\", {$row['id']})'>↑</button>";
                    echo "<button class='action-btn move-btn' onclick='moveDown(\"$table\", {$row['id']})'>↓</button>";
                    echo "</td></tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='no-data'>No credentials available</p>";
            }
        } catch (PDOException $e) {
            echo "Error querying $table: " . $e->getMessage();
        }
    } else {
        echo "Database connection failed for $table";
    }
}
?>