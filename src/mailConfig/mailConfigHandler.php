<?php
include __DIR__ . "/../../loadenv.php";

// Define file paths
define('EMAIL_ADDR_FILE', '/var/lib/blesk/emailaddr');
define('EMAIL_SENDER_FILE', '/var/lib/blesk/emailsender');
define('SMTP_HOST_FILE', '/var/lib/blesk/smtphost');

// Include helper functions
require_once 'mailConfigHelpers.php';

class MailConfigHandler {
    private $conn;
    private const DB_NAME = "blesk";
    
    public function __construct() {
        $this->conn = new mysqli($_ENV["DB_SERVER"], $_ENV["DB_USER"], $_ENV["DB_PASSWORD"], self::DB_NAME);
        if ($this->conn->connect_error) {
            $this->handleError("Connection failed: " . $this->conn->connect_error);
        }
    }
    
    public function handleRequest(): void {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handlePostRequest();
        } elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'getSettings') {
            $this->handleGetRequest();
        }
    }

    private function handlePostRequest(): void {
        header('Content-Type: application/json');
        $response = ['success' => false, 'message' => 'An unknown error occurred.'];

        // Validate input
        $receiverEmail = $this->validateInput('receiverEmail', FILTER_VALIDATE_EMAIL, 'Invalid or empty Receiver Email provided.');
        $senderEmail = $this->validateInput('senderEmail', FILTER_VALIDATE_EMAIL, 'Invalid or empty Sender Email provided.');
        $smtpHost = $this->validateInput('smtpHost', null, 'SMTP Host cannot be empty.');

        // Get old values and credentials
        $oldReceiverEmail = readMailFileContent(EMAIL_ADDR_FILE);
        $oldSenderEmail = readMailFileContent(EMAIL_SENDER_FILE);
        $oldSmtpHost = readMailFileContent(SMTP_HOST_FILE);
        $credentials = $this->getAdminCredentials();

        // Prepare and send request
        $postData = [
            'admin_email' => $receiverEmail,
            'admin_email_old' => $oldReceiverEmail,
            'smtp_host' => $smtpHost,
            'smtp_host_old' => $oldSmtpHost,
            'mail_sender' => $senderEmail,
            'mail_sender_old' => $oldSenderEmail
        ];

        $ch = $this->initCurl('https://'.$_SERVER['SERVER_ADDR'].'/settings-mail-proc.php', $credentials);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));

        $result = curl_exec($ch);
        if ($result === false) {
            $response['message'] = 'cURL error: ' . curl_error($ch);
        } else {
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $responseBody = $result; // Capture the response body
            if ($httpCode === 200 && strpos($responseBody, 'fa fa-exclamation-triangle') === false) {
                $restartOutput = [];
                $restartStatus = null;
                exec('sudo systemctl restart postfix 2>&1', $restartOutput, $restartStatus);
                
                if ($restartStatus === 0) {
                    $postsuperOutput = [];
                    $postsuperStatus = null;
                    exec('sudo postsuper -d ALL 2>&1', $postsuperOutput, $postsuperStatus);
                    
                    if ($postsuperStatus === 0) {
                        $response['success'] = true;
                        $response['message'] = 'Mail settings submitted successfully.';
                    } else {
                        $response['success'] = false;
                        $response['message'] = 'Mail settings updated and postfix restarted but mail queue clearing failed: ' . implode("\n", $postsuperOutput);
                    }
                } else {
                    $response['success'] = false;
                    $response['message'] = 'Mail settings updated but postfix restart failed: ' . implode("\n", $restartOutput);
                }
            } else {
                $response['success'] = false;
                $response['message'] = 'Failed to submit mail settings. HTTP code: ' . $httpCode . '. Response: ' . $responseBody;
            }
        }
        curl_close($ch);

        echo json_encode($response);
        exit;
    }

    private function handleGetRequest(): void {
        header('Content-Type: application/json');
        $settings = [
            'receiverEmail' => readMailFileContent(EMAIL_ADDR_FILE),
            'senderEmail' => readMailFileContent(EMAIL_SENDER_FILE),
            'smtpHost' => readMailFileContent(SMTP_HOST_FILE)
        ];
        echo json_encode($settings);
        exit;
    }

    private function validateInput(string $field, ?int $filter, string $errorMessage): string {
        $value = isset($_POST[$field]) ? trim($_POST[$field]) : '';
        if (empty($value) || ($filter && !filter_var($value, $filter))) {
            echo json_encode(['success' => false, 'message' => $errorMessage]);
            exit;
        }
        return $value;
    }

    private function getAdminCredentials(): array {
        $sql = "SELECT username, password FROM users WHERE user_id = 1";
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return [
                "username" => $row["username"],
                "password" => $row["password"]
            ];
        }
        return ["username" => "", "password" => ""];
    }

    private function initCurl(string $url, array $credentials): CurlHandle {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => "{$credentials['username']}:{$credentials['password']}",
            CURLOPT_HTTPHEADER => ['Content-Type: application/x-www-form-urlencoded']
        ]);
        return $ch;
    }

    private function handleError(string $message): void {
        error_log($message);
        header('Content-Type: application/json');
        echo json_encode(["success" => false, "message" => $message]);
        exit;
    }
}

// Instantiate and handle request
$handler = new MailConfigHandler();
$handler->handleRequest();
?>