<?php
require_once 'mailConfigHelpers.php';

// --- Configuration ---
define('MAILQ_COMMAND', '/usr/bin/mailq'); // Adjust path if necessary
define('MAILLOG_PATH', '/var/log/maillog'); // Path to the mail log file
define('SUDO_COMMAND', '/usr/bin/sudo'); // Path to sudo
define('TAIL_COMMAND', '/usr/bin/tail'); // Path to tail command
define('LOG_LINES_TO_FETCH', 5); // Number of recent log lines to fetch
define('SENDMAIL_PATH', '/usr/sbin/sendmail'); // Adjust path if necessary
define('POST_SEND_WAIT_SECONDS', 2); // How long to wait after sending before checking queue/logs
// --- End Configuration ---

// Function to check if commands are available and executable
function checkCommandPermissions() {
    $errors = [];
    // Check for sudo command
    if (!is_executable(SUDO_COMMAND)) {
         $errors[] = "Error: '" . SUDO_COMMAND . "' command not found or not executable. Cannot use sudo to fetch logs.";
    }
    // Check for tail command
    if (!is_executable(TAIL_COMMAND)) {
         $errors[] = "Error: '" . TAIL_COMMAND . "' command not found or not executable. Cannot fetch logs.";
    }
    // Check for sendmail command
    if (!is_executable(SENDMAIL_PATH)) {
         $errors[] = "Error: '" . SENDMAIL_PATH . "' not found or not executable by the web server user. Cannot send email.";
    }
    return $errors;
}


// Function to send test email (simplified return)
function sendTestEmailSimple($to, $from) {
    $logPrefix = "[sendTestEmailSimple] ";
    error_log($logPrefix . "Attempting to send test email to: $to from: $from");
    $subject = "Test Email from Blesk";
    $message = "This is a test email sent from the Blesk system.\n\n";
    $message .= "Timestamp: " . date('Y-m-d H:i:s') . "\n";

    $fromHeader = $from; // Basic handling, assumes $from is usable directly or formatted

    $headers = "From: " . $fromHeader . "\r\n";
    $headers .= "Reply-To: " . $fromHeader . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

    $sendmail_cmd = SENDMAIL_PATH . " -t -f " . escapeshellarg($from);
    error_log($logPrefix . "Executing sendmail command: $sendmail_cmd");

    $mail = popen($sendmail_cmd, "w");

    if (!$mail) {
        error_log($logPrefix . "Failed to open sendmail pipe.");
        return ['success' => false, 'message' => 'Failed to open sendmail pipe.'];
    }

    fputs($mail, "To: $to\n");
    fputs($mail, "Subject: $subject\n");
    fputs($mail, $headers . "\n");
    fputs($mail, $message);
    $exitCode = pclose($mail);

    if ($exitCode === 0) {
        error_log($logPrefix . "Sendmail accepted the email (exit code 0).");
        return ['success' => true, 'message' => 'Email successfully handed off to sendmail.'];
    } else {
        error_log($logPrefix . "Sendmail command exited with non-zero code: $exitCode");
        return ['success' => false, 'message' => "Sendmail rejected the email or failed (exit code $exitCode). Check logs for details."];
    }
}

// Function to check mail queue (simple version)
function checkMailQueueSimple() {
    // Check if command exists before trying to run
    if (!is_executable(MAILQ_COMMAND)) {
         return ['status' => 'error', 'content' => MAILQ_COMMAND . ' not found or not executable.', 'is_empty' => null];
    }

    $output = shell_exec(MAILQ_COMMAND . ' 2>&1'); // Capture stderr too
    if ($output === null) {
        // This usually indicates a failure to execute, maybe permissions despite is_executable passing?
        error_log("[checkMailQueueSimple] Failed to execute mailq command, shell_exec returned null.");
        return ['status' => 'error', 'content' => 'Failed to execute mailq command.', 'is_empty' => null];
    }
    $outputTrimmed = trim(preg_replace('/\x1b\[[0-9;]*m/', '', $output)); // Clean output
    $isEmpty = (stripos($outputTrimmed, 'Mail queue is empty') !== false) || (strlen($outputTrimmed) == 0);

    return ['status' => 'ok', 'content' => $output, 'is_empty' => $isEmpty]; // Return raw output
}

// Function to get recent logs from /var/log/maillog using sudo tail
function getRecentPostfixLogsSimple($startTime) { // $startTime is no longer used but kept for compatibility
     if (!is_executable(SUDO_COMMAND) || !is_executable(TAIL_COMMAND)) {
          return ['status' => 'error', 'content' => 'sudo or tail command not found or not executable. Cannot fetch logs.'];
     }
     if (!file_exists(MAILLOG_PATH)) {
         return ['status' => 'error', 'content' => 'Mail log file not found: ' . MAILLOG_PATH];
     }

    // Construct the command using sudo
    $tailCmd = SUDO_COMMAND . " " . TAIL_COMMAND . " -n " . LOG_LINES_TO_FETCH . " " . escapeshellarg(MAILLOG_PATH);

    error_log("[getRecentPostfixLogsSimple] Executing: $tailCmd");
    $output = shell_exec($tailCmd . ' 2>&1'); // Capture stderr

    $trimmedOutput = trim($output);
    if (empty($trimmedOutput)) {
         return ['status' => 'ok', 'content' => '-- No recent log entries found via `sudo tail` (fetched last ' . LOG_LINES_TO_FETCH . ' lines) --'];
    }

    // Add a header indicating the source and method
    $logHeader = "--- Last " . LOG_LINES_TO_FETCH . " lines from " . MAILLOG_PATH . " ---\n";
    // Return the successful log output, ensuring it's safe for HTML display
    return ['status' => 'ok', 'content' => $logHeader . htmlspecialchars($trimmedOutput)];
}


// --- Main Request Handling ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'testEmail') {
    error_log("Received test email request (Simple Version)");
    header('Content-Type: application/json');
    $response = [
        'success' => false, // Default to false
        'message' => 'Processing...',
        'mailq_output' => '[Not Checked Yet]',
        'log_output' => '[Not Checked Yet]',
    ];

    // 1. Basic Command Checks
    $permissionErrors = checkCommandPermissions();
    $criticalErrors = array_filter($permissionErrors, function($msg) {
        return strpos(strtolower($msg), 'error:') === 0;
    });
    $infoWarnings = array_filter($permissionErrors, function($msg) {
        return strpos(strtolower($msg), 'error:') !== 0;
    });

    // Combine messages for display, showing info/warnings even if critical errors exist
    $combinedMessages = implode(" ", $permissionErrors);

    if (!empty($criticalErrors)) {
        $response['message'] = "Prerequisite Error: " . $combinedMessages;
        // Add the detailed error to log_output if available from check
        $logCheckResult = getRecentPostfixLogsSimple(time()); // Attempt log check early for errors
        if ($logCheckResult['status'] === 'error') {
             $response['log_output'] = $logCheckResult['content'];
        }
        error_log("Command permission errors: " . implode("; ", $permissionErrors));
        echo json_encode($response);
        exit;
    } else if (!empty($infoWarnings)) {
         // If only info/warnings, proceed but show them in the log area later? Or status? Let's add to message for now.
         $response['message'] = "Info/Warnings: " . implode(" ", $infoWarnings) . " Proceeding...";
         error_log("Command permission warnings: " . implode("; ", $infoWarnings));
         // Don't exit here, allow the test to proceed
    }


    // 2. Get Settings
    $to = readMailFileContent('/var/lib/blesk/emailaddr');
    $from = readMailFileContent('/var/lib/blesk/emailsender');
    error_log("Current settings - To: $to, From: $from");

    if (empty($to) || empty($from)) {
        $response['message'] = 'Email settings are not configured. Please set up your email address and sender first.';
        $response['mailq_output'] = '[Not Applicable]';
        $response['log_output'] = '[Not Applicable]';
        error_log("Email settings not configured");
        echo json_encode($response);
        exit;
    }
     // Basic validation (optional but recommended)
    if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Invalid "To" email address format: ' . htmlspecialchars($to);
        $response['mailq_output'] = '[Not Applicable]';
        $response['log_output'] = '[Not Applicable]';
        error_log("Invalid To address: $to");
        echo json_encode($response);
        exit;
    }
    // Basic From validation
    if (!preg_match('/^[\w.-]+$/', $from) && !filter_var($from, FILTER_VALIDATE_EMAIL) && !preg_match('/^.+<.*@.*>$/', $from) ) {
         $response['message'] = 'Invalid "From" sender format. Use a simple name, an email address, or "Name <<EMAIL>>": ' . htmlspecialchars($from);
         $response['mailq_output'] = '[Not Applicable]';
         $response['log_output'] = '[Not Applicable]';
         error_log("Invalid From value: $from");
         echo json_encode($response);
         exit;
    }

    // Record start time just before sending
    $startTime = time();

    // 3. Attempt to Send Email
    $sendResult = sendTestEmailSimple($to, $from);

    if (!$sendResult['success']) {
        // Sendmail itself failed immediately
        $response['message'] = "Initial send failed: " . $sendResult['message'];
        error_log("Initial send failed: " . $sendResult['message']);
        // Still try to get logs for context
        sleep(1); // Give logs a tiny moment
        $logs = getRecentPostfixLogsSimple($startTime);
        $response['log_output'] = $logs['content']; // Show logs even on initial failure
        $response['mailq_output'] = '[Send Failed]'; // Queue wasn't checked as send failed
        echo json_encode($response);
        exit;
    }

    // Email handed off to sendmail successfully
    error_log("Email handed off to sendmail. Waiting " . POST_SEND_WAIT_SECONDS . " seconds...");
    sleep(POST_SEND_WAIT_SECONDS); // Wait a bit for Postfix to process

    // 4. Check Mail Queue
    error_log("Checking mail queue...");
    $queueCheck = checkMailQueueSimple();
    $response['mailq_output'] = $queueCheck['content']; // Store raw mailq output

    // 5. Determine Success based *only* on Mailq Empty status (as requested)
    if ($queueCheck['status'] === 'ok' && $queueCheck['is_empty']) {
        $response['success'] = true;
        $response['message'] = 'Test email sent. Mail queue is empty, indicating successful processing or relay.';
        error_log("Mail queue check: Empty. Assuming success.");
    } elseif ($queueCheck['status'] === 'ok' && !$queueCheck['is_empty']) {
        $response['success'] = false; // Queue not empty, considered "not good" per user request
        $response['message'] = 'Test email sent, but the mail queue is NOT empty. This indicates an issue with the SMTP server configuration. Please verify your SMTP settings and ensure the server is properly configured and reachable. Check logs for more details.';
        error_log("Mail queue check: Not Empty - SMTP server issue suspected.");
    } else {
        // Mailq check failed
        $response['success'] = false; // Cannot confirm success if mailq failed
        $response['message'] = 'Test email sent, but could not check mail queue status: ' . $queueCheck['content'] . '. Check logs.';
        error_log("Mail queue check failed: " . $queueCheck['content']);
    }

    // 6. Get Logs (ALWAYS get logs)
    error_log("Retrieving Postfix logs...");
    $logs = getRecentPostfixLogsSimple($startTime);
    $response['log_output'] = $logs['content']; // Store raw log output

    // Prepend any initial warnings to the final message if success is false
    if (!$response['success'] && !empty($infoWarnings)) {
        $response['message'] = implode(" ", $infoWarnings) . " " . $response['message'];
    }


    // Log final status
    error_log("Final Status: Success=" . ($response['success'] ? 'Yes' : 'No') . ", Message=" . $response['message']);

    // 7. Send Response
    error_log("Sending response: " . json_encode($response));
    echo json_encode($response);
    exit;
}
?>