<?php

// Function to safely read file content
function readMailFileContent($filePath) {
    if (file_exists($filePath) && is_readable($filePath)) {
        // Trim whitespace, including potential trailing newline
        return trim(file_get_contents($filePath));
    }
    return ''; // Return empty string if file doesn't exist or isn't readable
}

// You can add other mail-related helper functions here in the future.

?>