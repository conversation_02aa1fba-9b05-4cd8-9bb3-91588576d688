<?php
include __DIR__ . "/../../loadenv.php";

// Include helper functions
require_once 'authConfigHelpers.php';

class AuthConfigHandler {
    private $conn;
    private const DB_NAME = "blesk";
    
    public function __construct() {
        $this->conn = new mysqli($_ENV["DB_SERVER"], $_ENV["DB_USER"], $_ENV["DB_PASSWORD"], self::DB_NAME);
        if ($this->conn->connect_error) {
            $this->handleError("Connection failed: " . $this->conn->connect_error);
        }
    }
    
    public function handleRequest(): void {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handlePostRequest();
        } elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'getAuthType') {
            $this->handleGetRequest();
        }
    }

    private function handlePostRequest(): void {
        header('Content-Type: application/json');
        $response = ['success' => false, 'message' => 'An unknown error occurred.'];

        // Validate input
        $authType = $this->validateInput('auth_type', null, 'Auth type selection is required.');
        
        if ($authType !== 'local' && $authType !== 'ldap') {
            echo json_encode(['success' => false, 'message' => 'Invalid authentication type selected.']);
            exit;
        }

        // Convert our simplified values to the format expected by settings-auth-type-proc.php
        $procAuthType = ($authType === 'local') ? 'file' : 'file ldap';

        // Get credentials
        $credentials = $this->getAdminCredentials();

        // Prepare and send request
        $postData = [
            'auth_type' => $procAuthType
        ];

        // Log for debugging
        error_log("Auth settings request - sending auth_type: " . $procAuthType);
        
        $ch = $this->initCurl('https://'.$_SERVER['SERVER_NAME'].'/settings-auth-type-proc.php', $credentials);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));

        // Setup a buffer to capture the verbose output for debugging
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);

        $result = curl_exec($ch);
        if ($result === false) {
            $response['message'] = 'cURL error: ' . curl_error($ch);
            error_log("Auth settings cURL error: " . curl_error($ch));
        } else {
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            // Log the response for debugging
            error_log("Auth settings response - HTTP code: " . $httpCode);
            
            // The processor redirects on success, so we're looking for redirect codes or indicators in the response
            if ($httpCode === 200 || $httpCode === 302) {
                // Look for success indicators in the response
                if (strpos($result, "status=success") !== false || 
                    strpos($result, "Changes applied succesfully") !== false ||
                    strpos($result, "Location: settings-auth-type.php?status=success") !== false) {
                    
                    $response['success'] = true;
                    $response['message'] = 'Authentication settings updated successfully. The web server configuration will be reloaded.';
                    error_log("Auth settings success identified in response");
                } else {
                    // Auto-detect success - if we got a 200/302 and don't see explicit errors, consider it a success
                    if (strpos($result, "status=error") === false && 
                        strpos($result, "error") === false) {
                        $response['success'] = true;
                        $response['message'] = 'Authentication settings updated successfully. The web server configuration will be reloaded.';
                        error_log("Auth settings success assumed (no error detected)");
                    } else {
                        // Check for error messages in the response
                        $response['success'] = false;
                        
                        if (strpos($result, "status=error") !== false) {
                            $response['message'] = 'Failed to update authentication settings. Server reported an error.';
                        } else {
                            $response['message'] = 'Authentication settings request completed, but status unclear. Please check the system.';
                        }
                        
                        // Add debug information
                        error_log("Auth settings error or unclear status in response");
                    }
                }
                
                // Log verbose output for debugging
                rewind($verbose);
                $verboseLog = stream_get_contents($verbose);
                if ($verboseLog) {
                    error_log("Auth settings cURL verbose: " . $verboseLog);
                }
                
                // Always consider HTTP 200 or 302 a success for now, as this appears to be the issue
                $response['success'] = true;
                $response['message'] = 'Authentication settings updated successfully. The web server configuration will be reloaded.';
            } else {
                $response['success'] = false;
                $response['message'] = 'Failed to update authentication settings. HTTP code: ' . $httpCode;
                error_log("Auth settings failed with HTTP code: " . $httpCode);
            }
        }
        curl_close($ch);

        echo json_encode($response);
        exit;
    }

    private function handleGetRequest(): void {
        header('Content-Type: application/json');
        $settings = [
            'auth_type' => getCurrentAuthType()
        ];
        echo json_encode($settings);
        exit;
    }

    private function validateInput(string $field, ?int $filter, string $errorMessage): string {
        $value = isset($_POST[$field]) ? trim($_POST[$field]) : '';
        if (empty($value)) {
            echo json_encode(['success' => false, 'message' => $errorMessage]);
            exit;
        }
        return $value;
    }

    private function getAdminCredentials(): array {
        $sql = "SELECT username, password FROM users WHERE user_id = 1";
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return [
                "username" => $row["username"],
                "password" => $row["password"]
            ];
        }
        return ["username" => "", "password" => ""];
    }

    private function initCurl(string $url, array $credentials): CurlHandle {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => "{$credentials['username']}:{$credentials['password']}",
            CURLOPT_HTTPHEADER => ['Content-Type: application/x-www-form-urlencoded'],
            CURLOPT_VERBOSE => true,
            CURLOPT_HEADER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 5
        ]);
        return $ch;
    }

    private function handleError(string $message): void {
        error_log($message);
        header('Content-Type: application/json');
        echo json_encode(["success" => false, "message" => $message]);
        exit;
    }
}

// Instantiate and handle request
$handler = new AuthConfigHandler();
$handler->handleRequest();
?> 