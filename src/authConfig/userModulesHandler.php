<?php
include __DIR__ . "/../../loadenv.php";

/**
 * Class to handle user module assignments
 */
class UserModulesHandler {
    private $conn;
    private const DB_NAME = "blesk";
    private const MODULE_NAMES = [
        1 => 'BGA',
        2 => 'APM',
        3 => 'NDD',
        4 => 'NPM',
        5 => 'NTA',
        6 => 'SPM',
        7 => 'ELM',
        8 => 'NCM',
        9 => 'NSM',
        10 => 'ALM'
    ];
    
    public function __construct() {
        $this->conn = new mysqli($_ENV["DB_SERVER"], $_ENV["DB_USER"], $_ENV["DB_PASSWORD"], self::DB_NAME);
        if ($this->conn->connect_error) {
            $this->handleError("Connection failed: " . $this->conn->connect_error);
        }
    }
    
    /**
     * Get all users with their module assignments
     * 
     * @return array Users with their module assignments
     */
    public function getUsersWithModules(): array {
        $users = $this->getUsers();
        $moduleAssignments = $this->getModuleAssignments();
        
        // Assign modules to each user
        foreach ($users as &$user) {
            $userId = $user['user_id'];
            $user['modules'] = [];
            
            // Initialize all modules as not assigned
            foreach (self::MODULE_NAMES as $moduleId => $moduleName) {
                $user['modules'][$moduleId] = [
                    'id' => $moduleId,
                    'name' => $moduleName,
                    'assigned' => false
                ];
            }
            
            // Mark assigned modules
            if (isset($moduleAssignments[$userId])) {
                foreach ($moduleAssignments[$userId] as $moduleId) {
                    if (isset($user['modules'][$moduleId])) {
                        $user['modules'][$moduleId]['assigned'] = true;
                    }
                }
            }
        }
        
        return $users;
    }
    
    /**
     * Get all users from the database
     * 
     * @return array Users
     */
    private function getUsers(): array {
        $users = [];
        $sql = "SELECT user_id, username, password FROM users ORDER BY user_id";
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $users[] = $row;
            }
        }
        
        return $users;
    }
    
    /**
     * Get all module assignments
     * 
     * @return array Module assignments indexed by user_id
     */
    private function getModuleAssignments(): array {
        $assignments = [];
        $sql = "SELECT user_id, module_id FROM lnkUsersToModules ORDER BY user_id, module_id";
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $userId = $row['user_id'];
                $moduleId = $row['module_id'];
                
                if (!isset($assignments[$userId])) {
                    $assignments[$userId] = [];
                }
                
                $assignments[$userId][] = $moduleId;
            }
        }
        
        return $assignments;
    }
    
    /**
     * Update user password and module access
     * 
     * @param array $data User data to update
     * @return array Result of the operation
     */
    public function updateUser(array $data): array {
        if (empty($data['user_id']) || empty($data['username']) || empty($data['password'])) {
            return ['success' => false, 'message' => 'Missing required fields'];
        }
        
        try {
            // Get admin credentials for authentication
            $credentials = $this->getAdminCredentials();
            if (!$credentials) {
                return ['success' => false, 'message' => 'Could not retrieve admin credentials'];
            }

            // We need to forward this request to the settings-auth-user-edit-proc.php script
            // Create a cURL request to handle the processing
            $ch = curl_init();
            
            // Set URL to the processor
            curl_setopt($ch, CURLOPT_URL, 'https://' . $_SERVER['SERVER_NAME'] . '/settings-auth-user-edit-proc.php');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HEADER, true);
            
            // Set authentication
            curl_setopt($ch, CURLOPT_USERPWD, $credentials['username'] . ':' . $credentials['password']);
            
            // Execute cURL session and get the response
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            // Check for errors
            if (curl_errno($ch)) {
                error_log('cURL error: ' . curl_error($ch));
                return ['success' => false, 'message' => 'Error connecting to the processor: ' . curl_error($ch)];
            }
            
            curl_close($ch);
            
            // Process response - if we get a 302 redirect to a success page, it worked
            if ($httpCode === 302 || strpos($response, 'status=success') !== false) {
                return ['success' => true, 'message' => 'User updated successfully'];
            } else {
                error_log('Failed to update user. HTTP Code: ' . $httpCode);
                error_log('Response: ' . $response);
                return ['success' => false, 'message' => 'Failed to update user. Please check the server logs.'];
            }
        } catch (Exception $e) {
            error_log('Exception updating user: ' . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get admin credentials for authentication
     *
     * @return array|null Array with username and password or null on failure
     */
    private function getAdminCredentials(): ?array {
        try {
            $sql = "SELECT username, password FROM users WHERE user_id = 1";
            $result = $this->conn->query($sql);
            
            if ($result && $result->num_rows > 0) {
                $row = $result->fetch_assoc();
                return [
                    "username" => $row["username"],
                    "password" => $row["password"]
                ];
            }
            return null;
        } catch (Exception $e) {
            error_log('Error getting admin credentials: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Handle errors
     * 
     * @param string $message Error message
     */
    private function handleError(string $message): void {
        error_log($message);
        header('Content-Type: application/json');
        echo json_encode(["success" => false, "message" => $message]);
        exit;
    }
    
    /**
     * Handle AJAX requests
     */
    public function handleRequest(): void {
        header('Content-Type: application/json');
        
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            if (isset($_GET['action']) && $_GET['action'] === 'getUserModules') {
                echo json_encode([
                    'success' => true,
                    'users' => $this->getUsersWithModules()
                ]);
                exit;
            }
        } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Handle user update
            echo json_encode($this->updateUser($_POST));
            exit;
        }
        
        echo json_encode(['success' => false, 'message' => 'Invalid request']);
        exit;
    }
}

// Handle requests if this file is accessed directly
$handler = new UserModulesHandler();
$handler->handleRequest();
?> 