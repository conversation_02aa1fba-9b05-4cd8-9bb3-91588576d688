<?php

/**
 * Read the current authentication type from settings
 * @return string Authentication type ('local' or 'ldap')
 */
function getCurrentAuthType() {
    // Default to 'local' if settings file doesn't exist or can't be read
    $authType = 'local';
    
    // Path to settings file
    $settingsAuthFile = '/var/www/html/include/settings-auth.php';
    
    if (file_exists($settingsAuthFile) && is_readable($settingsAuthFile)) {
        $settingsAuth = parse_ini_file($settingsAuthFile);
        
        if (isset($settingsAuth['auth_type'])) {
            if ($settingsAuth['auth_type'] === 'file') {
                $authType = 'local';
            } else if ($settingsAuth['auth_type'] === 'ldap') {
                $authType = 'ldap';
            } else if ($settingsAuth['auth_type'] === 'file ldap') {
                $authType = 'ldap'; // Both local and LDAP
            }
        }
    }
    
    return $authType;
}

?> 