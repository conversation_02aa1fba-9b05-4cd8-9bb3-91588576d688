<?php
include __DIR__ . "/../../loadenv.php";

/**
 * Handler for LDAP settings operations
 */
class LdapSettingsHandler {
    private $conn;
    private const DB_NAME = "blesk";
    
    public function __construct() {
        $this->conn = new mysqli($_ENV["DB_SERVER"], $_ENV["DB_USER"], $_ENV["DB_PASSWORD"], self::DB_NAME);
        if ($this->conn->connect_error) {
            $this->handleError("Connection failed: " . $this->conn->connect_error);
        }
    }
    
    /**
     * Handle API requests
     */
    public function handleRequest() {
        header('Content-Type: application/json');
        
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            if (isset($_GET['action']) && $_GET['action'] === 'getLdapSettings') {
                $this->getLdapSettings();
                exit;
            }
        }
        
        echo json_encode(['success' => false, 'message' => 'Invalid request']);
        exit;
    }
    
    /**
     * Get current LDAP settings
     */
    private function getLdapSettings() {
        $settings = [
            'server' => '',
            'basedn' => '',
            'rootdn' => '',
            'password' => '' // Password will be masked for security
        ];
        
        // Direct query to get settings from the authldaps table
        $sql = "SELECT host, basedn, rootdn, rootdn_passwd FROM authldaps";
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $settings['server'] = $row['host'] ?? '';
            $settings['basedn'] = $row['basedn'] ?? '';
            $settings['rootdn'] = $row['rootdn'] ?? '';
            $settings['password'] = $row['rootdn_passwd'] ?? '';
        } else {
            // Fall back to settings file if no database records
            $settingsFile = '../../../include/settings-auth.php';
            if (file_exists($settingsFile)) {
                $authSettings = parse_ini_file($settingsFile);
                
                if ($authSettings) {
                    $settings['server'] = $authSettings['ldap_server'] ?? '';
                    $settings['basedn'] = $authSettings['ldap_basedn'] ?? '';
                    $settings['rootdn'] = $authSettings['ldap_rootdn'] ?? '';
                    // Password is typically not stored in the settings file
                }
            }
        }
        
        echo json_encode([
            'success' => true,
            'settings' => $settings
        ]);
    }
    
    /**
     * Check if a table exists in the database
     * 
     * @param string $database Database name
     * @param string $table Table name
     * @return bool Whether the table exists
     */
    private function checkTableExists($database, $table) {
        $sql = "SELECT COUNT(*) AS count FROM information_schema.tables 
                WHERE table_schema = '$database' AND table_name = '$table'";
        $result = $this->conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return (int)$row['count'] > 0;
        }
        
        return false;
    }
    
    /**
     * Handle errors
     * 
     * @param string $message Error message
     */
    private function handleError($message) {
        error_log($message);
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => $message]);
        exit;
    }
}

// Handle the request
$handler = new LdapSettingsHandler();
$handler->handleRequest();
?> 