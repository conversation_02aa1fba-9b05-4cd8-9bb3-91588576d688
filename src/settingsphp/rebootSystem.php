<?php
// Check if the request is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Execute the reboot command (requires root privileges)
    exec('sudo reboot', $output, $return_var);
    
    // Check if command executed successfully
    if ($return_var === 0) {
        echo "System is rebooting...";
        header('Location: /bubblemaps/infra.php');
    } else {
        echo "Failed to initiate reboot. You may need root privileges.";
    }
} else {
    // Redirect if accessed directly
    header('Location: /bubblemaps/infra.php');
    exit;
}
?>