<?php
// src/settingsphp/systemLogs.php

session_start();

// Define available log files (hardcoded list)
$log_options = [
    '/var/log/arpwatch2.log' => 'Arpwatch',
    '/var/log/blesk.log' => 'Blesk',
    '/var/log/nagios/nagios.log' => 'APM (Nagios)',
    '/var/log/cron' => 'Cron',
    '/var/log/httpd/ssl_error_log' => 'Httpd (Apache)',
    '/var/log/maillog' => 'Maillog',
    '/var/log/mysqld.log' => 'Mysql (Blesk5)',
    '/var/log/mysql/mysqld.log' => 'Mysql (Blesk8)',
    '/var/log/messages' => 'Messages',
    '/var/log/pnp4nagios/npcd.log' => 'Npcd',
    '/var/log/yum.log' => 'Updates (Yum)',
    '/var/log/redis/redis.log' => 'Redis',
    '/home/<USER>/logs/netdisco-backend.log' => 'Netdisco',
    '/var/log/elasticsearch/elasticsearch.log' => 'ELM (Elastic)',
    '/var/log/logstash/logstash.log' => 'ELM (Logstash)',
    '/var/log/fusioninventory/fusioninventory.log' => 'ALCM (GLPI)',
];

// Log file selection is removed. We will fetch from all files.

// Number of lines to show
$lines_to_show = 25; // Default
if (!empty($_POST['lines_to_show'])) {
    // Add 200 to the options and update max_range
    $lines = filter_var($_POST['lines_to_show'], FILTER_VALIDATE_INT, ['options' => ['min_range' => 1, 'max_range' => 200]]);
    $lines_to_show = $lines !== false ? $lines : 25;
}

// Fetch log contents
$log_contents = '';
foreach ($log_options as $file_path => $label) {
    $current_logfile = $file_path;

    // Handle special case for elasticsearch dynamically
    if ($file_path == '/var/log/elasticsearch/elasticsearch.log' && !file_exists($file_path)) {
        $current_logfile = '/var/log/elasticsearch/elastic.log';
    } else {
        $current_logfile = $file_path;
    }

    // Check if file exists and is readable
    if (file_exists($current_logfile) && is_readable($current_logfile)) {
        $file_lines = file($current_logfile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        // Check if reading was successful and file is not empty
        if ($file_lines !== false && !empty($file_lines)) {
            $lines = array_slice(array_reverse($file_lines), 0, $lines_to_show);

            // Check if after slicing we still have lines (handles cases where file has fewer lines than requested)
            if (!empty($lines)) {
                // Add header only if we have content
                $log_contents .= "<span class='log-header'>--- Logs for: " . htmlspecialchars($label) . " (" . htmlspecialchars($current_logfile) . ") ---</span>\n";
                $log_contents .= implode("\n", array_reverse($lines)) . "\n\n"; // Reverse back for chronological order
            }
            // No else needed: If file is empty or has fewer lines than requested resulting in empty $lines, skip this section.
        }
        // No else needed: If file reading failed, skip this section. Log the error if desired.
        // elseif ($file_lines === false) { error_log("systemLogs.php: Could not read $current_logfile"); }
    }
    // No else needed: If file doesn't exist or isn't readable, skip this section.
    // else { error_log("systemLogs.php: Cannot access $current_logfile"); }
}
// Trim trailing newlines that might accumulate if the last file(s) were skipped
$log_contents = rtrim($log_contents);

// Store data in session for the view
// $_SESSION['logfile'] is no longer needed
$_SESSION['lines_to_show'] = $lines_to_show;
$_SESSION['log_contents'] = $log_contents;
$_SESSION['log_options'] = $log_options;

// Check if this is an AJAX request
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

if ($isAjax) {
    // For AJAX requests, include the content and exit
    include 'systemLogsContent.php';
    exit;
} else {
    // For initial load, include the content (this will be handled by the modal's fetch)
    include 'systemLogsContent.php';
}
?>