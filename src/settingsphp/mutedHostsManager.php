<?php
// Include database connection helper
require_once '../../loadenv.php';

// Function to get database connection
function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

// Function to unmute a host (set muted=0)
function unmuteHost($ip, $infra) {
    $conn = getDatabaseConnection();
    
    // Prepare the SQL statement
    $sql = "UPDATE hosts SET muted = 0 WHERE ip = ? AND infra = ?";
    
    if ($stmt = $conn->prepare($sql)) {
        $stmt->bind_param('ss', $ip, $infra);
        
        // Execute the statement
        $success = $stmt->execute();
        $affected = $stmt->affected_rows;
        $stmt->close();
        
        // Return success if at least one row was updated
        $result = [
            'success' => $success && $affected > 0,
            'message' => $success ? 'Host removed from service discovery exclusions' : 'Failed to update host',
            'affected' => $affected
        ];
    } else {
        $result = [
            'success' => false,
            'message' => 'Failed to prepare SQL statement: ' . $conn->error
        ];
    }
    
    $conn->close();
    return $result;
}

// Process AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set response header
    header('Content-Type: application/json');
    
    // Check if the action is valid
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'unmute_host':
                // Check if required parameters are provided
                if (isset($_POST['ip']) && isset($_POST['infra'])) {
                    $ip = $_POST['ip'];
                    $infra = $_POST['infra'];
                    
                    // Perform the unmute operation
                    $result = unmuteHost($ip, $infra);
                    
                    // Return JSON response
                    echo json_encode($result);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Missing required parameters (ip, infra)'
                    ]);
                }
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'Invalid action specified'
                ]);
                break;
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No action specified'
        ]);
    }
} else {
    // Handle non-POST requests
    header('HTTP/1.1 405 Method Not Allowed');
    header('Allow: POST');
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST.'
    ]);
}
?> 