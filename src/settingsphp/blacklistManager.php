<?php
// Load environment variables
require_once __DIR__ . '/../../loadenv.php';

// Function to establish database connection
function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"] ?? ''; // Use null coalescing for safety
    $dbname = "bubblemaps";

    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);

    // Check connection
    if ($conn->connect_error) {
        // Log error instead of dying directly in production
        error_log("Database Connection failed: " . $conn->connect_error);
        // Return null or throw an exception for better error handling upstream
        return null;
    }

    return $conn;
}

// Check if this is an AJAX request - will be used only for POST responses
$isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

// Handle POST request for removing from blacklist
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set headers only for AJAX requests to avoid interfering with normal page rendering
    if ($isAjax) {
        header('Content-Type: application/json');
    }
    
    // Prepare the response
    $response = ['success' => false, 'message' => 'Unknown error'];
    
    if (!isset($_POST['action'])) {
        $response['message'] = 'Missing action parameter.';
        if ($isAjax) {
            echo json_encode($response);
            exit;
        }
    }
    
    // Handle bulk removal action
    if ($_POST['action'] === 'remove_multiple_blacklist') {
        if (!isset($_POST['hosts'])) {
            $response['message'] = 'No hosts selected for removal.';
            if ($isAjax) {
                echo json_encode($response);
                exit;
            }
        }
        
        // Log received data for debugging
        error_log("Hosts data received: " . $_POST['hosts']);
        
        // Parse the JSON string into an array
        $hosts = json_decode($_POST['hosts'], true);
        
        if ($hosts === null) {
            error_log("JSON decode error: " . json_last_error_msg() . " - Raw data: " . $_POST['hosts']);
            $response['message'] = 'Invalid hosts data format: ' . json_last_error_msg();
            if ($isAjax) {
                echo json_encode($response);
                exit;
            }
        }
        
        if (empty($hosts)) {
            $response['message'] = 'No hosts selected for removal.';
            if ($isAjax) {
                echo json_encode($response);
                exit;
            }
        }
        
        $conn = getDatabaseConnection();
        
        if (!$conn) {
            $response['message'] = 'Database connection error.';
            if ($isAjax) {
                echo json_encode($response);
                exit;
            }
        }
        
        $successCount = 0;
        $errors = [];
        
        foreach ($hosts as $host) {
            if (!isset($host['ip']) || !isset($host['infra'])) {
                $errors[] = "Missing IP or Infra parameter for a host.";
                continue;
            }
            
            $ipToRemove = $host['ip'];
            $infraToRemove = $host['infra'];
            
            $stmt = $conn->prepare("UPDATE hosts SET blacklist = 0, apmStatus = 'not-added' WHERE ip = ? AND infra = ?");
            if (!$stmt) {
                $errors[] = "Prepare failed: (" . $conn->errno . ") " . $conn->error;
                continue;
            }
            
            $stmt->bind_param("ss", $ipToRemove, $infraToRemove);
            
            if ($stmt->execute()) {
                if ($stmt->affected_rows > 0) {
                    $successCount++;
                }
            } else {
                $errors[] = "Execute failed for IP $ipToRemove: (" . $stmt->errno . ") " . $stmt->error;
            }
            
            $stmt->close();
        }
        
        $conn->close();
        
        if ($successCount > 0) {
            $response = [
                'success' => true, 
                'message' => "$successCount host" . ($successCount > 1 ? "s" : "") . " removed from blacklist.",
                'errors' => $errors
            ];
        } else {
            $response = [
                'success' => false, 
                'message' => 'Failed to remove any hosts from blacklist.',
                'errors' => $errors
            ];
        }
        
        if ($isAjax) {
            echo json_encode($response);
            exit;
        }
    }
    // Handle single host removal
    else if ($_POST['action'] === 'remove_blacklist') {
        if (!isset($_POST['ip']) || !isset($_POST['infra'])) {
            $response['message'] = 'Missing IP or Infra parameter.';
            if ($isAjax) {
                echo json_encode($response);
                exit;
            }
        }

        $ipToRemove = $_POST['ip'];
        $infraToRemove = $_POST['infra'];
        $conn = getDatabaseConnection();

        if (!$conn) {
            $response['message'] = 'Database connection error.';
            if ($isAjax) {
                echo json_encode($response);
                exit;
            }
        }

        $stmt = $conn->prepare("UPDATE hosts SET blacklist = 0, apmStatus = 'not-added' WHERE ip = ? AND infra = ?");
        if (!$stmt) {
            error_log("Prepare failed: (" . $conn->errno . ") " . $conn->error);
            $response['message'] = 'Failed to prepare statement.';
            if ($isAjax) {
                echo json_encode($response);
                $conn->close();
                exit;
            }
        }

        $stmt->bind_param("ss", $ipToRemove, $infraToRemove);

        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                $response = ['success' => true, 'message' => 'Host removed from blacklist.'];
            } else {
                // This might happen if the entry was already removed or didn't exist with blacklist=1
                $response = ['success' => true, 'message' => 'Host not found or already removed from blacklist.'];
            }
        } else {
            error_log("Execute failed: (" . $stmt->errno . ") " . $stmt->error);
            $response['message'] = 'Failed to update blacklist status.';
        }

        $stmt->close();
        $conn->close();
        
        if ($isAjax) {
            echo json_encode($response);
            exit;
        }
    }
    // Handle complete host deletion
    else if ($_POST['action'] === 'delete_completely') {
        if (!isset($_POST['ip']) || !isset($_POST['infra'])) {
            $response['message'] = 'Missing IP or Infra parameter.';
            if ($isAjax) {
                echo json_encode($response);
                exit;
            }
        }

        $ipToDelete = $_POST['ip'];
        $infraToDelete = $_POST['infra'];
        $conn = getDatabaseConnection();

        if (!$conn) {
            $response['message'] = 'Database connection error.';
            if ($isAjax) {
                echo json_encode($response);
                exit;
            }
        }

        // First, delete from servicesPending table
        $deletePendingStmt = $conn->prepare("DELETE FROM servicesPending WHERE host_ip = ?");
        if ($deletePendingStmt) {
            $deletePendingStmt->bind_param("s", $ipToDelete);
            $deletePendingStmt->execute();
            $deletePendingStmt->close();
        }

        // Then delete from hosts table
        $stmt = $conn->prepare("DELETE FROM hosts WHERE ip = ? AND infra = ?");
        if (!$stmt) {
            error_log("Prepare failed: (" . $conn->errno . ") " . $conn->error);
            $response['message'] = 'Failed to prepare statement.';
            if ($isAjax) {
                echo json_encode($response);
                $conn->close();
                exit;
            }
        }

        $stmt->bind_param("ss", $ipToDelete, $infraToDelete);

        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                $response = ['success' => true, 'message' => 'Host completely deleted from database.'];
            } else {
                // This might happen if the entry didn't exist
                $response = ['success' => true, 'message' => 'Host not found in database.'];
            }
        } else {
            error_log("Execute failed: (" . $stmt->errno . ") " . $stmt->error);
            $response['message'] = 'Failed to delete host from database.';
        }

        $stmt->close();
        $conn->close();
        
        if ($isAjax) {
            echo json_encode($response);
            exit;
        }
    }
    else {
        // Unknown action
        $response['message'] = 'Unknown action: ' . $_POST['action'];
        if ($isAjax) {
            echo json_encode($response);
            exit;
        }
    }
}

// Handle GET request for fetching blacklist (default action)
$conn = getDatabaseConnection();

if (!$conn) {
    if ($isAjax && $_SERVER['REQUEST_METHOD'] === 'POST') {
        echo json_encode(['success' => false, 'message' => 'Error connecting to the database.']);
        exit;
    } else {
        echo '<div class="message message-error">Error connecting to the database.</div>';
        exit;
    }
}

$sql = "SELECT ip, hostname, infra, subnet FROM hosts WHERE blacklist = 1 ORDER BY subnet, infra, ip";
$result = $conn->query($sql);

if (!$result) {
    if ($isAjax && $_SERVER['REQUEST_METHOD'] === 'POST') {
        echo json_encode(['success' => false, 'message' => 'Error fetching blacklist: ' . $conn->error]);
        exit;
    } else {
        echo '<div class="message message-error">Error fetching blacklist: ' . htmlspecialchars($conn->error) . '</div>';
        $conn->close();
        exit;
    }
}

// Group results by subnet
$groupedBySubnet = [];
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $subnet = !empty($row['subnet']) ? $row['subnet'] : 'Unknown';
        if (!isset($groupedBySubnet[$subnet])) {
            $groupedBySubnet[$subnet] = [];
        }
        $groupedBySubnet[$subnet][] = $row;
    }
}

// For GET requests or non-AJAX requests, always render the HTML
// We don't need a conditional here - this is the default view of the page
?>

<div class="blacklist-manager">
    <?php if (empty($groupedBySubnet)): ?>
        <div class="message message-info">No hosts found in the blacklist.</div>
    <?php else: ?>
        <!-- Bulk Action UI -->
        <div class="blacklist-actions">
            <button id="remove-selected-btn" class="btn-stop" onclick="document.querySelectorAll('.select-host:checked').length > 0 ? removeSelected() : alert('No hosts selected for removal.')">
                <i class="fa fa-undo"></i>&nbsp;Remove Selected (<span id="selected-count">0</span>)
            </button>
            <div class="select-all-wrapper">
                <label class="checkbox-label">
                    <input type="checkbox" id="select-all-blacklist" onclick="toggleAll(this)"> Select All
                </label>
            </div>
        </div>
        
        <div class="service-modules-container">
            <?php foreach ($groupedBySubnet as $subnet => $hosts): ?>
                <details class="module-section" open>
                    <summary class="module-header">
                        <div class="module-name">
                            Subnet: <strong><?php echo htmlspecialchars($subnet); ?></strong>
                            <span class="module-status-indicator">
                                <?php echo count($hosts); ?> host<?php echo count($hosts) > 1 ? 's' : ''; ?>
                            </span>
                            <a href="#" class="subnet-select-all" data-subnet="<?php echo htmlspecialchars($subnet); ?>" onclick="toggleSubnet(null, '<?php echo htmlspecialchars($subnet); ?>'); return false;">
                                Select All
                            </a>
                        </div>
                    </summary>
                    
                    <table class="module-table">
                        <thead>
                            <tr>
                                <th style="width: 40px;"><input type="checkbox" class="select-subnet" data-subnet="<?php echo htmlspecialchars($subnet); ?>" onclick="toggleSubnet(this, '<?php echo htmlspecialchars($subnet); ?>')"></th>
                                <th>IP Address</th>
                                <th>Hostname</th>
                                <th>Infrastructure</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($hosts as $row): ?>
                                <tr data-ip="<?php echo htmlspecialchars($row['ip']); ?>" data-infra="<?php echo htmlspecialchars($row['infra']); ?>">
                                    <td>
                                        <input type="checkbox" class="select-host" 
                                               data-ip="<?php echo htmlspecialchars($row['ip']); ?>" 
                                               data-infra="<?php echo htmlspecialchars($row['infra']); ?>"
                                               data-subnet="<?php echo htmlspecialchars($subnet); ?>"
                                               onclick="updateSelection()">
                                    </td>
                                    <td><?php echo htmlspecialchars($row['ip']); ?></td>
                                    <td><?php echo !empty($row['hostname']) ? htmlspecialchars($row['hostname']) : ''; ?></td>
                                    <td><?php echo htmlspecialchars($row['infra']); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-remove-blacklist btn-stop"
                                                    data-ip="<?php echo htmlspecialchars($row['ip']); ?>"
                                                    data-infra="<?php echo htmlspecialchars($row['infra']); ?>"
                                                    title="Remove from blacklist">
                                                    <i class="fa fa-undo"></i>
                                            </button>
                                            <button class="btn-delete-completely btn-critical"
                                                    data-ip="<?php echo htmlspecialchars($row['ip']); ?>"
                                                    data-infra="<?php echo htmlspecialchars($row['infra']); ?>"
                                                    onclick="deleteCompletelyFromBlacklist('<?php echo htmlspecialchars($row['ip']); ?>', '<?php echo htmlspecialchars($row['infra']); ?>')"
                                                    title="Delete completely from database">
                                                    <i class="fa fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </details>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<style>
/* Blacklist-specific styles to enhance the accordion */
.blacklist-manager .module-status-indicator {
    background-color: var(--pending-bg);
    color: var(--text);
}

.blacklist-manager .module-section {
    transition: box-shadow 0.3s ease;
}

.blacklist-manager .module-section:hover {
    box-shadow: var(--shadow-hover);
}



/* Icon button styling for blacklist actions */
.blacklist-manager .btn-remove-blacklist,
.blacklist-manager .btn-delete-completely {
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-left: 6px;
    font-size: 14px;
}

/* Remove from blacklist button (undo icon) */
.blacklist-manager .btn-remove-blacklist {
    background-color: var(--pending-bg);
    color: var(--pending);
}

/* Specific styles for dark theme */
:root[data-theme="dark"] .blacklist-manager .btn-remove-blacklist {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

/* Light theme */
:root[data-theme="light"] .blacklist-manager .btn-remove-blacklist {
    background-color: #fff3cd;
    color: #856404;
}

.blacklist-manager .btn-remove-blacklist:hover {
    background-color: rgba(255, 193, 7, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Delete completely button (trash icon) */
.blacklist-manager .btn-delete-completely {
    background-color: var(--critical-bg);
    color: var(--critical);
}

/* Specific styles for dark theme */
:root[data-theme="dark"] .blacklist-manager .btn-delete-completely {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

/* Light theme */
:root[data-theme="light"] .blacklist-manager .btn-delete-completely {
    background-color: #f8d7da;
    color: #721c24;
}

.blacklist-manager .btn-delete-completely:hover {
    background-color: rgba(220, 53, 69, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Make INFRA tag stand out more */
.blacklist-manager td:nth-child(3) {
    color: #4a7fbe;
    font-weight: bold;
}

/* Bold IP addresses for better readability */
.blacklist-manager td:nth-child(2) {
    font-weight: bold;
}

/* Bulk actions styling */
.blacklist-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: var(--surface);
    border-radius: 8px;
    border: 1px solid var(--border);
}

.blacklist-actions button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.select-all-wrapper {
    display: flex;
    align-items: center;
}

.select-all-wrapper input[type="checkbox"] {
    margin-right: 5px;
}

/* Checkbox styling */
.blacklist-manager input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.blacklist-manager th:first-child,
.blacklist-manager td:first-child {
    text-align: center;
}

/* Subnet Select All link styling */
.blacklist-manager .subnet-select-all {
    margin-left: 15px;
    font-size: 12px;
    padding: 3px 8px;
    background-color: var(--pending-bg);
    color: var(--text);
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.blacklist-manager .subnet-select-all:hover {
    background-color: var(--primary);
    color: white;
}

.blacklist-manager .module-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
}

.blacklist-manager th:first-child,
.blacklist-manager td:first-child {
    text-align: center;
    width: 40px;
}

.blacklist-manager th:nth-child(2),
.blacklist-manager td:nth-child(2) {
    width: 20%;
}

.blacklist-manager th:nth-child(3),
.blacklist-manager td:nth-child(3) {
    width: 30%;
    word-break: break-word;
}

.blacklist-manager th:nth-child(4),
.blacklist-manager td:nth-child(4) {
    width: 20%;
}

.blacklist-manager th:last-child,
.blacklist-manager td:last-child {
    width: 15%;
    text-align: center;
}

/* Make column widths responsive on smaller screens */
@media (max-width: 768px) {
    .blacklist-manager .module-table {
        table-layout: auto;
    }
    
    .blacklist-manager th:not(:first-child):not(:last-child),
    .blacklist-manager td:not(:first-child):not(:last-child) {
        width: auto;
    }
}
</style>

<?php
$conn->close();
?>