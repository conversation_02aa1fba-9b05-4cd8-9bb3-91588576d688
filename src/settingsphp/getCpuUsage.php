<?php
function run_command($command) {
    if (strpbrk($command, ';|&`') !== false && php_sapi_name() != 'cli') {
        return null;
    }
    $output = shell_exec($command . ' 2>&1');
    return $output ? trim($output) : null;
}

function calculate_cpu_usage() {
    // Try mpstat first
    $mpstat_output = run_command('mpstat 1 1 | awk \'END{print 100-$NF}\'');
    if ($mpstat_output && is_numeric($mpstat_output)) {
        return round($mpstat_output, 1);
    }

    // Fallback to reading /proc/stat
    $stat1 = file('/proc/stat');
    if ($stat1) {
        sleep(1);
        $stat2 = file('/proc/stat');
        if ($stat2) {
            $info1 = explode(" ", preg_replace("!cpu +!", "", $stat1[0]));
            $info2 = explode(" ", preg_replace("!cpu +!", "", $stat2[0]));
            
            $dif = array();
            $dif['user'] = $info2[0] - $info1[0];
            $dif['nice'] = $info2[1] - $info1[1];
            $dif['sys'] = $info2[2] - $info1[2];
            $dif['idle'] = $info2[3] - $info1[3];
            $total = array_sum($dif);
            if ($total > 0) {
                $percent = 100 - ($dif['idle'] / $total * 100);
                return round($percent, 1);
            }
        }
    }

    // Fallback to top command
    $top_output = run_command('top -bn1 | grep "Cpu(s)"');
    if ($top_output && preg_match('/Cpu\(s\):\s+([\d.]+)%us,\s+([\d.]+)%sy,\s+([\d.]+)%ni,\s+([\d.]+)%id/', $top_output, $matches)) {
        $idle = (float)$matches[4];
        return round(100 - $idle, 1);
    }

    // Fallback to vmstat
    $vmstat_output = run_command('vmstat 1 2 | tail -1');
    if ($vmstat_output && preg_match('/\s+\d+\s+\d+\s+\d+\s+\d+\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)/', $vmstat_output, $matches)) {
        $idle = (int)$matches[15];
        return round(100 - $idle, 1);
    }

    return 'N/A';
}

header('Content-Type: application/json');

$cpu_usage = calculate_cpu_usage();

echo json_encode([
    'cpu_usage' => $cpu_usage,
    'cpu_usage_percent' => $cpu_usage !== 'N/A' ? $cpu_usage . '%' : 'N/A'
]);
?>