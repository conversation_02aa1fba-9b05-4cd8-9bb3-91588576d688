<?php
/**
 * Handles reading and writing to the configuration file at /ndd/include/settings.php
 */

class SettingsConfigHandler {
    private $configPath = '/var/www/html/ndd/include/settings.php';
    
    /**
     * Reads the current configuration values
     */
    public function readConfig() {
        if (!file_exists($this->configPath)) {
            throw new Exception("Configuration file not found at {$this->configPath}");
        }

        // Parse the config file to extract current values
        $configContent = file_get_contents($this->configPath);
        
        // Extract values using regex
        $config = [];
        preg_match('/language\s*=\s*"([^"]+)"/', $configContent, $matches);
        $config['language'] = $matches[1] ?? 'english';
        
        // Windows agent type mapping
        preg_match('/winmon_type\s*=\s*"([^"]+)"/', $configContent, $matches);
        $winmonValue = $matches[1] ?? 'wmi';
        // Map config values to form values
        if ($winmonValue === 'nsclientpp') {
            $winmonValue = 'nsclient'; // Map to form value
        }
        $config['winmon_type'] = $winmonValue;
        
        // Unix/Linux agent type mapping
        preg_match('/unixmon_type\s*=\s*"([^"]+)"/', $configContent, $matches);
        $config['unixmon_type'] = $matches[1] ?? 'snmp'; // No mapping needed for Unix
        
        preg_match('/export_switchports\s*=\s*"([^"]+)"/', $configContent, $matches);
        $config['export_switchports'] = $matches[1] ?? 'uplinks_only';
        
        preg_match('/export_ncm\s*=\s*"([^"]+)"/', $configContent, $matches);
        $config['export_ncm'] = $matches[1] ?? 'yes';
        
        preg_match('/monitor_stp\s*=\s*"([^"]+)"/', $configContent, $matches);
        $config['monitor_stp'] = $matches[1] ?? 'yes';
        
        preg_match('/illegal_chars_snmp\s*=\s*"([^"]+)"/', $configContent, $matches);
        $config['illegal_chars_snmp'] = $matches[1] ?? '!|:$@';
        
        preg_match('/illegal_chars_wmi\s*=\s*"([^"]+)"/', $configContent, $matches);
        $config['illegal_chars_wmi'] = $matches[1] ?? '$%@';
        
        preg_match('/shortname_dot_limit\s*=\s*(\d+)/', $configContent, $matches);
        $config['shortname_dot_limit'] = $matches[1] ?? 1;
        
        return $config;
    }
    
    /**
     * Updates the configuration file with new values
     */
    public function updateConfig($newConfig) {
        if (!file_exists($this->configPath)) {
            throw new Exception("Configuration file not found at {$this->configPath}");
        }

        $configContent = file_get_contents($this->configPath);
        
        // Update each config value
        // Don't update language setting as per user request
        
        // Windows agent type mapping (form -> config)
        $winmonValue = $newConfig['winmon_type'];
        if ($winmonValue === 'nsclient') {
            $winmonValue = 'nsclientpp'; // Map to config value
        }
        $configContent = preg_replace(
            '/winmon_type\s*=\s*"[^"]+"/',
            'winmon_type = "' . $winmonValue . '"',
            $configContent
        );
        
        // Unix/Linux agent type (no mapping needed)
        $configContent = preg_replace(
            '/unixmon_type\s*=\s*"[^"]+"/',
            'unixmon_type = "' . $newConfig['unixmon_type'] . '"',
            $configContent
        );
        
        // Map form values to config file values for switch ports
        $switchPortValue = $newConfig['export_switchports'];
        if ($switchPortValue === 'all_up') {
            $switchPortValue = 'yes'; // Map 'all_up' to 'yes' in config file
        }
        
        $configContent = preg_replace(
            '/export_switchports\s*=\s*"[^"]+"/',
            'export_switchports = "' . $switchPortValue . '"',
            $configContent
        );
        
        $configContent = preg_replace(
            '/export_ncm\s*=\s*"[^"]+"/', 
            'export_ncm = "' . $newConfig['export_ncm'] . '"', 
            $configContent
        );
        
        $configContent = preg_replace(
            '/monitor_stp\s*=\s*"[^"]+"/', 
            'monitor_stp = "' . $newConfig['monitor_stp'] . '"', 
            $configContent
        );
        
        $configContent = preg_replace(
            '/illegal_chars_snmp\s*=\s*"[^"]+"/', 
            'illegal_chars_snmp = "' . $newConfig['illegal_chars_snmp'] . '"', 
            $configContent
        );
        
        $configContent = preg_replace(
            '/illegal_chars_wmi\s*=\s*"[^"]+"/', 
            'illegal_chars_wmi = "' . $newConfig['illegal_chars_wmi'] . '"', 
            $configContent
        );
        
        $configContent = preg_replace(
            '/shortname_dot_limit\s*=\s*\d+/', 
            'shortname_dot_limit = ' . $newConfig['shortname_dot_limit'], 
            $configContent
        );
        
        // Write the updated content back to the file
        file_put_contents($this->configPath, $configContent);
        
        return true;
    }
}

// Handle AJAX requests
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        $handler = new SettingsConfigHandler();
        
        if (isset($_POST['action']) && $_POST['action'] === 'get_config') {
            // Return current config
            echo json_encode($handler->readConfig());
        } 
        elseif (isset($_POST['action']) && $_POST['action'] === 'update_config') {
            // Update config with posted values
            $handler->updateConfig($_POST['config']);
            echo json_encode(['success' => true]);
        }
        else {
            throw new Exception("Invalid action");
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
    
    exit;
}