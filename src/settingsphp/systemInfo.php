<?php
// --- Set timezone (optional, but good practice) ---
// Ensure this matches the timezone setting of your main application if necessary
// date_default_timezone_set('UTC'); // Replace 'UTC' with your server's timezone

// --- Helper Functions ---

/**
 * Executes a shell command and returns the trimmed output.
 * Returns null if the command fails or returns no output.
 */
function run_command($command) {
    // Basic security check (less critical for hardcoded commands)
    if (strpbrk($command, ';|&`') !== false && php_sapi_name() != 'cli') {
         // Log potentially unsafe commands if they were dynamic
         // error_log("Potentially unsafe command attempt blocked: " . $command);
         // return null;
    }
    // Redirect stderr to stdout to potentially capture error messages if needed for debugging
    $output = shell_exec($command . ' 2>&1'); // Added 2>&1
    return $output ? trim($output) : null;
}

/**
 * Formats bytes into a human-readable format (KB, MB, GB, TB).
 */
function format_bytes($bytes, $precision = 2) {
    if ($bytes <= 0) return '0 B';
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    // Ensure division by zero is not possible
    $divisor = pow(1024, $pow);
    if ($divisor == 0) return '0 B'; // Avoid division by zero
    $bytes /= $divisor;
    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * Parses /proc/meminfo to get memory details.
 * Returns an array [total_kb, free_kb, available_kb, swap_total_kb, swap_free_kb]
 */
function get_memory_details() {
    $meminfo_raw = @file_get_contents('/proc/meminfo'); // Use @ to suppress warnings if file not readable
    $data = [];
    if ($meminfo_raw) {
        preg_match('/^MemTotal:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        $data['total_kb'] = isset($matches[1]) ? (int)$matches[1] : 0;

        preg_match('/^MemFree:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        $data['free_kb'] = isset($matches[1]) ? (int)$matches[1] : 0;

        // MemAvailable is generally a better measure of usable memory than MemFree
        preg_match('/^MemAvailable:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        // Fallback to Free + Buffers + Cached calculation if MemAvailable isn't present (less accurate)
        if (isset($matches[1])) {
            $data['available_kb'] = (int)$matches[1];
        } else {
            $buffers_kb = 0;
            $cached_kb = 0;
            preg_match('/^Buffers:\s+(\d+)\s+kB/m', $meminfo_raw, $buf_match);
            if(isset($buf_match[1])) $buffers_kb = (int)$buf_match[1];
            preg_match('/^Cached:\s+(\d+)\s+kB/m', $meminfo_raw, $cache_match);
             // Be careful with just 'Cached', sometimes 'SReclaimable' is better excluded/included depending on kernel version
            if(isset($cache_match[1])) $cached_kb = (int)$cache_match[1];
             // Simple fallback: Free + Buffers + Cached. Might overestimate available.
            $data['available_kb'] = $data['free_kb'] + $buffers_kb + $cached_kb;
        }

        preg_match('/^SwapTotal:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        $data['swap_total_kb'] = isset($matches[1]) ? (int)$matches[1] : 0;

        preg_match('/^SwapFree:\s+(\d+)\s+kB/m', $meminfo_raw, $matches);
        $data['swap_free_kb'] = isset($matches[1]) ? (int)$matches[1] : 0;

    } else {
         // Return zeros if /proc/meminfo cannot be read
         $data = [
             'total_kb' => 0, 'free_kb' => 0, 'available_kb' => 0,
             'swap_total_kb' => 0, 'swap_free_kb' => 0
         ];
    }
    return $data;
}

/**
 * Gets disk usage details for a given mount point (default '/').
 * Returns an array [total_kb, used_kb, free_kb, percent_used]
 */
function get_disk_details($mount_point = '/') {
    $data = ['total_kb' => 0, 'used_kb' => 0, 'free_kb' => 0, 'percent_used' => 0];
    // df -Pk outputs in 1K-blocks. Use -P for POSIX standard output.
    $df_output = run_command("df -Pk " . escapeshellarg($mount_point));
    if ($df_output) {
        $lines = explode("\n", $df_output);
        if (count($lines) > 1) {
            // Find the line corresponding to the mount point (usually the second line for '/')
            $target_line = null;
             // Skip header line (index 0)
            for ($i = 1; $i < count($lines); $i++) {
                $parts = preg_split('/\s+/', trim($lines[$i]));
                // Check if the last element matches the mount point
                if (end($parts) === $mount_point) {
                    $target_line = $lines[$i];
                    break;
                }
            }
            // Fallback: If not found by exact mount point name (e.g., '/dev/sda1' shown instead of '/'),
            // assume the first data line is the one we want if $mount_point is '/'
            if ($target_line === null && $mount_point === '/' && isset($lines[1])) {
                 $target_line = $lines[1];
            }


            if ($target_line !== null) {
                $parts = preg_split('/\s+/', trim($target_line));
                // Expected format (POSIX): Filesystem 1024-blocks Used Available Capacity Mounted on
                // Indices:                    0          1           2    3         4        5
                 if (count($parts) >= 6 && is_numeric($parts[1]) && is_numeric($parts[2]) && is_numeric($parts[3])) {
                     $data['total_kb'] = (int)$parts[1];
                     $data['used_kb'] = (int)$parts[2];
                     $data['free_kb'] = (int)$parts[3];
                     $percent_str = rtrim($parts[4], '%'); // Capacity column
                     $data['percent_used'] = is_numeric($percent_str) ? (float)$percent_str : 0;
                 }
            } else {
                 // Log if the mount point wasn't found in df output
                 // error_log("Could not find mount point '$mount_point' in df output: " . $df_output);
            }
        }
    }
    return $data;
}

/**
 * Determines the CSS class ('success', 'warning', 'critical') based on percentage usage.
 */
function get_threshold_class($percent, $warn_threshold = 70, $crit_threshold = 90) {
    // Ensure thresholds are logical (warning <= critical)
    if ($warn_threshold > $crit_threshold) {
        $warn_threshold = $crit_threshold; // Adjust if accidentally reversed
    }

    // Clamp percentage between 0 and 100 for safety
    $percent = max(0, min(100, $percent));

    if ($percent >= $crit_threshold) {
        return 'critical';
    } elseif ($percent >= $warn_threshold) {
        return 'warning';
    } else {
        return 'success';
    }
}


// --- Gather Information ---

$hostname = gethostname() ?: 'N/A';

// --- IP Address Retrieval (Improved) ---
$ip_output = run_command('hostname -I'); // Try 'hostname -I' first
$ip_addresses = [];
if ($ip_output) {
    $ip_addresses = preg_split('/\s+/', trim($ip_output)); // Split by space
} else {
    // Fallback 1: Use 'ip' command for global scope IPv4
    $ip_output_fallback = run_command("ip -4 -o addr show scope global | awk '{print $4}' | cut -d/ -f1");
     if ($ip_output_fallback) {
         $ip_addresses = explode("\n", trim($ip_output_fallback));
     } else {
         // Fallback 2: Original command (less reliable)
         $ip_output_original = run_command("ip -4 addr show | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | cut -d/ -f1");
         if ($ip_output_original) {
              $ip_addresses = explode("\n", trim($ip_output_original));
         }
     }
}
// Filter out potential empty elements, loopback, and ensure uniqueness
$ip_addresses = array_filter(array_unique($ip_addresses), function($ip) {
    // return !empty($ip) && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false;
    // Note: The above filter primarily keeps public IPs. Adjust if you NEED private IPs.
    // Use simpler filter to include private IPs:
    return !empty($ip) && filter_var($ip, FILTER_VALIDATE_IP) !== false; // Keep any valid IP
});
$ip_address_str = !empty($ip_addresses) ? implode(', ', $ip_addresses) : 'N/A';
// If the strict filter removed everything, try getting the first non-loopback again simpler
if ($ip_address_str === 'N/A') {
    $ip_output_simple = run_command("ip -4 -o addr show | grep -v '127.0.0.1' | awk '{print $4}' | cut -d/ -f1 | head -n 1");
    if ($ip_output_simple) {
        $ip_address_str = $ip_output_simple;
    }
}
// --- End IP Address Retrieval ---


// Try getting distribution-specific version, then fallback
$os_version = 'N/A';
if (is_readable('/etc/os-release')) {
    $os_release = parse_ini_file('/etc/os-release');
    $os_version = $os_release['PRETTY_NAME'] ?? $os_release['NAME'] ?? 'N/A';
} elseif (is_readable('/etc/redhat-release')) {
    $os_version = trim(file_get_contents('/etc/redhat-release'));
} elseif (is_readable('/etc/lsb-release')) {
     $lsb_release = parse_ini_file('/etc/lsb-release');
     $os_version = $lsb_release['DISTRIB_DESCRIPTION'] ?? 'N/A';
} elseif (($uname_s = run_command('uname -s')) && ($uname_r = run_command('uname -r'))) {
     $os_version = trim($uname_s . ' ' . $uname_r);
}
$os_version = $os_version ?: 'N/A';


// --- Operation Mode ---
$m = run_command('binit -model') ?: '';
$e = run_command('binit -e') ?: '';
$h = run_command('binit -h') ?: '';
$t = ''; // Demo flag if needed

$edition = '';
if ($m == "OLD_MODEL") {
    if ($e == "s") {
        $edition = "Standard";
    } elseif ($e == "a") {
        $edition = "Advanced";
    } elseif ($e == "e") {
        $edition = "Enterprise";
    } else {
        $edition = "Lite";
    }
} elseif ($m == "DEMO") {
    $edition = "Demo";
} else {
    $modules = run_command('binit -modules') ?: '';
    $module_count = 0;
    if (preg_match('/^\[(.*)\]$/', $modules, $matches)) {
        $module_count = count(array_filter(explode(',', $matches[1])));
    }
    
    if ($module_count > 5) {
        $edition = "Complete";
    } elseif ($module_count > 2) {
        $edition = "Core";
    } else {
        $edition = "Base";
    }
}

$hosts = '';
if ($h == "0050") {
    $hosts = " For 1-50 hosts";
} elseif ($h == "0100") {
    $hosts = " For 51-100 hosts";
} elseif ($h == "0000" || $h == "999") {
    $hosts = " For Unlimited hosts";
} else {
    $hosts = " For $h hosts";
}

$demo = '';
if ($t == "d") {
    $demo = " (Demo)";
}

$operation_mode = $edition . $hosts . $demo;

// Get license information
$license = run_command('binit -key') ?: "N/A";
$expiry_date_raw = run_command('binit -d');
$expiry_date = "N/A";

// Calculate days remaining and format expiry date if valid
$days_remaining = "N/A";
if ($expiry_date_raw && preg_match('/^\d{6}$/', $expiry_date_raw)) {
    $expiry_year = '20' . substr($expiry_date_raw, 0, 2);
    $expiry_month = substr($expiry_date_raw, 2, 2);
    $expiry_day = substr($expiry_date_raw, 4, 2);
    $expiry_date = "$expiry_year-$expiry_month-$expiry_day";
    $expiry_timestamp = strtotime($expiry_date);
    $current_timestamp = time();
    if ($expiry_timestamp !== false) {
        $seconds_remaining = $expiry_timestamp - $current_timestamp;
        $days_remaining = max(0, floor($seconds_remaining / (60 * 60 * 24)) + 1); // Include today
    }
}

$enabled_modules_raw = run_command('binit -modules');
if ($enabled_modules_raw) {
    $enabled_modules = strtoupper(trim($enabled_modules_raw, "[] ")); // Remove brackets and whitespace
} else {
    $enabled_modules = "N/A";
}

$nodes_monitored_raw = run_command('binit -apmcount');
$nodes_monitored = is_numeric($nodes_monitored_raw) ? (int)$nodes_monitored_raw : "N/A";

$system_time = date('Y-m-d H:i:s T'); // Uses server's default timezone unless overridden above
$uptime_str = run_command("uptime -p"); // Prefer pretty format
if (!$uptime_str) {
    $uptime_raw = run_command("uptime");
    if ($uptime_raw && preg_match('/up\s+(.*?),\s+\d+\s+user/', $uptime_raw, $match)) {
        $uptime_str = trim($match[1]);
    } elseif ($uptime_raw) {
        $uptime_str = $uptime_raw; // Fallback to raw string
    } else {
         $uptime_str = "N/A";
    }
} else {
    // Remove leading 'up ' if present from 'uptime -p'
    $uptime_str = preg_replace('/^up\s+/', '', $uptime_str);
}
$uptime_str = ucfirst($uptime_str); // Capitalize first letter

// --- Resource Usage ---
$mem_details = get_memory_details();
$mem_total = $mem_details['total_kb'] * 1024;
$mem_available = $mem_details['available_kb'] * 1024;
$mem_used = $mem_total > 0 ? $mem_total - $mem_available : 0;
$mem_percent_used = $mem_total > 0 ? round(($mem_used / $mem_total) * 100, 1) : 0;

$swap_total = $mem_details['swap_total_kb'] * 1024;
$swap_free = $mem_details['swap_free_kb'] * 1024;
$swap_used = $swap_total > 0 ? $swap_total - $swap_free : 0;
$swap_percent_used = $swap_total > 0 ? round(($swap_used / $swap_total) * 100, 1) : 0;

$disk_details = get_disk_details('/'); // Check root filesystem '/'
$disk_total = $disk_details['total_kb'] * 1024;
$disk_used = $disk_details['used_kb'] * 1024;
$disk_free = $disk_details['free_kb'] * 1024;
// Use percent directly from df command as it's often more accurate (accounts for reserved blocks)
$disk_percent_used = $disk_details['percent_used'];
// Fallback calculation if df didn't provide percentage
if ($disk_percent_used <= 0 && $disk_total > 0) {
    $disk_percent_used = round(($disk_used / $disk_total) * 100, 1);
}


// --- CPU Information ---
$cpu_model = 'N/A';
$cpu_cores = 'N/A';
$cpu_threads = 'N/A';
$cpu_speed = 'N/A';
$cpu_cache = 'N/A';

$lscpu_output = run_command("lscpu");
if ($lscpu_output) {
    // Extract model name
    if (preg_match('/^Model name:\s+(.+)/m', $lscpu_output, $matches)) {
        $cpu_model = trim($matches[1]);
    } elseif (preg_match('/^Vendor ID:\s+(.+)/m', $lscpu_output, $matches)) {
        $cpu_model = trim($matches[1]);
    }
    
    // Extract cores and threads
    if (preg_match('/^CPU\(s\):\s+(\d+)/m', $lscpu_output, $matches)) {
        $cpu_threads = $matches[1];
    }
    if (preg_match('/^Core\(s\) per socket:\s+(\d+)/m', $lscpu_output, $matches)) {
        $cores_per_socket = $matches[1];
    }
    if (preg_match('/^Socket\(s\):\s+(\d+)/m', $lscpu_output, $matches)) {
        $sockets = $matches[1];
        if (isset($cores_per_socket)) {
            $cpu_cores = $sockets * $cores_per_socket;
        }
    }
    
    // Extract CPU speed
    if (preg_match('/^CPU MHz:\s+([\d.]+)/m', $lscpu_output, $matches)) {
        $cpu_speed = round($matches[1] / 1000, 2) . ' GHz';
    } elseif (preg_match('/^CPU max MHz:\s+([\d.]+)/m', $lscpu_output, $matches)) {
        $cpu_speed = round($matches[1] / 1000, 2) . ' GHz';
    }
    
    // Extract cache info
    if (preg_match('/^L3 cache:\s+(.+)/m', $lscpu_output, $matches)) {
        $cpu_cache = trim($matches[1]);
    } elseif (preg_match('/^L2 cache:\s+(.+)/m', $lscpu_output, $matches)) {
        $cpu_cache = trim($matches[1]);
    }
}

// Fallback to /proc/cpuinfo if lscpu didn't provide all info
if ($cpu_model === 'N/A' || $cpu_cores === 'N/A') {
    $cpuinfo_raw = @file_get_contents('/proc/cpuinfo');
    if ($cpuinfo_raw) {
        if ($cpu_model === 'N/A' && preg_match('/model name\s+:\s+(.+)/', $cpuinfo_raw, $matches)) {
            $cpu_model = trim($matches[1]);
        }
        if ($cpu_cores === 'N/A') {
            $cpu_cores = substr_count($cpuinfo_raw, 'processor');
        }
        if ($cpu_speed === 'N/A' && preg_match('/cpu MHz\s+:\s+([\d.]+)/', $cpuinfo_raw, $matches)) {
            $cpu_speed = round($matches[1] / 1000, 2) . ' GHz';
        }
    }
}

// Fallback to nproc for thread count if needed
if ($cpu_threads === 'N/A') {
    $nproc_output = run_command('nproc');
    if ($nproc_output && is_numeric($nproc_output)) {
        $cpu_threads = (int)$nproc_output;
    }
}

// Format CPU string
$cpu_total_str = '';
if ($cpu_model !== 'N/A') {
    $cpu_total_str .= $cpu_model;
    if ($cpu_cores !== 'N/A' && $cpu_threads !== 'N/A') {
        $cpu_total_str .= " ($cpu_cores Cores, $cpu_threads Threads)";
    } elseif ($cpu_cores !== 'N/A') {
        $cpu_total_str .= " ($cpu_cores Cores)";
    }
    if ($cpu_speed !== 'N/A') {
        $cpu_total_str .= " @ $cpu_speed";
    }
    if ($cpu_cache !== 'N/A') {
        $cpu_total_str .= " | $cpu_cache Cache";
    }
} else {
    $cpu_total_str = $cpu_cores !== 'N/A' ? $cpu_cores . ' Core(s)' : 'N/A';
}


// --- Determine Threshold Classes for Progress Bars ---
// Adjust these thresholds as desired
$mem_warn_threshold = 75;
$mem_crit_threshold = 90;
$swap_warn_threshold = 50; // Warning if swap usage is moderate
$swap_crit_threshold = 80; // Critical if swap usage is high
$disk_warn_threshold = 80;
$disk_crit_threshold = 95; // Often higher threshold for disk
$cpu_warn_threshold = 70; // Warning if CPU usage is high
$cpu_crit_threshold = 90; // Critical if CPU usage is very high

// License threshold values (in days)
$license_warn_threshold = 90; // 3 months
$license_crit_threshold = 30; // 1 month

// Determine license status icon class based on days remaining
$license_status_class = 'success'; // Default green
$license_tooltip = 'License valid';

if (is_numeric($days_remaining)) {
    if ($days_remaining <= $license_crit_threshold) {
        $license_status_class = 'critical'; // Red for less than 1 month
        $license_tooltip = 'License expired or expires in less than 1 month';
    } elseif ($days_remaining <= $license_warn_threshold) {
        $license_status_class = 'warning'; // Yellow for less than 3 months
        $license_tooltip = 'License expires in less than 3 months';
    } else {
        $license_tooltip = 'License valid';
    }
}

$mem_threshold_class = get_threshold_class($mem_percent_used, $mem_warn_threshold, $mem_crit_threshold);
$swap_threshold_class = get_threshold_class($swap_percent_used, $swap_warn_threshold, $swap_crit_threshold);
$disk_threshold_class = get_threshold_class($disk_percent_used, $disk_warn_threshold, $disk_crit_threshold);


// --- HTML Fragment Output ---
?>

<h2><i class="fa fa-server"></i> General Information</h2>
<p>Overview of the machine's identity and basic configuration.</p>
<dl class="info-grid">
    <dt><i class="fa fa-desktop"></i> Hostname:</dt>
    <dd><?php echo htmlspecialchars($hostname); ?></dd>

    <dt><i class="fa fa-globe"></i> IP Address:</dt>
    <dd><?php echo htmlspecialchars($ip_address_str); ?></dd>

    <dt><i class="fa fa-info-circle"></i> Software Version:</dt>
    <dd><?php echo htmlspecialchars($os_version); ?></dd>

    <dt><i class="fa fa-shield"></i> Operation Mode:</dt>
    <dd><?php echo htmlspecialchars($operation_mode); ?></dd>

    <dt><i class="fa fa-key"></i> License:</dt>
    <dd>
        <span class="license-status" title="<?php echo htmlspecialchars($license_tooltip); ?>">
            <i class="fa fa-certificate <?php echo $license_status_class; ?>"></i>
        </span>
        <button id="renew-license-btn" class="btn-restart" style="margin-left: 10px;">
            Renew
        </button>
    </dd>

    <dt><i class="fa fa-calendar-times-o"></i> Expiry Date:</dt>
    <dd><?php echo htmlspecialchars($expiry_date); ?></dd>

    <dt><i class="fa fa-hourglass-half"></i> Days Remaining:</dt>
    <dd><?php echo htmlspecialchars($days_remaining); ?></dd>

    <dt><i class="fa fa-puzzle-piece"></i> Enabled Modules:</dt>
    <dd><?php echo htmlspecialchars($enabled_modules); ?></dd>

    <dt><i class="fa fa-sitemap"></i> Nodes Monitored:</dt>
    <dd><?php echo htmlspecialchars($nodes_monitored); ?></dd>
</dl>

<style>
/* License status icon styles */
.license-status .fa-certificate {
    font-size: 18px;
}
.license-status .success {
    color: #4caf50; /* Green */
}
.license-status .warning {
    color: #ff9800; /* Yellow/Orange */
}
.license-status .critical {
    color: #f44336; /* Red */
}
</style>

<!-- Hidden License Renewal Form -->
<div id="renew-license-form" style="display: none; margin-top: 15px; padding: 20px; border: 1px solid var(--border); border-radius: var(--radius); background-color: var(--surface);">
    <h4 style="margin-bottom: 15px; font-size: 16px; color: var(--text); display: flex; align-items: center;"><i class="fa fa-key" style="margin-right: 8px;"></i> Renew License Key</h4>
    <!-- Modified form to post directly like the original lic.php -->
    <form id="license-form-actual" action="../../../proc.php" method="post" autocomplete="off">
        <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 15px;">
            <label for="license-key-input" style="margin-bottom: 5px; font-weight: 500; color: var(--text); font-size: 14px;">Enter New License Key:</label>
            <!-- Using textarea like original lic.php, though input type=text should also work -->
            <textarea id="license-key-input" name="lk" class="form-control" placeholder="XXXXXX-XXXXXX-XXXXXX-XXXXXX" required rows="3" style="background: var(--background); color: var(--text); border: 1px solid var(--border); padding: 8px 10px; border-radius: 6px; font-size: 14px; width: 95%; transition: border-color 0.2s ease;"></textarea>
            <small style="display: block; color: var(--text-secondary); font-size: 12px; margin-top: 5px;">Enter your license key in the format shown above.</small>
        </div>
        <div class="input-group" style="display: flex; align-items: center; margin-bottom: 15px;">
            <input type="checkbox" id="agree-terms" name="agree_terms" required style="margin-right: 8px;">
            <label for="agree-terms" style="color: var(--text); font-size: 14px;">
                I agree to the <a href="#" id="open-terms-modal" style="color: var(--primary); text-decoration: underline;">Terms and Conditions</a>
            </label>
        </div>
        <div class="action-buttons" style="display: flex; gap: 6px; align-items: center; justify-content: flex-start; flex-wrap: nowrap;">
            <button type="submit" class="btn-restart" style="padding: 6px 12px; height: 32px; border: 1px solid var(--border); border-radius: 4px; cursor: pointer; font-size: 13px; display: inline-flex; align-items: center; justify-content: center; background-color: var(--background); color: var(--text); transition: all 0.2s;"><i class="fa fa-check" style="margin-right: 6px;"></i>Submit Key</button>
            <button type="button" id="cancel-renew-btn" class="btn-restart" style="padding: 6px 12px; height: 32px; border: 1px solid var(--border); border-radius: 4px; cursor: pointer; font-size: 13px; display: inline-flex; align-items: center; justify-content: center; background-color: var(--background); color: var(--text); transition: all 0.2s;"><i class="fa fa-times" style="margin-right: 6px;"></i>Cancel</button>
        </div>
    </form>
    <div id="license-status-message" class="message" style="display: none; margin-top: 15px; padding: 12px 15px; border-radius: 4px; font-size: 14px; width: 100%; box-sizing: border-box;"></div>
</div>

<!-- Terms and Conditions Modal -->
<div id="terms-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
    <div style="background-color: var(--surface); border-radius: var(--radius); width: 80%; max-width: 800px; max-height: 80vh; overflow: hidden; padding: 0; position: relative; display: flex; flex-direction: column;">
        <div style="position: sticky; top: 0; z-index: 5; background-color: var(--surface); padding: 20px 20px 10px; border-bottom: 1px solid var(--border); display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 18px; color: var(--text);">Terms and Conditions</h3>
            <button id="close-terms-modal" style="background: none; border: none; font-size: 20px; cursor: pointer; color: var(--text); width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;">×</button>
        </div>
        <div id="terms-content" style="padding: 20px; overflow-y: auto; flex: 1; scrollbar-width: thin; scrollbar-color: var(--border) transparent;">
            <?php include('src/settingsphp/terms.php'); ?>
        </div>
    </div>
</div>

<style>
/* Custom scrollbar styles for the terms modal */
#terms-content::-webkit-scrollbar {
    width: 8px;
}
#terms-content::-webkit-scrollbar-track {
    background: transparent;
}
#terms-content::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 4px;
}
#terms-content::-webkit-scrollbar-thumb:hover {
    background-color: #999;
}
</style>

<hr style="margin: 25px 0; border: 0; border-top: 1px solid #eee;">

<h2><i class="fa fa-cogs"></i> System Status</h2>
<p>Current resource usage and system state.</p>
<dl class="info-grid">
    <dt><i class="fa fa-clock-o"></i> System Time:</dt>
    <dd><?php echo htmlspecialchars($system_time); ?></dd>

    <dt><i class="fa fa-arrow-circle-up"></i> Uptime:</dt>
    <dd><?php echo htmlspecialchars($uptime_str); ?></dd>

    <dt><i class="fa fa-desktop"></i> CPU information:</dt>
    <dd><?php echo htmlspecialchars($cpu_total_str); ?></dd>
    
    <dt><i class="fa fa-tachometer"></i> CPU Usage:</dt>
    <dd>
        <span class="usage-summary">
            Current: <span id="cpu-usage-value">Loading...</span>
        </span>
        <div class="progress-bar-container" id="cpu-usage-container" style="display:none;">
            <div id="cpu-usage-bar" class="progress-bar" style="width: 0%;" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                <span id="cpu-usage-percent">0%</span>
            </div>
        </div>
        <script>
        function updateCpuUsage() {
            fetch('src/settingsphp/getCpuUsage.php')
                .then(response => response.json())
                .then(data => {
                    const cpuValue = document.getElementById('cpu-usage-value');
                    const cpuPercent = document.getElementById('cpu-usage-percent');
                    const cpuBar = document.getElementById('cpu-usage-bar');
                    const cpuContainer = document.getElementById('cpu-usage-container');

                    if (data.cpu_usage_percent !== 'N/A') {
                        cpuValue.textContent = data.cpu_usage_percent;
                        cpuPercent.textContent = data.cpu_usage_percent;
                        cpuBar.style.width = data.cpu_usage + '%';
                        cpuBar.setAttribute('aria-valuenow', data.cpu_usage);
                        
                        // Set threshold class
                        const cpuClass = data.cpu_usage >= <?php echo $cpu_crit_threshold; ?> ? 'critical' :
                                       data.cpu_usage >= <?php echo $cpu_warn_threshold; ?> ? 'warning' : 'success';
                        
                        cpuBar.classList.remove('success', 'warning', 'critical');
                        cpuBar.classList.add(cpuClass);
                        
                        cpuContainer.style.display = 'block';
                    } else {
                        cpuValue.textContent = 'N/A';
                        cpuContainer.style.display = 'none';
                    }
                })
                .catch(error => console.error('Error fetching CPU usage:', error));
        }
        
        // Start updates when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Update immediately and every 5 seconds
            updateCpuUsage();
            setInterval(updateCpuUsage, 5000);
            
            // License renewal form functionality
            const renewBtn = document.getElementById('renew-license-btn');
            const licenseForm = document.getElementById('renew-license-form');
            const cancelBtn = document.getElementById('cancel-renew-btn');
            const licenseFormActual = document.getElementById('license-form-actual');
            const agreeCheckbox = document.getElementById('agree-terms');
            
            // Terms modal functionality
            const openTermsBtn = document.getElementById('open-terms-modal');
            const termsModal = document.getElementById('terms-modal');
            const closeTermsBtn = document.getElementById('close-terms-modal');
            
            if (renewBtn) {
                renewBtn.addEventListener('click', () => {
                    licenseForm.style.display = 'block';
                });
            }
            
            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    licenseForm.style.display = 'none';
                });
            }
            
            if (licenseFormActual) {
                licenseFormActual.addEventListener('submit', (e) => {
                    if (!agreeCheckbox.checked) {
                        e.preventDefault();
                        alert('You must agree to the terms and conditions to proceed.');
                    }
                });
            }
            
            if (openTermsBtn) {
                openTermsBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    termsModal.style.display = 'flex';
                });
            }
            
            if (closeTermsBtn) {
                closeTermsBtn.addEventListener('click', () => {
                    termsModal.style.display = 'none';
                });
            }
            
            // Close modal when clicking outside
            window.addEventListener('click', (e) => {
                if (e.target === termsModal) {
                    termsModal.style.display = 'none';
                }
            });
        });
        </script>
    </dd>

    <dt><i class="fa fa-dashboard"></i> Memory:</dt>
    <dd>
        <span class="usage-summary">
            Total: <?php echo format_bytes($mem_total); ?> |
            Used: <?php echo format_bytes($mem_used); ?> |
            Available: <?php echo format_bytes($mem_available); ?>
        </span>
        <div class="progress-bar-container" title="<?php printf('%.1f%% Used (Warn: %d%%, Crit: %d%%)', $mem_percent_used, $mem_warn_threshold, $mem_crit_threshold); ?>">
            <?php /* Apply dynamic class: success, warning, or critical */ ?>
            <div class="progress-bar <?php echo $mem_threshold_class; ?>" style="width: <?php echo max(0, min(100, $mem_percent_used)); ?>%;" role="progressbar" aria-valuenow="<?php echo $mem_percent_used; ?>" aria-valuemin="0" aria-valuemax="100">
               <?php if($mem_percent_used > 10) printf('%.1f%%', $mem_percent_used); ?>
            </div>
        </div>
    </dd>

    <dt><i class="fa fa-exchange"></i> Swap:</dt>
    <dd>
        <span class="usage-summary">
            Total: <?php echo format_bytes($swap_total); ?> |
            Used: <?php echo format_bytes($swap_used); ?> |
            Free: <?php echo format_bytes($swap_free); ?>
        </span>
        <?php if ($swap_total > 0): ?>
        <div class="progress-bar-container" title="<?php printf('%.1f%% Used (Warn: %d%%, Crit: %d%%)', $swap_percent_used, $swap_warn_threshold, $swap_crit_threshold); ?>">
             <?php /* Apply dynamic class: success, warning, or critical */ ?>
             <div class="progress-bar <?php echo $swap_threshold_class; ?>" style="width: <?php echo max(0, min(100, $swap_percent_used)); ?>%;" role="progressbar" aria-valuenow="<?php echo $swap_percent_used; ?>" aria-valuemin="0" aria-valuemax="100">
                <?php if($swap_percent_used > 10) printf('%.1f%%', $swap_percent_used); ?>
             </div>
         </div>
        <?php else: ?>
         <span class="text-muted" style="font-size: 0.9em;">(Swap not configured)</span>
        <?php endif; ?>
    </dd>

    <dt><i class="fa fa-hdd-o"></i> Disk Usage (/):</dt>
    <dd>
        <span class="usage-summary">
            Size: <?php echo format_bytes($disk_total); ?> |
            Used: <?php echo format_bytes($disk_used); ?> |
            Free: <?php echo format_bytes($disk_free); ?>
        </span>
        <div class="progress-bar-container" title="<?php printf('%.1f%% Used (Warn: %d%%, Crit: %d%%)', $disk_percent_used, $disk_warn_threshold, $disk_crit_threshold); ?>">
            <?php /* Apply dynamic class: success, warning, or critical */ ?>
            <div class="progress-bar <?php echo $disk_threshold_class; ?>" style="width: <?php echo max(0, min(100, $disk_percent_used)); ?>%;" role="progressbar" aria-valuenow="<?php echo $disk_percent_used; ?>" aria-valuemin="0" aria-valuemax="100">
               <?php if($disk_percent_used > 10) printf('%.1f%%', $disk_percent_used); ?>
            </div>
        </div>
    </dd>
</dl>

<?php
// --- End of Fragment Output ---
?>