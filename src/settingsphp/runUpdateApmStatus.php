<?php
/**
 * Run the update_apm_status.php script
 * This file acts as a wrapper to execute the update script via AJAX
 */

// Handle both trigger requests and progress requests
if (isset($_GET['action']) && $_GET['action'] === 'progress') {
    // Return progress information
    header('Content-Type: application/json');
    
    $progressFile = dirname(__FILE__) . '/../../locks/apm_progress.json';
    $lockFile = dirname(__FILE__) . '/../../locks/update_apm_status.lock';
    
    if (file_exists($progressFile)) {
        $progress = json_decode(file_get_contents($progressFile), true);
        
        // Check if the script is actually running
        $isRunning = file_exists($lockFile);
        
        // If progress file is older than 2 minutes and no lock, mark as idle
        if (!$isRunning && (time() - $progress['timestamp']) > 120) {
            $progress['status'] = 'idle';
            $progress['message'] = 'Waiting for next update cycle';
        }
        
        echo json_encode($progress);
    } else {
        // No progress file exists
        echo json_encode([
            'current' => 0,
            'total' => 0,
            'percentage' => 0,
            'status' => 'idle',
            'timestamp' => time(),
            'message' => 'Waiting for next update cycle'
        ]);
    }
    exit;
}

// Original functionality - trigger the update script
// Check if the script is not already running
$lockFile = dirname(__FILE__) . '/../../locks/update_apm_status.lock';

if (!file_exists($lockFile)) {
    // Start the update script in background
    $scriptPath = dirname(__FILE__) . '/../update_apm_status.php';
    $command = "php " . escapeshellarg($scriptPath) . " > /dev/null 2>&1 &";
    exec($command);
    
    echo json_encode(['status' => 'started', 'message' => 'APM status update started']);
} else {
    echo json_encode(['status' => 'running', 'message' => 'APM status update already running']);
}
?> 