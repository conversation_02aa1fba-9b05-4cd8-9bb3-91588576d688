<?php
// Basic security check: Ensure this is an AJAX request (optional but recommended)
if (empty($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    // If not an AJAX request, maybe redirect or show an error
    // For simplicity here, we'll just exit, but a proper error/redirect is better.
    // http_response_code(403); // Forbidden
    // die('Direct access not allowed.');
    // Allow non-AJAX for potential direct testing, but be aware of implications.
}

// Command to execute - Use the full path to yum and include -y for non-interactive update
// IMPORTANT: Requires passwordless sudo configuration for the web server user for this specific command.
$command = 'sudo /usr/bin/yum update -y 2>&1'; // Redirect stderr to stdout

// Increase execution time limit if needed, as updates can take a while
set_time_limit(600); // 10 minutes, adjust as necessary

// Run yum clean all before update
$clean_command = 'sudo /usr/bin/yum clean all 2>&1';
$clean_output_lines = [];
$clean_return_var = -1;
exec($clean_command, $clean_output_lines, $clean_return_var);
$clean_output = implode("\n", $clean_output_lines);

// Execute the update command using exec()
$output_lines = []; // Array to hold output lines
$return_var = -1;   // Variable to hold the return status

// exec() executes the command and populates $output_lines and $return_var
exec($command, $output_lines, $return_var);

// Combine the output lines into a single string
$full_output = implode("\n", $output_lines);

// Run yum clean all AGAIN after update
$clean_command_after = 'sudo /usr/bin/yum clean all 2>&1';
$clean_output_lines_after = [];
$clean_return_var_after = -1;
exec($clean_command_after, $clean_output_lines_after, $clean_return_var_after);
$clean_output_after = implode("\n", $clean_output_lines_after);

if ($return_var === 0) {
    // Command executed successfully
    http_response_code(200); // OK
    echo "Update command finished successfully.\n";
    echo "\nClean (Before) Output:\n-------\n" . htmlspecialchars($clean_output ?: 'No output received.');
    echo "\nUpdate Output:\n-------\n" . htmlspecialchars($full_output ?: 'No output received.');
    echo "\nClean (After) Output:\n-------\n" . htmlspecialchars($clean_output_after ?: 'No output received.');
} else {
    // Command failed
    // Note: exec() might not always provide detailed error output like proc_open stderr redirection
    http_response_code(500); // Internal Server Error
    echo "Update command failed with exit code: " . $return_var . "\n";
    echo "\nClean (Before) Output:\n-------\n" . htmlspecialchars($clean_output ?: 'No output received.');
    echo "\nUpdate Output:\n-------\n" . htmlspecialchars($full_output ?: 'No output received. Check server logs for details if command failed.');
    echo "\nClean (After) Output:\n-------\n" . htmlspecialchars($clean_output_after ?: 'No output received.');
}

?>