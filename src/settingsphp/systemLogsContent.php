<?php
// src/settingsphp/systemLogsContent.php

// Ensure session variables are available if needed (already done in systemLogs.php)
// $logfile is no longer needed here as we fetch all logs
$lines_to_show = $_SESSION['lines_to_show'] ?? 25;
$log_contents = $_SESSION['log_contents'] ?? '';
$log_options = $_SESSION['log_options'] ?? [];

// Split log contents into lines for better formatting
$log_lines = $log_contents ? explode("\n", trim($log_contents)) : [];
?>

<!-- NO SCRIPT TAG HERE ANYMORE -->

<div id="logs-content">
    <h2><i class="fa fa-file-text-o"></i> System Logs</h2>
    <p>View logs from various system components.</p>

    <form id="log-form" action="/bubblemaps/src/settingsphp/systemLogs.php" method="post">
        <fieldset>
            <legend>Log Options</legend>
            <!-- Log file selection removed -->

            <div class="input-group">
                <label for="lines_to_show">Lines to Display:</label>
                <select id="lines_to_show" name="lines_to_show">
                    <option value="25" <?= $lines_to_show == 25 ? 'selected' : '' ?>>25</option>
                    <option value="50" <?= $lines_to_show == 50 ? 'selected' : '' ?>>50</option>
                    <option value="100" <?= $lines_to_show == 100 ? 'selected' : '' ?>>100</option>
                    <option value="200" <?= $lines_to_show == 200 ? 'selected' : '' ?>>200</option>
                </select>
            </div>

            <button type="submit" class="btn-restart"><i class="fa fa-download"></i><span>&nbsp;Fetch</span></button>
        </fieldset>
    </form>

    <div id="log-display" style="margin-top: 20px; max-height:300px;">
        <?php if ($log_lines): ?>
            <pre style="background: var(--background); border: 1px solid var(--border); border-radius: 8px; padding: 15px; max-height: 600px; overflow-y: auto; font-family: monospace; font-size: 13px; color: var(--text-secondary); white-space: pre-wrap; word-break: break-word;"><?php
                $current_section = '';
                $section_lines = 0;
                foreach ($log_lines as $line) {
                    // Check if the line is one of our custom headers
                    if (strpos($line, "<span class='log-header'>") === 0) {
                        // If we have a previous section, show its line count
                        if ($current_section !== '') {
                            echo "<span class='log-line-count'>Lines: " . ($section_lines - 1) . "</span>\n";
                        }
                        $current_section = $line;
                        $section_lines = 0;
                        echo $line . "\n"; // Output the header HTML directly
                    } else {
                        $section_lines++;
                        $escaped_line = htmlspecialchars($line);
                        // Apply highlighting to important keywords
                        $escaped_line = preg_replace_callback(
                            '/(warning|alert|critical|failure|fail|error|exception|severe|fatal|emergency|panic|down|offline|unreachable|timeout|denied|rejected|blocked|invalid|unauthorized|forbidden|broken|corrupt|malformed|malicious|attack|intrusion|breach|compromise|exploit|vulnerability)/i',
                            function($matches) {
                                $class = 'log-warning';
                                if (preg_match('/error|fail|exception|fatal|emergency|panic|down|offline|unreachable|timeout|denied|rejected|blocked|invalid|unauthorized|forbidden|broken|corrupt|malformed|malicious|attack|intrusion|breach|compromise|exploit|vulnerability/i', $matches[0])) {
                                    $class = 'log-error';
                                } elseif (preg_match('/critical|severe|alert/i', $matches[0])) {
                                    $class = 'log-critical';
                                }
                                return '<span class="'.$class.'">'.$matches[0].'</span>';
                            },
                            $escaped_line
                        );
                        echo $escaped_line . "\n";
                    }
                }
                // Show line count for the last section
                if ($current_section !== '') {
                    echo "<span class='log-line-count'>Lines: " . ($section_lines - 1) . "</span>\n";
                }
            ?></pre>
        <?php elseif (strpos($log_contents, 'Error:') === 0): ?>
             <p class="text-muted message message-error"><?= htmlspecialchars($log_contents) ?></p>
        <?php else: ?>
            <p class="text-muted">No log data available or file is empty.</p>
        <?php endif; ?>
    </div>
</div>