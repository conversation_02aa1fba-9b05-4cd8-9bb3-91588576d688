<?php
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    die(json_encode(['error' => 'Method not allowed']));
}

$data = json_decode(file_get_contents('php://input'), true);

// Check service status
if (isset($data['action']) && $data['action'] === 'check') {
    exec('systemctl is-active dnf-automatic-install.timer 2>&1', $output, $return_var);
    $isActive = trim(implode("\n", $output)) === 'active';
    die(json_encode(['isActive' => $isActive]));
}

if (!isset($data['action']) || !in_array($data['action'], ['enable', 'disable'])) {
    http_response_code(400);
    die(json_encode(['error' => 'Invalid request']));
}

$output = [];
$return_var = 0;

if ($data['action'] === 'enable') {
    exec('sudo systemctl start dnf-automatic-install.timer 2>&1', $output, $return_var);
} else {
    exec('sudo systemctl stop dnf-automatic-install.timer 2>&1', $output, $return_var);
}

if ($return_var !== 0) {
    http_response_code(500);
    die(json_encode(['error' => implode("\n", $output)]));
}

// Verify the change was successful
exec('systemctl is-active dnf-automatic-install.timer 2>&1', $output, $return_var);
$isActive = trim(implode("\n", $output)) === 'active';

echo json_encode([
    'success' => true,
    'message' => 'Automatic updates ' . ($data['action'] === 'enable' ? 'enabled' : 'disabled'),
    'isActive' => $isActive
]);