<?php
include __DIR__ . "/../loadenv.php";

// Database configuration
define('DB_HOST', $_ENV["DB_SERVER"]);
define('DB_USER', $_ENV["DB_USER"]);
define('DB_PASS', $_ENV["DB_PASSWORD"]);
define('DB_NAME', 'bubblemaps');

// Status mappings (matching helperFunctions.js)
$statusMappings = [
    'host' => [1 => 'pending', 2 => 'ok', 4 => 'down', 8 => 'unknown'],
    'service' => [1 => 'pending', 4 => 'warning', 8 => 'unknown', 16 => 'critical']
];

// Progress tracking
function updateProgress($current, $total, $status = 'running') {
    $progressFile = dirname(__FILE__) . '/../locks/apm_progress.json';
    $progress = [
        'current' => $current,
        'total' => $total,
        'percentage' => $total > 0 ? round(($current / $total) * 100, 1) : 0,
        'status' => $status,
        'timestamp' => time(),
        'message' => $status === 'running' ? "Processing host $current of $total" : $status
    ];
    file_put_contents($progressFile, json_encode($progress));
}

// Function to read the lock status
function isLocked($lockFile) {
    if (!file_exists($lockFile)) {
        return false;
    }
    
    $fp = @fopen($lockFile, 'r');
    if (!$fp) {
        return false;
    }
    
    // Try to get a shared lock (non-blocking)
    $locked = !flock($fp, LOCK_SH | LOCK_NB);
    fclose($fp);
    
    return $locked;
}

// Function to set the lock status
function setLock($lockFile, $status) {
    global $lockHandle;
    
    if ($status) {
        // Create or open the lock file
        $lockHandle = fopen($lockFile, 'w');
        if (!$lockHandle) {
            throw new Exception("Unable to create lock file: $lockFile");
        }
        
        // Try to acquire an exclusive lock (non-blocking)
        if (!flock($lockHandle, LOCK_EX | LOCK_NB)) {
            fclose($lockHandle);
            throw new Exception("Unable to acquire lock: $lockFile");
        }
        
        // Write PID to the lock file
        fwrite($lockHandle, getmypid());
        fflush($lockHandle);
    } else if (isset($lockHandle) && is_resource($lockHandle)) {
        // Release the lock
        flock($lockHandle, LOCK_UN);
        fclose($lockHandle);
        
        // Remove the lock file
        if (file_exists($lockFile)) {
            @unlink($lockFile);
        }
    }
}

/**
 * Get database connection
 */
function getDatabaseConnection() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    return $conn;
}

/**
 * Get admin user database connection
 */
function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "blesk";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

/**
 * Get user credentials for Nagios API requests
 */
function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();

    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "tfUsername" => $row["username"],
            "tfPassword" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }

    $conn->close();

    return $userCredentials;
}

/**
 * Get the server's IP address
 */
function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

/**
 * Get hostname by IP from Nagios
 */
function getHostnameByIP($ip) {
    try {
        // Get credentials from the database
        $credentials = getUserCredentials();
        $hostname = getSelfIp();
        
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials");
        }

        $nagiosUrl = "https://{$hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true";
        
        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $nagiosUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // Disable SSL verification
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        // Set HTTP Basic Authentication credentials
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        
        // Execute request
        $response = curl_exec($ch);
        
        // Handle cURL errors
        if ($response === false) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            throw new Exception("cURL error: " . $errorMsg);
        }
        
        // Check HTTP status code
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode != 200) {
            throw new Exception("HTTP error! Status: {$httpCode}");
        }
        
        // Parse JSON response
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON parse error: " . json_last_error_msg());
        }
        
        // Search for matching IP in hostlist
        if (isset($data['data']['hostlist'])) {
            foreach ($data['data']['hostlist'] as $host) {
                if (isset($host['address']) && $host['address'] === $ip) {
                    return $host['name'];
                }
            }
        }
        
        return null;
        
    } catch (Exception $e) {
        error_log('Error fetching host data: ' . $e->getMessage());
        return null;
    }
}

/**
 * Check host status from Nagios
 */
function checkHostStatus($hostname) {
    try {
        $credentials = getUserCredentials();
        $selfHostname = getSelfIp();
        
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials");
        }

        $apiUrl = "https://{$selfHostname}/nagios/cgi-bin/statusjson.cgi?query=host&hostname=" . urlencode($hostname);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        
        $response = curl_exec($ch);
        
        if ($response === false) {
            curl_close($ch);
            throw new Exception('cURL error: ' . curl_error($ch));
        }
        
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode != 200) {
            throw new Exception("HTTP error! Status: {$httpCode}");
        }
        
        $jsonData = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON parse error: " . json_last_error_msg());
        }
        
        if (isset($jsonData['result']) && $jsonData['result']['type_code'] !== 0) {
            throw new Exception("Query failed: " . $jsonData['result']['type_text']);
        }
        
        return isset($jsonData['data']['host']['status']) ? $jsonData['data']['host']['status'] : 0;
    } catch (Exception $e) {
        error_log('Error checking host status: ' . $e->getMessage());
        return 0;
    }
}

/**
 * Get all service statuses for a host
 */
function getAllServiceStatuses($hostname) {
    try {
        $credentials = getUserCredentials();
        $selfHostname = getSelfIp();
        
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials");
        }

        $apiUrl = "https://{$selfHostname}/nagios/cgi-bin/statusjson.cgi?query=servicelist&hostname=" . urlencode($hostname);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        
        $response = curl_exec($ch);
        
        if ($response === false) {
            curl_close($ch);
            throw new Exception('cURL error: ' . curl_error($ch));
        }
        
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode != 200) {
            throw new Exception("HTTP error! Status: {$httpCode}");
        }
        
        $jsonData = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON parse error: " . json_last_error_msg());
        }
        
        if (isset($jsonData['result']) && $jsonData['result']['type_code'] !== 0) {
            throw new Exception("Query failed: " . $jsonData['result']['type_text']);
        }
        
        // Default response if no services found
        $result = [
            'serviceStatuses' => [],
            'worstStatus' => 0,
            'criticalCount' => 0,
            'statusCounts' => [1 => 0, 2 => 0, 4 => 0, 8 => 0, 16 => 0]
        ];
        
        // Get the full list of services and their statuses
        $serviceData = $jsonData['data']['servicelist'][$hostname] ?? [];
        if (empty($serviceData)) {
            return $result;
        }

        // Count by status (for summary)
        $statusCounts = [1 => 0, 2 => 0, 4 => 0, 8 => 0, 16 => 0];
        
        // Create a mapping of service name to status code
        $serviceStatuses = [];
        foreach ($serviceData as $serviceName => $statusCode) {
            $serviceStatuses[$serviceName] = $statusCode;
            
            // Increment counters for this status
            if (isset($statusCounts[$statusCode])) {
                $statusCounts[$statusCode]++;
            }
        }

        $statuses = array_values($serviceData);
        if (empty($statuses)) {
            return $result;
        }
        
        $maxStatus = max($statuses);
        
        // If max is 2 and there's a 1 present, return 1 (pending); otherwise return maxStatus
        $worstStatus = ($maxStatus === 2 && in_array(1, $statuses)) ? 1 : $maxStatus;

        // Count all non-OK statuses (not just critical ones)
        $criticalCount = count(array_filter($statuses, function($status) {
            return $status !== 2 && $status !== 0;
        }));

        return [
            'serviceStatuses' => $serviceStatuses,
            'worstStatus' => $worstStatus,
            'criticalCount' => $criticalCount,
            'statusCounts' => $statusCounts
        ];
        
    } catch (Exception $e) {
        error_log('Error getting service statuses: ' . $e->getMessage());
        return [
            'serviceStatuses' => [],
            'worstStatus' => 0,
            'criticalCount' => 0,
            'statusCounts' => [1 => 0, 2 => 0, 4 => 0, 8 => 0, 16 => 0]
        ];
    }
}

/**
 * Determine host status class based on host and service statuses
 */
function getHostStatusClass($host) {
    global $statusMappings;
    
    $hostStatus = checkHostStatus($host);
    
    // Default values for a host not found in Nagios
    if ($hostStatus === 0) {
        return [
            'bubbleClass' => 'pending',
            'criticalCount' => 0,
            'hostStatus' => 0,
            'serviceStatuses' => []
        ];
    }
    
    $bubbleClass = $statusMappings['host'][$hostStatus] ?? 'not-added';
    $criticalCount = 0;
    $serviceStatuses = [];

    // Get service statuses
    $serviceData = getAllServiceStatuses($host);
    $worstStatus = $serviceData['worstStatus'];
    $countCritical = $serviceData['criticalCount'];
    $services = $serviceData['serviceStatuses'];
    
    if ($hostStatus === 2) {
        // Host is UP, use service status for bubble class
        $bubbleClass = $statusMappings['service'][$worstStatus] ?? 'ok';
        $criticalCount = $countCritical;
    } else {
        // Host is DOWN/PENDING/UNKNOWN, keep host status for bubble class but include service info
        $criticalCount = max(1, $countCritical); // At least 1 critical (the host itself)
    }
    
    $serviceStatuses = $services;

    return [
        'bubbleClass' => $bubbleClass,
        'criticalCount' => $criticalCount,
        'hostStatus' => $hostStatus,
        'serviceStatuses' => $serviceStatuses,
        'statusCounts' => $serviceData['statusCounts']
    ];
}

/**
 * Update the status counts in the database
 */
function updateHostStatusCounts($hostId, $statusCounts) {
    $conn = getDatabaseConnection();
    
    $pendingCount = intval($statusCounts[1]);
    $okCount = intval($statusCounts[2]);
    $warningCount = intval($statusCounts[4]);
    $unknownCount = intval($statusCounts[8]);
    $criticalCount = intval($statusCounts[16]);
    
    $query = "UPDATE hosts SET 
                pending_count = ?,
                ok_count = ?, 
                warning_count = ?, 
                unknown_count = ?, 
                critical_count = ? 
              WHERE id = ?";
    
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        $conn->close();
        return false;
    }
    
    $stmt->bind_param("iiiiii", 
        $pendingCount, 
        $okCount, 
        $warningCount, 
        $unknownCount, 
        $criticalCount, 
        $hostId
    );
    
    $result = $stmt->execute();
    
    if (!$result) {
        error_log("Execute failed: " . $stmt->error);
    }
    
    $stmt->close();
    $conn->close();
    
    return $result;
}

/**
 * Update a host's APM status in the database
 */
function updateApmStatus($id, $status) {
    $conn = getDatabaseConnection();
    
    $query = "UPDATE hosts SET apmStatus = ? WHERE id = ?";
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        $conn->close();
        return false;
    }
    
    $stmt->bind_param("si", $status, $id);
    $result = $stmt->execute();
    
    if (!$result) {
        error_log("Execute failed: " . $stmt->error);
    }
    
    $stmt->close();
    $conn->close();
    
    return $result;
}

/**
 * Process all hosts in the database
 */
function processAllHosts() {
    $conn = getDatabaseConnection();
    
    // Get all hosts from the database, excluding blacklisted, 'ask' and 'not-added' status hosts
    $query = "SELECT id, ip FROM hosts WHERE blacklist = 0 AND apmStatus != 'ask' AND apmStatus != 'not-added'";
    $result = $conn->query($query);
    
    if (!$result) {
        error_log("Query failed: " . $conn->error);
        $conn->close();
        return;
    }
    
    $totalHosts = $result->num_rows;
    $processed = 0;
    $errors = 0;
    
    // Initialize progress
    updateProgress(0, $totalHosts, 'running');
    
    echo "Starting to process $totalHosts hosts\n";
    
    while ($row = $result->fetch_assoc()) {
        $hostId = $row['id'];
        $ip = $row['ip'];
        
        echo "Processing host ID: $hostId, IP: $ip\n";
        
        try {
            // Get the real hostname by IP
            $hostname = getHostnameByIP($ip);
            
            // Add 50ms delay between API calls
            usleep(50000);
            
            if ($hostname) {
                echo "  Host found in Nagios as: $hostname\n";
                
                // Get host status class
                $statusData = getHostStatusClass($hostname);
                
                // Add 50ms delay between API calls
                usleep(50000);
                
                // Update APM status
                if (updateApmStatus($hostId, $statusData['bubbleClass'])) {
                    // Update status counts
                    updateHostStatusCounts($hostId, $statusData['statusCounts']);
                    echo "  Updated status to: " . $statusData['bubbleClass'] . "\n";
                    $processed++;
                } else {
                    echo "  Error updating status in database\n";
                    $errors++;
                }
            } else {
                echo "  Warning: Host not found in Nagios but should exist\n";
                $errors++;
            }
        } catch (Exception $e) {
            echo "  Error processing host ID $hostId: " . $e->getMessage() . "\n";
            error_log("Error processing host ID $hostId: " . $e->getMessage());
            $errors++;
        }
        
        // Update progress after each host
        updateProgress($processed + $errors, $totalHosts, 'running');
    }
    
    $conn->close();
    
    // Final progress update
    updateProgress($totalHosts, $totalHosts, 'completed');
    
    echo "Processed $processed hosts successfully with $errors errors\n";
}

// Script start

// Disallow the script to run multiple instances if already running
$lockFile = dirname(__FILE__) . '/../locks/update_apm_status.lock';
$lockHandle = null;

// Check if the script is already running
if (isLocked($lockFile)) {
    echo "Script is already running. Exiting.\n";
    exit;
}

// Set the lock
try {
    setLock($lockFile, true);
    
    // Run the main function
    processAllHosts();
    
    echo "Script completed successfully.\n";
} catch (Exception $e) {
    // Handle any errors that occur during execution
    echo "Error: " . $e->getMessage() . "\n";
    updateProgress(0, 0, 'error: ' . $e->getMessage());
} finally {
    // Always release the lock when the script finishes
    setLock($lockFile, false);
} 