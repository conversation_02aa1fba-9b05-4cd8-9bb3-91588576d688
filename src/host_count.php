<?php
include __DIR__ . "/../loadenv.php";

// Function to fetch host count data from Nagios API
function getHostCount() {
    $apiUrl = 'https://' . $_SERVER['SERVER_ADDR'] . '/nagios/cgi-bin/statusjson.cgi?query=hostcount';
    
    // Check if hostgroup parameter exists in the URL and append it to the API URL
    if (isset($_GET['hostgroup']) && !empty($_GET['hostgroup'])) {
        $hostgroup = urlencode($_GET['hostgroup']);
        $apiUrl .= "&hostgroup=" . $hostgroup;
    }
    
    // Get admin credentials from database
    $credentials = getAdminCredentials();
    
    // Initialize cURL session
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERPWD, "{$credentials['username']}:{$credentials['password']}");
    
    // Execute cURL request
    $response = curl_exec($ch);
    
    // Check for errors
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        return ['error' => $error];
    }
    
    // Close cURL session
    curl_close($ch);
    
    // Decode JSON response
    $data = json_decode($response, true);
    
    // Check if JSON decoding was successful
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['error' => 'Invalid JSON response'];
    }
    
    // Return the host count data
    return $data;
}

/**
 * Get admin credentials from the database
 * @return array Credentials array with username and password
 */
function getAdminCredentials(): array {
    $conn = new mysqli($_ENV["DB_SERVER"], $_ENV["DB_USER"], $_ENV["DB_PASSWORD"], "blesk");
    
    if ($conn->connect_error) {
        return ["username" => "", "password" => ""];
    }
    
    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $conn->close();
        return [
            "username" => $row["username"],
            "password" => $row["password"]
        ];
    }
    
    $conn->close();
    return ["username" => "", "password" => ""];
}

// If this file is called directly, return the host count data as JSON
if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
    header('Content-Type: application/json');
    echo json_encode(getHostCount());
}
?> 