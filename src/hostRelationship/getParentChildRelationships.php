<?php
// src/hostRelationship/getParentChildRelationships.php

include "../../loadenv.php";

// Database connection
$db_host = $_ENV["DB_SERVER"];
$db_user = $_ENV["DB_USER"];
$db_pass = $_ENV["DB_PASSWORD"];
$db_name = 'db_nagiosql_v3';

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

if ($conn->connect_error) {
    // Return a server error response
    http_response_code(500);
    echo json_encode(['error' => "Connection failed: " . $conn->connect_error]);
    exit;
}

// Prevent caching
header("Cache-Control: no-cache, must-revalidate");
header("Content-Type: application/json");

$relationships = [];

$sql = "SELECT
            p.address AS parent_ip,
            p.host_name AS parent_hostname,
            c.address AS child_ip,
            c.host_name AS child_hostname
        FROM
            tbl_lnkHostToHost l
        JOIN
            tbl_host p ON l.idSlave = p.id
        JOIN
            tbl_host c ON l.idMaster = c.id";

$result = $conn->query($sql);

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $relationships[] = $row;
    }
    echo json_encode($relationships);
} else {
    http_response_code(500);
    echo json_encode(['error' => "Query failed: " . $conn->error]);
}

$conn->close();
?> 