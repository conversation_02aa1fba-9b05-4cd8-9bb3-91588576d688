<?php
include __DIR__ . "/../../loadenv.php";

class IpConfigHandler {
    private $conn;
    private const DB_NAME = "blesk";

    public function __construct() {
        $this->conn = new mysqli($_ENV["DB_SERVER"], $_ENV["DB_USER"], $_ENV["DB_PASSWORD"], self::DB_NAME);
        if ($this->conn->connect_error) {
            $this->handleError("Connection failed: " . $this->conn->connect_error);
        }
    }

    public function handleRequest(): void {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handlePostRequest();
        }
    }

    private function handlePostRequest(): void {
        header('Content-Type: application/json');
        $response = ['success' => false, 'message' => 'An unknown error occurred.'];

        // Validate required fields
        $iface = $this->validateInput('iface', null, 'Interface name is required.');
        $method = $this->validateInput('method', null, 'Connection method is required.');

        // Get all form data
        $formData = $_POST;

        // Get admin credentials for cURL request
        $credentials = $this->getAdminCredentials();

        // Initialize cURL session
        $ch = $this->initCurl('https://' . $_SERVER['SERVER_ADDR'] . '/settings-ip-proc.php', $credentials);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($formData));

        // Execute cURL request
        $result = curl_exec($ch);
        if ($result === false) {
            $response['message'] = 'cURL error: ' . curl_error($ch);
        } else {
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $responseBody = $result; // Capture the response body

            if ($httpCode === 200 && strpos($responseBody, 'fa fa-exclamation-triangle') === false){
                $response['success'] = true;
                $response['message'] = 'Network settings submitted successfully.';
            } else {
                $response['success'] = false;
                $response['message'] = 'Failed to update network settings. HTTP code: ' . $httpCode . '. Response: ' . $responseBody;
            }
        }
        curl_close($ch);

        echo json_encode($response);
        exit;
    }

    private function validateInput(string $field, ?int $filter, string $errorMessage): string {
        $value = isset($_POST[$field]) ? trim($_POST[$field]) : '';
        if (empty($value) || ($filter && !filter_var($value, $filter))) {
            echo json_encode(['success' => false, 'message' => $errorMessage]);
            exit;
        }
        return $value;
    }

    private function getAdminCredentials(): array {
        $sql = "SELECT username, password FROM users WHERE user_id = 1";
        $result = $this->conn->query($sql);

        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return [
                "username" => $row["username"],
                "password" => $row["password"]
            ];
        }
        return ["username" => "", "password" => ""];
    }

    private function initCurl(string $url, array $credentials): CurlHandle {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => "{$credentials['username']}:{$credentials['password']}",
            CURLOPT_HTTPHEADER => ['Content-Type: application/x-www-form-urlencoded']
        ]);
        return $ch;
    }

    private function handleError(string $message): void {
        error_log($message);
        header('Content-Type: application/json');
        echo json_encode(["success" => false, "message" => $message]);
        exit;
    }
}

// Instantiate and handle request
$handler = new IpConfigHandler();
$handler->handleRequest();
?>
