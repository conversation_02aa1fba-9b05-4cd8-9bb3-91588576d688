<?php
// --- Configuration & Defaults ---
$interface_name = 'N/A';
$ip_address = '';
$prefix = '';
$gateway = '';
$dns1 = '';
$dns2 = '';
$method = 'manual'; // Default to manual
$uuid = '';
$type = 'Ethernet'; // Common default
$onboot = 'yes';
$nm_controlled = 'yes'; // Assumed yes if nmcli works
$bootproto = 'none'; // Corresponds to 'manual'
$hwaddr = '';
$defroute = 'yes';
$ipv4_failure_fatal = 'no'; // No direct equivalent in show output, default
$ipv6init = 'no'; // Default, check later
$error_message = '';
$use_hostname_checked = ''; // Default unchecked

// --- Function to safely escape output ---
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// --- Get Active Interface ---
// Suppress potential errors from shell_exec if nmcli isn't found or fails
$status_output = @shell_exec('nmcli --terse --fields DEVICE,STATE device status 2>&1');
$active_interface = null;
if ($status_output === null) {
     $error_message = "Error: Failed to execute nmcli. Is it installed and in the web server's PATH?";
} elseif ($status_output) {
    $lines = explode("\n", trim($status_output));
    foreach ($lines as $line) {
        // Tolerate potential extra colons in device names (though unlikely)
        $parts = explode(':', $line);
        if (count($parts) >= 2) {
            $device = trim($parts[0]);
            $state = trim($parts[1]);
             // Handle cases like "virbr0:connected (externally)" -> only care about "connected" part
            if (strpos($state, 'connected') === 0) {
                 $active_interface = $device;
                 break; // Found the first connected interface
            }
        }
    }
}

if (!$active_interface && !$error_message) {
    $error_message = "Error: Could not automatically find an active 'connected' network interface. Please check 'nmcli device status'.";
    $interface_name = 'N/A (Error)';
} elseif ($active_interface) {
    $interface_name = h($active_interface);
}


// --- Get Interface Details using standard 'nmcli device show' (if interface found) ---
if ($active_interface) {
    // Suppress errors here too
    $details_output = @shell_exec('nmcli device show ' . escapeshellarg($active_interface) . ' 2>&1');

    if ($details_output === null) {
        $error_message = "Error: Failed to execute nmcli show for interface '$interface_name'.";
        $details_output = ''; // Prevent further processing
    } elseif (strpos($details_output, 'Error:') === 0) {
         $error_message = "Error getting details for interface '$interface_name': " . h(trim($details_output));
         $details_output = ''; // Prevent further processing
    }


    if ($details_output) {
        $lines = explode("\n", trim($details_output));
        $current_dns_index = 1; // To handle DNS[1], DNS[2], etc.

        foreach ($lines as $line) {
            $line = trim($line);

            // --- Use preg_match for more flexible parsing ---
            if (preg_match('/^GENERAL\.DEVICE:\s*(.+)$/', $line, $matches)) {
                // Already have $active_interface, this confirms it
            } elseif (preg_match('/^GENERAL\.TYPE:\s*(.+)$/', $line, $matches)) {
                $type = trim($matches[1]);
            } elseif (preg_match('/^GENERAL\.HWADDR:\s*(.+)$/', $line, $matches)) {
                $hwaddr = trim($matches[1]);
            } elseif (preg_match('/^GENERAL\.UUID:\s*(.+)$/', $line, $matches)) {
                $uuid = trim($matches[1]);
            } elseif (preg_match('/^IP4\.ADDRESS\[\d+\]:\s*([\d\.]+)\/(\d+)/', $line, $matches)) {
                // Capture the first IP address found
                if (empty($ip_address)) {
                    $ip_address = trim($matches[1]);
                    $prefix = trim($matches[2]);
                }
            } elseif (preg_match('/^IP4\.GATEWAY:\s*([\d\.]+)/', $line, $matches)) {
                $gateway = trim($matches[1]);
            } elseif (preg_match('/^IP4\.DNS\[\d+\]:\s*([\d\.]+)/', $line, $matches)) {
                if ($current_dns_index === 1) {
                    $dns1 = trim($matches[1]);
                } elseif ($current_dns_index === 2) {
                    $dns2 = trim($matches[1]);
                }
                $current_dns_index++;
                // Add more DNS if needed: elseif ($current_dns_index === 3) { $dns3 = ... }
            } elseif (preg_match('/^IP4\.METHOD:\s*(.+)$/', $line, $matches)) {
                $ip4_method = strtolower(trim($matches[1]));
                if ($ip4_method === 'auto') {
                    $method = 'dhcp';
                    $bootproto = 'dhcp';
                } else {
                    $method = 'manual'; // includes 'manual', 'shared', 'disabled' etc.
                    $bootproto = ($ip4_method === 'manual') ? 'none' : $ip4_method; // 'none' for manual, keep others
                }
            } elseif (preg_match('/^CONNECTION\.AUTOCONNECT:\s*(yes|no)/', $line, $matches)) {
                $onboot = trim($matches[1]);
            } elseif (preg_match('/^IP4\.NEVER-DEFAULT:\s*(yes|no)/', $line, $matches)) {
                $defroute = (trim($matches[1]) === 'no') ? 'yes' : 'no'; // Inverse logic
            } elseif (preg_match('/^IP6\.ADDRESS\[\d+\]:/', $line)) {
                // If any IPv6 address exists, assume IPV6INIT might be yes (best guess)
                $ipv6init = 'yes';
            } elseif (preg_match('/^IP6\.METHOD:\s*(auto|manual|dhcp|disabled|ignore)/i', $line, $matches)) {
                 // If IPv6 method is not disabled or ignore, likely init is yes
                 if (!in_array(strtolower(trim($matches[1])), ['disabled', 'ignore'])) {
                    $ipv6init = 'yes';
                 }
            }
            // Note: NM_CONTROLLED, BOOTPROTO (exact old value), IPV4_FAILURE_FATAL
            // don't have direct 1:1 mappings in the standard 'show' output.
            // We derive BOOTPROTO from IP4.METHOD and make assumptions for others.
        }
         // If method ended up DHCP, clear out manual settings for display consistency
        if ($method === 'dhcp') {
            // $ip_address = ''; // Or display DHCP assigned? Let's display them but make readonly.
            // $prefix = '';
            // $gateway = '';
            // $dns1 = '';
            // $dns2 = '';
        }

    } elseif (!$error_message) {
        // If shell_exec returned empty string or false, but not null or an explicit error message
        $error_message = "Warning: Received no output from 'nmcli device show $interface_name'. Configuration might be incomplete.";
    }
}

// --- Method change is now handled by JavaScript without page reload ---
// When in DHCP mode, only the IP address field is shown (readonly)
// When in Manual mode, all fields are shown and editable

// --- Determine Checked State for Use Hostname (Example: Load from config?) ---
// $use_hostname_checked = (load_hostname_preference() === true) ? 'checked' : '';

// Check states for radio buttons
$manual_checked = ($method === 'manual') ? 'checked' : '';
$dhcp_checked = ($method === 'dhcp') ? 'checked' : '';
$readonly_attr = ($method === 'dhcp') ? 'readonly' : '';

// --- Store old values (using initially fetched values before potential method switch) ---
// We need to fetch them again *before* the method switch logic if we want true 'old' values
// Or, more simply, just pass the currently displayed values as 'old' for the processing script
// Let's pass the currently fetched values
$ipaddr_old_esc = h($ip_address);
$dns1_old_esc = h($dns1);
$dns2_old_esc = h($dns2);

?>
<?php if ($error_message): ?>
<div class="message message-error">
    <?php echo $error_message; /* Already escaped if needed during generation */ ?>
</div>
<?php endif; ?>

<p>Configure network settings for interface: <strong><?php echo $interface_name; /* Already escaped */ ?></strong></p>

<form id="ip-config-form" role="form" action="javascript:void(0);" method="post" autocomplete="off">
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get radio buttons
        const manualRadio = document.getElementById('method_manual');
        const dhcpRadio = document.getElementById('method_dhcp');

        // Get input fields that should be readonly when DHCP is selected
        const ipFields = ['IPADDR', 'PREFIX', 'GATEWAY', 'DNS1', 'DNS2'];
        const ipContainers = ipFields.map(id => document.getElementById(id).closest('.input-group'));

        // Function to update field visibility based on method
        function updateFieldsVisibility(isDhcp) {
            // Update bootproto hidden field
            document.querySelector('input[name="BOOTPROTO"]').value = isDhcp ? 'dhcp' : 'none';

            // Update IP address description
            const ipDescription = document.getElementById('ipaddr-description');
            if (ipDescription) {
                ipDescription.textContent = isDhcp ?
                    'Current IP address assigned by DHCP (read-only).' :
                    'The static IP address for this interface.';
            }

            if (isDhcp) {
                // In DHCP mode, only show IP address field and make it readonly
                ipContainers.forEach(function(container, index) {
                    if (index === 0) { // Only show IP address field
                        container.style.display = 'flex';
                        document.getElementById(ipFields[index]).setAttribute('readonly', 'readonly');
                    } else {
                        container.style.display = 'none';
                    }
                });
            } else {
                // In Manual mode, show all fields and make them editable
                ipContainers.forEach(function(container, index) {
                    container.style.display = 'flex';
                    document.getElementById(ipFields[index]).removeAttribute('readonly');
                });
            }
        }

        // Add event listeners to radio buttons
        manualRadio.addEventListener('change', function() {
            if(this.checked) {
                updateFieldsVisibility(false);
            }
        });

        dhcpRadio.addEventListener('change', function() {
            if(this.checked) {
                updateFieldsVisibility(true);
            }
        });

        // Initialize fields based on current selection
        updateFieldsVisibility(dhcpRadio.checked);

        // Handle form submission
        document.getElementById('ip-config-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading state
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = submitBtn.getAttribute('data-loading-text');

            // Get status message container
            const statusDiv = document.getElementById('ip-config-status');

            // Ensure all fields are included in the form submission even if hidden
            ipFields.forEach(function(fieldId) {
                const field = document.getElementById(fieldId);
                if (field.closest('.input-group').style.display === 'none') {
                    field.disabled = false; // Enable hidden fields for form submission
                }
            });

            // Get form data
            const formData = new FormData(this);

            // Submit form using fetch API
            fetch('src/ipConfig/ipConfigHandler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Update status message
                statusDiv.style.display = 'block';
                if (data.success) {
                    statusDiv.className = 'message message-success';
                    statusDiv.innerHTML = '<i class="fa fa-check-circle"></i>&nbsp;' + data.message;
                } else {
                    statusDiv.className = 'message message-error';
                    statusDiv.innerHTML = '<i class="fa fa-exclamation-triangle"></i>&nbsp;Error: ' + data.message;
                    console.error('Form submission error:', data);
                }

                // Restore button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;

                // Hide status message after 5 seconds
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            })
            .catch(error => {
                // Handle network errors
                statusDiv.style.display = 'block';
                statusDiv.className = 'message message-error';
                statusDiv.innerHTML = '<i class="fa fa-times-circle"></i>&nbsp;Network error: ' + error.message;
                console.error('Network error:', error);

                // Restore button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
            });
        });
    });
    </script>
    <?php /* Hidden fields to pass current/static config */ ?>
    <input type="hidden" name="sender" value=""> <?php /* Placeholder */ ?>
    <input type="hidden" name="ipaddr_old" value="<?php echo $ipaddr_old_esc; ?>">
    <input type="hidden" name="ipaddr_nagios" value=""> <?php /* Placeholder */ ?>
    <input type="hidden" name="dns1_old" value="<?php echo $dns1_old_esc; ?>">
    <input type="hidden" name="dns2_old" value="<?php echo $dns2_old_esc; ?>">
    <input type="hidden" name="iface" value="<?php echo $interface_name; ?>"> <?php /* Already escaped */ ?>
    <input type="hidden" name="DEVICE" value="<?php echo $interface_name; ?>"> <?php /* Already escaped */ ?>
    <input type="hidden" name="TYPE" value="<?php echo h($type); ?>">
    <input type="hidden" name="UUID" value="<?php echo h($uuid); ?>">
    <input type="hidden" name="ONBOOT" value="<?php echo h($onboot); ?>">
    <input type="hidden" name="NM_CONTROLLED" value="<?php echo h($nm_controlled); ?>">
    <input type="hidden" name="BOOTPROTO" value="<?php echo h($bootproto); ?>"> <?php /* Reflects current selection */ ?>
    <input type="hidden" name="HWADDR" value="<?php echo h($hwaddr); ?>">
    <input type="hidden" name="DEFROUTE" value="<?php echo h($defroute); ?>">
    <input type="hidden" name="IPV4_FAILURE_FATAL" value="<?php echo h($ipv4_failure_fatal); ?>">
    <input type="hidden" name="IPV6INIT" value="<?php echo h($ipv6init); ?>">

    <?php /* Method Selection Radio Buttons */ ?>
    <fieldset style="margin-bottom: 20px;">
        <legend>Connection Method</legend>
        <div style="display: flex; flex-direction: row;">
            <label class="radio-label" style="margin-right: 15px;">
                <input type="radio" name="method" id="method_manual" value="manual" <?php echo $manual_checked; ?>>
                Manual
            </label>
            <label class="radio-label">
                <input type="radio" name="method" id="method_dhcp" value="dhcp" <?php echo $dhcp_checked; ?>>
                DHCP (automatic)
            </label>
        </div>
    </fieldset>

    <?php /* Hostname Checkbox */ ?>
    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 20px;">
        <label class="checkbox-label">
            <input type="checkbox" name="use_hostname_in_urls" id="use_hostname_in_urls" value="yes" <?php echo $use_hostname_checked; ?>>
            Use hostname in URLs instead of IP address
        </label>
        <small>When enabled, system URLs will use the hostname rather than the IP address.</small>
    </div>

    <?php /* IP Configuration Fields */ ?>
    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 15px;">
        <label for="IPADDR" style="margin-bottom: 5px;"><i class="fa fa-globe"></i> IP Address:</label>
        <input type="text" id="IPADDR" name="IPADDR" value="<?php echo h($ip_address); ?>" <?php echo $readonly_attr; ?>>
        <small id="ipaddr-description"><?php echo ($method === 'dhcp') ? 'Current IP address assigned by DHCP (read-only).' : 'The static IP address for this interface.'; ?></small>
    </div>

    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 15px;">
        <label for="PREFIX" style="margin-bottom: 5px;"><i class="fa fa-sitemap"></i> Prefix:</label>
        <input type="text" id="PREFIX" name="PREFIX" value="<?php echo h($prefix); ?>" <?php echo $readonly_attr; ?>>
        <small>Network prefix length (e.g., 24 for a /24 subnet mask).</small>
    </div>

    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 15px;">
        <label for="GATEWAY" style="margin-bottom: 5px;"><i class="fa fa-exchange"></i> Gateway:</label>
        <input type="text" id="GATEWAY" name="GATEWAY" value="<?php echo h($gateway); ?>" <?php echo $readonly_attr; ?>>
        <small>The default gateway for this network.</small>
    </div>

    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 15px;">
        <label for="DNS1" style="margin-bottom: 5px;"><i class="fa fa-server"></i> DNS Server 1:</label>
        <input type="text" id="DNS1" name="DNS1" value="<?php echo h($dns1); ?>" <?php echo $readonly_attr; ?>>
        <small>Primary DNS server for name resolution.</small>
    </div>

    <div class="input-group" style="display: flex; flex-direction: column; margin-bottom: 15px;">
        <label for="DNS2" style="margin-bottom: 5px;"><i class="fa fa-server"></i> DNS Server 2:</label>
        <input type="text" id="DNS2" name="DNS2" value="<?php echo h($dns2); ?>" <?php echo $readonly_attr; ?>>
        <small>Secondary DNS server (optional).</small>
    </div>

    <?php /* Status Message */ ?>
    <div id="ip-config-status" class="message" style="display: none; margin-top: 15px;"></div>

    <?php /* Submit Button */ ?>
    <button type="submit" data-loading-text="<i class='fa fa-spinner fa-spin'></i>&nbsp;Please wait..." name="Submit" value="submit" class="btn-restart" style="margin-top: 15px;">
        <i class="fa fa-save"></i>&nbsp;Save Configuration
    </button>
</form>