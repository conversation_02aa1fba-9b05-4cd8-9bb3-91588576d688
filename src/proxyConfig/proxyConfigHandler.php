<?php
// Ensure errors are displayed for debugging (remove or adjust for production)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON for the response
header('Content-Type: application/json');

// Autoload the BleskProxyEditor class
spl_autoload_register(function ($class) {
    // Adjust the path based on the location of proxyConfigHandler.php relative to the include directory
    $class_path = __DIR__ . '/../../../include/class.' . $class . '.php';
    if (file_exists($class_path)) {
        include $class_path;
    } else {
        error_log("Error: Class file not found at " . $class_path);
        // Output JSON error and exit if critical class is missing
        if ($class === 'BleskProxyEditor') {
             echo json_encode(['success' => false, 'message' => 'Server error: BleskProxyEditor class not found.']);
             exit;
        }
    }
});

// Check if BleskLogger class exists before using it
if (!class_exists('BleskLogger')) {
    class BleskLogger { // Dummy class
        public function __construct($param) {}
        public function logMsg($msg) { error_log("BleskLogger (dummy): " . $msg); }
    }
    error_log("Warning: BleskLogger class not found via autoload. Using dummy class.");
}

// Check if the main class exists
if (!class_exists('BleskProxyEditor')) {
    echo json_encode(['success' => false, 'message' => 'Server error: BleskProxyEditor class not found.']);
    exit;
}

$response = ['success' => false, 'message' => 'Invalid request.'];

// --- Handle AJAX POST Request ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $proxyEditor = new BleskProxyEditor();

        // Prepare the data array for updateProxies
        $newProxies = [
            'http_proxy' => trim($_POST['http_proxy'] ?? ''),
            'https_proxy' => trim($_POST['https_proxy'] ?? ''),
            'ftp_proxy' => trim($_POST['ftp_proxy'] ?? ''),
            'http_proxy_line' => $_POST['http_proxy_line'] ?? '', // Get original line from hidden input
            'https_proxy_line' => $_POST['https_proxy_line'] ?? '',
            'ftp_proxy_line' => $_POST['ftp_proxy_line'] ?? ''
        ];

         // Add placeholder if proxy field is empty but original line was not provided (e.g., first time setting)
         // This logic might need refinement based on how BleskProxyEditor handles adding new lines vs modifying existing ones.
         // Assuming sedReplace needs *something* to match if the line doesn't exist yet.
        if (empty($newProxies['http_proxy_line']) && !empty($newProxies['http_proxy'])) {
             $newProxies['http_proxy_line'] = '#?export http_proxy='; // A pattern that might match commented or non-existent
        }
         if (empty($newProxies['https_proxy_line']) && !empty($newProxies['https_proxy'])) {
             $newProxies['https_proxy_line'] = '#?export https_proxy=';
        }
         if (empty($newProxies['ftp_proxy_line']) && !empty($newProxies['ftp_proxy'])) {
             $newProxies['ftp_proxy_line'] = '#?export ftp_proxy=';
        }


        // Update .bash_profile
        $bashProfileUpdated = $proxyEditor->updateProxies($newProxies);

        // Update .npmrc (only http and https)
        $npmProxies = [
            'http_proxy' => $newProxies['http_proxy'],
            'https_proxy' => $newProxies['https_proxy']
        ];
        $npmUpdated = $proxyEditor->updateNodePackageManagerProxies($npmProxies);

        if ($bashProfileUpdated && $npmUpdated) {
            // Re-fetch data after update to return the latest values
            $httpProxyData = $proxyEditor->getHttpProxy();
            $httpsProxyData = $proxyEditor->getHttpsProxy();
            $ftpProxyData = $proxyEditor->getFtpProxy();

            $response = [
                'success' => true,
                'message' => 'Proxy settings updated successfully.',
                'updatedValues' => [
                    'http_proxy' => $httpProxyData['http_proxy'] ?? '',
                    'https_proxy' => $httpsProxyData['https_proxy'] ?? '',
                    'ftp_proxy' => $ftpProxyData['ftp_proxy'] ?? '',
                    'http_proxy_line' => $httpProxyData['http_line'] ?? '', // Send back the potentially new line
                    'https_proxy_line' => $httpsProxyData['https_line'] ?? '',
                    'ftp_proxy_line' => $ftpProxyData['ftp_line'] ?? ''
                ]
            ];
        } else {
            $error_details = '';
             if (!$bashProfileUpdated) $error_details .= ' Failed to update .bash_profile.';
             if (!$npmUpdated) $error_details .= ' Failed to update .npmrc.';
            $response = ['success' => false, 'message' => 'Error updating proxy settings.' . $error_details];
        }
    } catch (Exception $e) {
        error_log("Error in proxyConfigHandler: " . $e->getMessage());
        $response = ['success' => false, 'message' => 'An unexpected server error occurred. Check logs.'];
    }
}

// Output the JSON response
echo json_encode($response);
exit;
?>