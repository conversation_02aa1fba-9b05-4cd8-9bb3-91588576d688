<?php
// Ensure errors are displayed for debugging (remove or adjust for production)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Autoload the BleskProxyEditor class
spl_autoload_register(function ($class) {
    $class_path = __DIR__ . '/../../../include/class.' . $class . '.php';
    if (file_exists($class_path)) {
        include $class_path;
    } else {
        // Log error or handle missing class file
        error_log("Error: Class file not found at " . $class_path);
    }
});

// Check if BleskLogger class exists before using it
if (!class_exists('BleskLogger')) {
    // Define a dummy class
    class BleskLogger {
        public function __construct($param) {}
        public function logMsg($msg) { error_log("BleskLogger (dummy): " . $msg); }
    }
    error_log("Warning: BleskLogger class not found via autoload. Using dummy class.");
}

$initial_load_error = '';
$http_proxy_value = '';
$https_proxy_value = '';
$ftp_proxy_value = '';
$http_proxy_line = '';
$https_proxy_line = '';
$ftp_proxy_line = '';

// Check if the main class exists before proceeding
if (!class_exists('BleskProxyEditor')) {
    $initial_load_error = 'Error: BleskProxyEditor class not found. Cannot load proxy settings.';
} else {
    try {
        $proxyEditor = new BleskProxyEditor();
        // Fetch current proxy settings for initial form load
        $httpProxyData = $proxyEditor->getHttpProxy();
        $httpsProxyData = $proxyEditor->getHttpsProxy();
        $ftpProxyData = $proxyEditor->getFtpProxy();

        // Prepare values for the form
        $http_proxy_value = $httpProxyData['http_proxy'] ?? '';
        $https_proxy_value = $httpsProxyData['https_proxy'] ?? '';
        $ftp_proxy_value = $ftpProxyData['ftp_proxy'] ?? '';

        // Store the original lines for the update function (needed for sedReplace)
        $http_proxy_line = $httpProxyData['http_line'] ?? '';
        $https_proxy_line = $httpsProxyData['https_line'] ?? '';
        $ftp_proxy_line = $ftpProxyData['ftp_line'] ?? '';
    } catch (Exception $e) {
        error_log("Error fetching initial proxy settings: " . $e->getMessage());
        $initial_load_error = 'Error fetching initial proxy settings. Check logs.';
    }
}

?>

<style>
/* Custom styles for this form */
#proxy-settings-form .input-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

#proxy-settings-form .input-group label {
    margin-bottom: 5px;
    font-weight: 500;
}

#proxy-settings-form .input-group input {
    width: 100%;
    max-width: 400px;
}

#proxy-settings-form .input-group small {
    margin-top: 4px;
}
</style>

<h2><i class="fa fa-globe"></i> Proxy Settings</h2>
<p>Configure proxy server settings for outbound network connections.</p>

<!-- Status message area -->
<div id="proxy-config-status" class="message" style="margin-bottom: 15px; display: <?php echo $initial_load_error ? 'block' : 'none'; ?>;">
    <?php if ($initial_load_error): ?>
        <span class="message-error"><?php echo htmlspecialchars($initial_load_error); ?></span>
    <?php endif; ?>
</div>


<form id="proxy-settings-form" method="POST" action="src/proxyConfig/proxyConfigHandler.php"> <!-- Action points to handler, but JS will intercept -->
    <!-- Hidden fields to store the original lines for replacement -->
    <input type="hidden" id="http_proxy_line" name="http_proxy_line" value="<?php echo htmlspecialchars($http_proxy_line); ?>">
    <input type="hidden" id="https_proxy_line" name="https_proxy_line" value="<?php echo htmlspecialchars($https_proxy_line); ?>">
    <input type="hidden" id="ftp_proxy_line" name="ftp_proxy_line" value="<?php echo htmlspecialchars($ftp_proxy_line); ?>">

    <fieldset <?php echo $initial_load_error ? 'disabled' : ''; ?>> <!-- Disable fieldset if initial load failed -->
        <legend>Proxy Configuration</legend>

        <div class="input-group">
            <label for="http_proxy"><i class="fa fa-cloud"></i> HTTP Proxy:</label>
            <input type="text" id="http_proxy" name="http_proxy" value="<?php echo htmlspecialchars($http_proxy_value); ?>" placeholder="e.g., http://user:<EMAIL>:8080">
            <small>Proxy server address for HTTP connections.</small>
        </div>

        <div class="input-group">
            <label for="https_proxy"><i class="fa fa-lock"></i> HTTPS Proxy:</label>
            <input type="text" id="https_proxy" name="https_proxy" value="<?php echo htmlspecialchars($https_proxy_value); ?>" placeholder="e.g., http://user:<EMAIL>:8080">
             <small>Proxy server address for HTTPS connections. Often the same as HTTP.</small>
        </div>

        <div class="input-group">
            <label for="ftp_proxy"><i class="fa fa-folder-open"></i> FTP Proxy:</label>
            <input type="text" id="ftp_proxy" name="ftp_proxy" value="<?php echo htmlspecialchars($ftp_proxy_value); ?>" placeholder="e.g., http://user:<EMAIL>:8080">
             <small>Proxy server address for FTP connections.</small>
        </div>

    </fieldset>

    <button type="submit" id="save-proxy-btn" class="btn-restart" style="margin-top: 15px;" <?php echo $initial_load_error ? 'disabled' : ''; ?>>
        <i class="fa fa-save"></i>&nbsp;<span class="btn-text">Save Proxy Settings</span>
        <i class="fa fa-spinner fa-spin" style="display: none; margin-left: 5px;"></i>
    </button>
</form>

<script>
// Ensure this runs after the DOM is fully loaded and jQuery is available
$(document).ready(function() {
    $('#proxy-settings-form').on('submit', function(e) {
        e.preventDefault(); // Prevent traditional form submission

        const form = $(this);
        const statusDiv = $('#proxy-config-status');
        const submitButton = $('#save-proxy-btn');
        const buttonText = submitButton.find('.btn-text');
        const spinner = submitButton.find('.fa-spinner');

        // Clear previous messages and show spinner
        statusDiv.hide().empty();
        buttonText.text('Saving...');
        spinner.show();
        submitButton.prop('disabled', true);

        $.ajax({
            type: 'POST',
            url: form.attr('action'), // Get URL from form's action attribute
            data: form.serialize(), // Serialize form data
            dataType: 'json', // Expect JSON response from the handler
            success: function(response) {
                if (response.success) {
                    statusDiv.removeClass('message-error').addClass('message-success');
                    // Update form fields with new values from response
                    if (response.updatedValues) {
                        $('#http_proxy').val(response.updatedValues.http_proxy);
                        $('#https_proxy').val(response.updatedValues.https_proxy);
                        $('#ftp_proxy').val(response.updatedValues.ftp_proxy);
                        // Update hidden fields with potentially new lines
                        $('#http_proxy_line').val(response.updatedValues.http_proxy_line);
                        $('#https_proxy_line').val(response.updatedValues.https_proxy_line);
                        $('#ftp_proxy_line').val(response.updatedValues.ftp_proxy_line);
                    }
                } else {
                    statusDiv.removeClass('message-success').addClass('message-error');
                }
                statusDiv.text(response.message).show(); // Display message
            },
            error: function(jqXHR, textStatus, errorThrown) {
                // Handle AJAX errors (e.g., network issue, server error 500)
                console.error("AJAX Error:", textStatus, errorThrown, jqXHR.responseText);
                statusDiv.removeClass('message-success').addClass('message-error');
                statusDiv.text('An error occurred while communicating with the server. Please try again.').show();
            },
            complete: function() {
                // Hide spinner, restore button text, and re-enable button
                spinner.hide();
                buttonText.text('Save Proxy Settings');
                submitButton.prop('disabled', false);
            }
        });
    });
});
</script>