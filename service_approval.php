<?php
include "loadenv.php";

/**
 * Handle service approval or rejection
 * If approved, deletes the entry from servicesPending and sets apmStatus to 'not-added' in the hosts table
 * If rejected, just deletes the entry from servicesPending
 */

function getDatabaseConnection() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"]; 
    $dbname = "bubblemaps";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

function getHostnameByIP(string $ip) {
    try {
        // Get credentials from the database
        $credentials = getUserCredentials();
        $hostname = getSelfIp();
        if ($credentials === null) {
            throw new Exception("Failed to retrieve user credentials");
        }
        $nagiosUrl = "https://{$hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $nagiosUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERPWD, $credentials['tfUsername'] . ":" . $credentials['tfPassword']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        $response = curl_exec($ch);
        if ($response === false) {
            $errorMsg = curl_error($ch);
            curl_close($ch);
            throw new Exception("cURL error: " . $errorMsg);
        }
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($httpCode != 200) {
            throw new Exception("HTTP error! Status: {$httpCode}");
        }
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON parse error: " . json_last_error_msg());
        }
        if (isset($data['data']['hostlist'])) {
            foreach ($data['data']['hostlist'] as $host) {
                if (isset($host['address']) && $host['address'] === $ip) {
                    return $host['name'];
                }
            }
        }
        return null;
    } catch (Exception $e) {
        error_log('Error fetching host data: ' . $e->getMessage());
        return null;
    }
}

function getDatabaseConnectionNagiosql() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"];
    $dbname = "db_nagiosql_v3";
    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    return $conn;
}

function getSelfIp() {
    $ip = shell_exec("cat /etc/sysconfig/ipaddr 2>&1");
    if ($ip === null) {
        die("Error: Unable to retrieve IP address.");
    }
    return trim($ip);
}

function getUserCredentials() {
    $conn = getDatabaseConnectionAdminUser();
    $sql = "SELECT username, password FROM users WHERE user_id = 1";
    $result = $conn->query($sql);
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $userCredentials = [
            "tfUsername" => $row["username"],
            "tfPassword" => $row["password"]
        ];
    } else {
        $userCredentials = null;
    }
    $conn->close();
    return $userCredentials;
}

function getDatabaseConnectionAdminUser() {
    $servername = $_ENV["DB_SERVER"];
    $username = $_ENV["DB_USER"];
    $password = $_ENV["DB_PASSWORD"];
    $dbname = "blesk";
    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    return $conn;
}

function simulateVerifyActions($hostname) {
    $actions = ['butValue1', 'butValue2', 'butValue3', 'butValue4'];
    try {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/index.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'tfUsername' => $_ENV['NAGIOS_USER'],
            'tfPassword' => $_ENV['NAGIOS_PASS'],
            'Submit' => 'Login'
        ]));
        $response = curl_exec($ch);
        if(curl_errno($ch)) throw new Exception('Login failed: ' . curl_error($ch));
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($status >= 300 && $status < 400) {
            $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            curl_setopt($ch, CURLOPT_URL, $redirect);
            curl_exec($ch);
        }
        curl_setopt($ch, CURLOPT_URL, "https://$hostname/nagiosql/admin/verify.php");
        foreach ($actions as $action) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, "$action=Do+it");
            $response = curl_exec($ch);
            if(curl_errno($ch)) throw new Exception("Action $action failed");
        }
        curl_close($ch);
    } catch (Exception $e) {
        error_log($e->getMessage());
    }
}

function approveService() {
    if (isset($_GET['id']) && isset($_GET['action'])) {
        $id = $_GET['id'];
        $action = $_GET['action'];
        
        $conn = getDatabaseConnection();
        
        if ($action === 'approve') {
            // First, get the host info and services from servicesPending
            $getHostInfoSql = "SELECT host_ip, infra, services FROM servicesPending WHERE id = ?";
            $ip = "";
            $infra = "";
            $servicesJson = null;
            if ($stmt = $conn->prepare($getHostInfoSql)) {
                $stmt->bind_param('i', $id);
                $stmt->execute();
                $result = $stmt->get_result();
                if ($row = $result->fetch_assoc()) {
                    $ip = $row['host_ip'];
                    $infra = $row['infra'];
                    $servicesJson = $row['services'];
                } else {
                    echo "Service record not found.";
                    $stmt->close();
                    $conn->close();
                    return;
                }
                $stmt->close();
            } else {
                echo "Error preparing query: " . $conn->error;
                $conn->close();
                return;
            }

            $services = json_decode($servicesJson, true);
            if (!is_array($services)) $services = [];

            // Get the real hostname from Nagios
            $realHostName = getHostnameByIP($ip);
            if (!$realHostName) {
                echo "Could not resolve real hostname for IP $ip.";
                $conn->close();
                return;
            }

            // Connect to NagiosQL DB
            $nagiosql = getDatabaseConnectionNagiosql();
            $configName = "imp_" . $realHostName;
            $query = "SELECT id, service_description, check_command FROM tbl_service WHERE config_name = ?";
            $stmt = $nagiosql->prepare($query);
            $stmt->bind_param('s', $configName);
            $stmt->execute();
            $result = $stmt->get_result();
            $nagiosServices = [];
            while ($row = $result->fetch_assoc()) {
                $nagiosServices[] = $row;
            }
            $stmt->close();

            $updated = false;
            $allRenamed = true;
            $portNotFound = false;
            $hasPortServices = false;
            $hasNonPortServices = false;
            foreach ($services as $service) {
                if (preg_match('/^Port-(\\d+)-(.*)$/', $service, $matches)) {
                    $hasPortServices = true;
                    $port = $matches[1];
                    $newName = $matches[2];
                    $found = false;
                    foreach ($nagiosServices as $nagiosService) {
                        $checkCmdParts = explode('!', $nagiosService['check_command']);
                        if (isset($checkCmdParts[2]) && $checkCmdParts[2] == $port) {
                            $found = true;
                            if ($nagiosService['service_description'] !== $newName) {
                                // Update service_description
                                $update = $nagiosql->prepare("UPDATE tbl_service SET service_description = ? WHERE id = ?");
                                $update->bind_param('si', $newName, $nagiosService['id']);
                                $update->execute();
                                $update->close();
                                $updated = true;
                            }
                        }
                    }
                    if (!$found) {
                        $portNotFound = true;
                        break;
                    }
                } else {
                    $hasNonPortServices = true;
                }
            }
            $nagiosql->close();

            if ($portNotFound) {
                // If any port was not found, set host as not-added and delete from servicesPending
                $conn->begin_transaction();
                try {
                    $updateHostSql = "UPDATE hosts SET apmStatus = 'not-added', muted = 0 WHERE ip = ? AND infra = ?";
                    $updateStmt = $conn->prepare($updateHostSql);
                    $updateStmt->bind_param('ss', $ip, $infra);
                    if (!$updateStmt->execute()) {
                        throw new Exception("Error updating host status: " . $updateStmt->error);
                    }
                    $updateStmt->close();
                    $deleteServiceSql = "DELETE FROM servicesPending WHERE id = ?";
                    $deleteStmt = $conn->prepare($deleteServiceSql);
                    $deleteStmt->bind_param('i', $id);
                    if (!$deleteStmt->execute()) {
                        throw new Exception("Error deleting service entry: " . $deleteStmt->error);
                    }
                    $deleteStmt->close();
                    $conn->commit();
                    echo "Port not found. Host marked as not-added and service entry deleted.";
                } catch (Exception $e) {
                    $conn->rollback();
                    echo $e->getMessage();
                }
                $conn->close();
                return;
            }

            if ($updated) {
                simulateVerifyActions(getSelfIp());
            }

            $conn->begin_transaction();
            try {
                // Mark as 'not-added' if there are any non-port services, otherwise mark as 'pending' if port services were updated
                $status = $hasNonPortServices ? 'not-added' : ($hasPortServices && $updated ? 'pending' : 'not-added');
                $updateHostSql = "UPDATE hosts SET apmStatus = ?, muted = 0 WHERE ip = ? AND infra = ?";
                $updateStmt = $conn->prepare($updateHostSql);
                $updateStmt->bind_param('sss', $status, $ip, $infra);
                if (!$updateStmt->execute()) {
                    throw new Exception("Error updating host status: " . $updateStmt->error);
                }
                $updateStmt->close();
                $deleteServiceSql = "DELETE FROM servicesPending WHERE id = ?";
                $deleteStmt = $conn->prepare($deleteServiceSql);
                $deleteStmt->bind_param('i', $id);
                if (!$deleteStmt->execute()) {
                    throw new Exception("Error deleting service entry: " . $deleteStmt->error);
                }
                $deleteStmt->close();
                $conn->commit();
                echo "Services approved and host marked as " . $status . ".";
            } catch (Exception $e) {
                $conn->rollback();
                echo $e->getMessage();
            }
            
        } elseif ($action === 'reject') {
            // Get host IP from servicesPending
            $getHostIpSql = "SELECT host_ip, infra FROM servicesPending WHERE id = ?";
            $stmt = $conn->prepare($getHostIpSql);
            $stmt->bind_param('i', $id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($row = $result->fetch_assoc()) {
                $ip = $row['host_ip'];
                $infra = $row['infra'];
                $stmt->close();
                
                // Begin transaction
                $conn->begin_transaction();
                
                try {
                    // Set the host as muted
                    $updateHostSql = "UPDATE hosts SET muted = 1 WHERE ip = ? AND infra = ?";
                    $updateStmt = $conn->prepare($updateHostSql);
                    $updateStmt->bind_param('ss', $ip, $infra);
                    
                    if (!$updateStmt->execute()) {
                        throw new Exception("Error setting host as muted: " . $updateStmt->error);
                    }
                    
                    $updateStmt->close();
                    
                    // Commit the transaction
                    $conn->commit();
                    echo "Services rejected and host muted";
                    
                } catch (Exception $e) {
                    // Rollback transaction on error
                    $conn->rollback();
                    echo $e->getMessage();
                }
            } else {
                echo "Service record not found.";
                $stmt->close();
            }
        } else {
            echo "Invalid action. Use 'approve' or 'reject'.";
        }
        
        $conn->close();
    } else {
        echo "Missing required parameters. Need id and action.";
    }
}

// Get all services waiting for approval
function getServicesWaitingApproval() {
    $conn = getDatabaseConnection();
    
    // Get pagination parameters
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 50; // Number of results per page
    $offset = ($page - 1) * $limit;
    
    // Get search term if present
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    
    // Build WHERE clause
    $where = "h.muted = 0";
    $params = [];
    $types = "";
    
    if (isset($_GET['infra'])) {
        $infra = $_GET['infra'];
        $where .= " AND sp.infra = ?";
        $params[] = $infra;
        $types .= "s";
    }
    
    // Add search condition if search term is provided
    if (!empty($search)) {
        $where .= " AND (sp.host_name LIKE ? OR sp.host_ip LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $types .= "ss";
    }
    
    // First, get the total count of services matching the criteria
    $countSql = "SELECT COUNT(*) as total FROM servicesPending sp 
                 JOIN hosts h ON sp.host_ip = h.ip AND (sp.infra = h.infra OR (sp.infra IS NULL AND h.infra IS NULL)) 
                 WHERE $where";
    
    $countStmt = $conn->prepare($countSql);
    if (!empty($params)) {
        $countStmt->bind_param($types, ...$params);
    }
    $countStmt->execute();
    $countResult = $countStmt->get_result();
    $countRow = $countResult->fetch_assoc();
    $totalServices = $countRow['total'];
    $countStmt->close();
    
    // Calculate total pages
    $totalPages = ceil($totalServices / $limit);
    
    // Make sure page is in valid range
    if ($page < 1) $page = 1;
    if ($page > $totalPages && $totalPages > 0) $page = $totalPages;
    
    // Then, get the services data with pagination
    $sql = "SELECT sp.id, sp.host_ip, sp.host_name, sp.infra, sp.services 
            FROM servicesPending sp
            JOIN hosts h ON sp.host_ip = h.ip AND (sp.infra = h.infra OR (sp.infra IS NULL AND h.infra IS NULL))
            WHERE $where";
    
    // Add ORDER BY clause for consistent results
    $sql .= " ORDER BY sp.host_name ASC, sp.host_ip ASC";
    
    // Add LIMIT clause for pagination
    $sql .= " LIMIT ?, ?";
    
    // Add pagination parameters
    $params[] = $offset;
    $params[] = $limit;
    $types .= "ii"; // integer types for offset and limit
    
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $services = [];
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $services[] = $row;
        }
    }
    
    $stmt->close();
    $conn->close();
    
    // Include pagination info in response
    $response = [
        'services' => $services,
        'total' => $totalServices,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => $totalPages
    ];
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Approve or reject all services at once
function approveOrRejectAllServices() {
    $action = $_GET['action'];
    
    if ($action !== 'approve_all' && $action !== 'reject_all') {
        echo "Invalid action. Use 'approve_all' or 'reject_all'.";
        return;
    }
    
    $conn = getDatabaseConnection();
    $isApprove = ($action === 'approve_all');
    
    // Build the WHERE clause
    $where = "h.muted = 0";
    $params = [];
    $types = "";
    
    if (isset($_GET['infra'])) {
        $infra = $_GET['infra'];
        $where .= " AND sp.infra = ?";
        $params[] = $infra;
        $types .= "s";
    }
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // First, get all the services matching the criteria
        $sql = "SELECT sp.id, sp.host_ip, sp.infra 
                FROM servicesPending sp
                JOIN hosts h ON sp.host_ip = h.ip AND (sp.infra = h.infra OR (sp.infra IS NULL AND h.infra IS NULL))
                WHERE $where";
        
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $serviceIds = [];
        $serviceData = [];
        
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $serviceIds[] = $row['id'];
                $serviceData[] = [
                    'id' => $row['id'],
                    'ip' => $row['host_ip'],
                    'infra' => $row['infra']
                ];
            }
        }
        
        $stmt->close();
        
        if (empty($serviceIds)) {
            $conn->commit();
            echo "No services found to " . ($isApprove ? "approve" : "reject");
            return;
        }
        
        // Process all services
        $processedCount = 0;
        
        foreach ($serviceData as $service) {
            $id = $service['id'];
            $ip = $service['ip'];
            $infra = $service['infra'];
            
            if ($isApprove) {
                // For approval: Set host's apmStatus to 'not-added'
                $updateHostSql = "UPDATE hosts SET apmStatus = 'not-added', muted = 0 WHERE ip = ? AND infra = ?";
                $updateStmt = $conn->prepare($updateHostSql);
                $updateStmt->bind_param('ss', $ip, $infra);
                
                if (!$updateStmt->execute()) {
                    throw new Exception("Error updating host status: " . $updateStmt->error);
                }
                
                $updateStmt->close();
                
                // Delete the service entry from servicesPending only for approval
                $deleteServiceSql = "DELETE FROM servicesPending WHERE id = ?";
                $deleteStmt = $conn->prepare($deleteServiceSql);
                $deleteStmt->bind_param('i', $id);
                
                if (!$deleteStmt->execute()) {
                    throw new Exception("Error deleting service entry: " . $deleteStmt->error);
                }
                
                $deleteStmt->close();
            } else {
                // For rejection: Set the host as muted
                $updateHostSql = "UPDATE hosts SET muted = 1 WHERE ip = ? AND infra = ?";
                $updateStmt = $conn->prepare($updateHostSql);
                $updateStmt->bind_param('ss', $ip, $infra);
                
                if (!$updateStmt->execute()) {
                    throw new Exception("Error setting host as muted: " . $updateStmt->error);
                }
                
                $updateStmt->close();
            }
            
            $processedCount++;
        }
        
        // Commit the transaction
        $conn->commit();
        echo ($isApprove ? "Approved" : "Rejected") . " $processedCount services successfully";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        echo $e->getMessage();
    }
    
    $conn->close();
}

// Route based on the request
if (isset($_GET['action'])) {
    if ($_GET['action'] === 'approve' || $_GET['action'] === 'reject') {
        approveService();
    } else if ($_GET['action'] === 'approve_all' || $_GET['action'] === 'reject_all') {
        approveOrRejectAllServices();
    } else {
        echo "Invalid action. Specify action=approve|reject|approve_all|reject_all";
    }
} else if (isset($_GET['list']) && $_GET['list'] === 'pending') {
    getServicesWaitingApproval();
} else {
    echo "Invalid request. Specify action=approve|reject|approve_all|reject_all or list=pending";
} 