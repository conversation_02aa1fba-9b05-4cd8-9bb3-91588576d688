<?php
header('Content-Type: text/plain; charset=utf-8');
include "loadenv.php";
// Database configuration
$dbHost = $_ENV["DB_SERVER"];
$dbUser = $_ENV["DB_USER"];
$dbPass =  $_ENV["DB_PASSWORD"];
$dbName = 'bubblemaps';

try {
    // Create database connection
    $pdo = new PDO(
        "mysql:host=$dbHost;dbname=$dbName;charset=utf8",
        $dbUser,
        $dbPass,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Get infra parameter from URL
    $infra = isset($_GET['infra']) ? $_GET['infra'] : null;
    
    if (!$infra) {
        http_response_code(400);
        echo "Error: infra parameter is required";
        exit;
    }

    // Prepare and execute query
    $stmt = $pdo->prepare("
        SELECT DISTINCT JSON_UNQUOTE(JSON_EXTRACT(hostgroup, '$[*]')) as hostgroup
        FROM hosts
        WHERE infra = :infra
        AND hostgroup IS NOT NULL
    ");
    $stmt->execute([':infra' => $infra]);

    // Store unique hostgroups
    $uniqueHostgroups = [];
    
    // Process results
    while ($row = $stmt->fetch()) {
        // hostgroup might be a JSON array, so decode it
        $hostgroups = json_decode($row['hostgroup']);
        
        if ($hostgroups) {
            // If it's an array, add each hostgroup individually
            if (is_array($hostgroups)) {
                foreach ($hostgroups as $hg) {
                    if (!empty($hg) && !in_array($hg, $uniqueHostgroups)) {
                        $uniqueHostgroups[] = $hg;
                    }
                }
            } 
            // If it's a single value
            elseif (!empty($hostgroups) && !in_array($hostgroups, $uniqueHostgroups)) {
                $uniqueHostgroups[] = $hostgroups;
            }
        }
    }

    // Output hostgroups, one per line
    if (empty($uniqueHostgroups)) {
        echo "No hostgroups found for infra: " . htmlspecialchars($infra);
    } else {
        echo implode("\n", $uniqueHostgroups);
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo "Database error: " . $e->getMessage();
} catch (Exception $e) {
    http_response_code(500);
    echo "Error: " . $e->getMessage();
}

// Close connection
$pdo = null;
?>