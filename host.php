<?php
include 'theme_loader.php'; // Include the theme loader
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Host Status</title>
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/host.css">
    <style>
        .os-icon {
            display: none;
        }
        .os-icon.loaded {
            display: inline-block;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <img class="blesk-logo" style="width: 25px;" src="../../images/blesk-favicon.svg" alt="Blesk">
            <h1 class="header-title">
                <?php
                $nickname = $_GET['nickname'];
                $hostip = $_GET['hostip'];
                $hostTitle = ($nickname === $hostip) ? $nickname : "{$nickname} ({$hostip})";
                echo $hostTitle;
                ?>
            </h1>
            <div class="header-actions">
                <div class="os-dropdown">
                    <img src="imgs/OS-Vendors/unknown-svgrepo-com.svg" class="os-icon" title="Operating System/Vendor" alt="Loading...">
                    <div class="os-menu">
                        <div class="os-item" data-os="windows">
                            <img src="imgs/OS-Vendors/windows-174-svgrepo-com.svg" alt="Windows">
                            Windows
                        </div>
                        <div class="os-item" data-os="linux">
                            <img src="imgs/OS-Vendors/linux-svgrepo-com.svg" alt="Linux">
                            Linux
                        </div>
                        <div class="os-item" data-os="mac">
                            <img src="imgs/OS-Vendors/apple-inc-svgrepo-com.svg" alt="macOS">
                            macOS
                        </div>
                        <div class="os-item" data-os="android">
                            <img src="imgs/OS-Vendors/android-svgrepo-com.svg" alt="Android">
                            Android
                        </div>
                        <div class="os-item" data-os="freebsd">
                            <img src="imgs/OS-Vendors/freebsd-svgrepo-com.svg" alt="FreeBSD">
                            FreeBSD
                        </div>
                        <div class="os-item" data-os="openbsd">
                            <img src="imgs/OS-Vendors/openbsd-svgrepo-com.svg" alt="OpenBSD">
                            OpenBSD
                        </div>
                        <div class="os-item" data-os="cisco">
                            <img src="imgs/OS-Vendors/cisco-svgrepo-com.svg" alt="Cisco">
                            Cisco
                        </div>
                        <div class="os-item" data-os="aruba">
                            <img src="imgs/OS-Vendors/Aruba_Networks_logo.svg" alt="Aruba Networks">
                            Aruba
                        </div>
                        <div class="os-item" data-os="ruckus">
                            <img src="imgs/OS-Vendors/ruckus-logo.png" alt="Ruckus">
                            Ruckus
                        </div>
                        <div class="os-item" data-os="apc">
                            <img src="imgs/OS-Vendors/APC-logo.svg" alt="APC">
                            APC
                        </div>
                        <div class="os-item" data-os="dell">
                            <img src="imgs/OS-Vendors/dell-2-logo-svgrepo-com.svg" alt="Dell">
                            Dell
                        </div>
                        <div class="os-item" data-os="fortinet">
                            <img src="imgs/OS-Vendors/fortinet-svgrepo-com.svg" alt="Fortinet">
                            Fortinet
                        </div>
                        <div class="os-item" data-os="hp">
                            <img src="imgs/OS-Vendors/hp-svgrepo-com.svg" alt="HP">
                            HP
                        </div>
                        <div class="os-item" data-os="vmware">
                            <img src="imgs/OS-Vendors/vmware-svgrepo-com.svg" alt="VMware">
                            VMware
                        </div>
                        <div class="os-item" data-os="canon">
                            <img src="imgs/OS-Vendors/Canon_Logo.svg" alt="Canon">
                            Canon
                        </div>
                        <div class="os-item" data-os="samsung">
                            <img src="imgs/OS-Vendors/samsung-svg.svg" alt="Samsung">
                            Samsung
                        </div>
                        <div class="os-item" data-os="lexmark">
                            <img src="imgs/OS-Vendors/Lexmark-Logo.svg" alt="Lexmark">
                            Lexmark
                        </div>
                        <div class="os-item" data-os="external">
                            <img src="imgs/OS-Vendors/internet-svgrepo-com.svg" alt="External Network">
                            External
                        </div>
                        <div class="os-item" data-os="unknown">
                            <img src="imgs/OS-Vendors/unknown-svgrepo-com.svg" alt="Unknown">
                            Unknown
                        </div>
                    </div>
                </div>
                <button id="selection-mode-button" class="selection-mode-button" title="Enable selection mode" onclick="toggleSelectionMode()">
                    <i class="fa fa-check-square-o"></i>
                </button>
                <button id="view-toggle-button" class="selection-mode-button" title="Switch to list view">
                    <i class="fa fa-list"></i>
                </button>
                <img src="imgs/icons/radar.svg" class="header-icons" title="Check host health" onclick="launchTroubleshooting('result')" style="width: 22px; height: 22px; filter: brightness(0) invert(1);">
                <i class="fa fa-refresh header-icons" title="Refresh" onclick="window.location.reload()"></i>
            </div>
        </div>
    </header>
    <div class="content-wrapper">
        <?php include 'hostApm.php' ?>
    </div>
    <script>
        const isExternal = urlParams.get('subnet') === 'External';
        const osDropdown = document.querySelector('.os-dropdown');
        const osIcon = osDropdown.querySelector('.os-icon');
        const osMenu = osDropdown.querySelector('.os-menu');
        const osItems = osDropdown.querySelectorAll('.os-item');

        function updateIcon(os, svg, alt) {
            osIcon.src = svg;
            osIcon.alt = alt;
            osIcon.setAttribute('data-os', os);
            osIcon.classList.add('loaded');
        }

        async function detectOS() {
            try {
                const ip = '<?php echo $_GET['hostip']; ?>';
                const response = await fetch(`detect_os.php?ip=${encodeURIComponent(ip)}${isExternal ? '&external=True' : ''}`);
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const data = await response.json();
                updateIcon(data.os, data.svg, data.alt);
            } catch (error) {
                console.error('Error detecting OS:', error);
                updateIcon('unknown', 'imgs/OS-Vendors/unknown-svgrepo-com.svg', 'Unknown');
            }
        }

        async function updateVendor(ip, vendor) {
            try {
                const formData = new FormData();
                formData.append('ip', ip);
                formData.append('vendor', vendor);

                const response = await fetch('update_vendor.php', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const data = await response.json();
                if (!data.success) {
                    console.error('Failed to update vendor:', data.message);
                }
            } catch (error) {
                console.error('Error updating vendor:', error);
            }
        }

        window.addEventListener('load', () => {
            detectOS();
        });

        osIcon.addEventListener('click', (e) => {
            e.stopPropagation();
            osMenu.classList.toggle('active');
        });

        document.addEventListener('click', (e) => {
            if (!osDropdown.contains(e.target)) {
                osMenu.classList.remove('active');
            }
        });

        osItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                const os = item.getAttribute('data-os');
                let svg, alt;

                switch (os) {
                    case 'windows':
                        svg = 'imgs/OS-Vendors/windows-174-svgrepo-com.svg';
                        alt = 'Windows';
                        break;
                    case 'linux':
                        svg = 'imgs/OS-Vendors/linux-svgrepo-com.svg';
                        alt = 'Linux';
                        break;
                    case 'mac':
                        svg = 'imgs/OS-Vendors/apple-inc-svgrepo-com.svg';
                        alt = 'macOS';
                        break;
                    case 'android':
                        svg = 'imgs/OS-Vendors/android-svgrepo-com.svg';
                        alt = 'Android';
                        break;
                    case 'freebsd':
                        svg = 'imgs/OS-Vendors/freebsd-svgrepo-com.svg';
                        alt = 'FreeBSD';
                        break;
                    case 'openbsd':
                        svg = 'imgs/OS-Vendors/openbsd-svgrepo-com.svg';
                        alt = 'OpenBSD';
                        break;
                    case 'cisco':
                        svg = 'imgs/OS-Vendors/cisco-svgrepo-com.svg';
                        alt = 'Cisco';
                        break;
                    case 'aruba':
                        svg = 'imgs/OS-Vendors/Aruba_Networks_logo.svg';
                        alt = 'Aruba Networks';
                        break;
                    case 'ruckus':
                        svg = 'imgs/OS-Vendors/ruckus-logo.png';
                        alt = 'Ruckus';
                        break;
                    case 'apc':
                        svg = 'imgs/OS-Vendors/APC-logo.svg';
                        alt = 'APC';
                        break;
                    case 'dell':
                        svg = 'imgs/OS-Vendors/dell-2-logo-svgrepo-com.svg';
                        alt = 'Dell';
                        break;
                    case 'fortinet':
                        svg = 'imgs/OS-Vendors/fortinet-svgrepo-com.svg';
                        alt = 'Fortinet';
                        break;
                    case 'hp':
                        svg = 'imgs/OS-Vendors/hp-svgrepo-com.svg';
                        alt = 'HP';
                        break;
                    case 'vmware':
                        svg = 'imgs/OS-Vendors/vmware-svgrepo-com.svg';
                        alt = 'VMware';
                        break;
                    case 'canon':
                        svg = 'imgs/OS-Vendors/Canon_Logo.svg';
                        alt = 'Canon';
                        break;
                    case 'samsung':
                        svg = 'imgs/OS-Vendors/samsung-svg.svg';
                        alt = 'Samsung';
                        break;
                    case 'lexmark':
                        svg = 'imgs/OS-Vendors/Lexmark-Logo.svg';
                        alt = 'Lexmark';
                        break;
                    case 'external':
                        svg = 'imgs/OS-Vendors/internet-svgrepo-com.svg';
                        alt = 'External Network';
                        break;
                    case 'unknown':
                    default:
                        svg = 'imgs/OS-Vendors/unknown-svgrepo-com.svg';
                        alt = 'Unknown';
                        break;
                }

                updateIcon(os, svg, alt);
                const ip = '<?php echo $_GET['hostip']; ?>';
                updateVendor(ip, os);
                osMenu.classList.remove('active');
            });
        });
    </script>
</body>
</html>