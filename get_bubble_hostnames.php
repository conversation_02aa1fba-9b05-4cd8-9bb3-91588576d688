<?php
include "loadenv.php";

// Database credentials
$servername = $_ENV["DB_SERVER"];
$username   = $_ENV["DB_USER"];
$password   = $_ENV["DB_PASSWORD"];
$dbname     = "bubblemaps";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    http_response_code(500);
    die("Connection failed: " . $conn->connect_error);
}

$sql = "SELECT ip, hostname FROM hosts WHERE blacklist = 0";
$result = $conn->query($sql);

$hostMap = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $ip        = $row['ip'];
        $bubbleName = $row['hostname'];
        if ($ip && $bubbleName) {
            $hostMap[$ip] = $bubbleName;
        }
    }
}

header('Content-Type: application/json');
echo json_encode($hostMap);

$conn->close();
?> 